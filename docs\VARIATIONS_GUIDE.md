# Product Variations Guide

## Overview

The product variations system allows you to create different options for your products (like Size, Color, Material, etc.) with individual pricing, SKUs, and stock levels.

## Features Implemented

### ✅ Admin Interface

- **Add Variations in Product Form**: Toggle "Add Variations" section when creating/editing products
- **Variations Management Modal**: Dedicated modal for managing existing product variations
- **CSV Import/Export**: Full support for variations in bulk operations

### ✅ Frontend Display

- **Variation Selector**: Interactive buttons for customers to select options
- **Dynamic Pricing**: Price updates automatically based on selected variations
- **Stock Management**: Individual stock tracking per variation combination

## How to Use

### Creating Products with Variations

1. **Go to Admin Products Page**: `/admin/products`
2. **Click "Add Product"** or edit existing product
3. **Fill Basic Information**: Name, description, price, etc.
4. **Enable Variations**: Click "Add Variations" to show the variations section
5. **Add Variation Options**:
   - **Name**: Type of variation (e.g., "Size", "Color", "Material")
   - **Value**: Specific option (e.g., "Large", "Red", "Cotton")
   - **Price Adjustment**: Additional cost (can be negative for discounts)

### Example Variations Setup

**Product**: Premium T-Shirt (Base Price: ₹299)

**Size Variations**:

- Small: +₹0 (₹299 total)
- Medium: +₹0 (₹299 total)
- Large: +₹50 (₹349 total)
- XL: +₹100 (₹399 total)

**Color Variations**:

- White: +₹0
- Black: +₹0
- Premium Blue: +₹25

**Final Combinations**:

- Large + Premium Blue = ₹299 + ₹50 + ₹25 = ₹374
- Small + White = ₹299 + ₹0 + ₹0 = ₹299

### Managing Existing Variations

1. **Go to Products List**: `/admin/products`
2. **Click Variations Button** (⚙️ icon) next to any product
3. **Add/Edit/Delete**: Use the variations management modal
4. **Reorder**: Variations are grouped by name automatically

### CSV Import/Export with Variations

#### Export Format

```csv
Name,Slug,Description,Price,Variations
"Premium T-Shirt","premium-t-shirt","High quality cotton t-shirt",299,"[{""name"":""Size"",""value"":""Large"",""price"":50,""sku"":""TSHIRT-L"",""quantity"":10}]"
```

#### Import Format

- **Variations Column**: JSON array of variation objects
- **Example**: `[{"name":"Size","value":"Medium","price":0},{"name":"Color","value":"Blue","price":5}]`

### Frontend Customer Experience

1. **Product Page**: Customers see variation options as buttons
2. **Selection**: Click buttons to select Size, Color, etc.
3. **Price Update**: Total price updates automatically
4. **Stock Display**: Shows availability for selected combination
5. **Add to Cart**: Selected variations are included in cart

## Technical Details

### Database Schema

```sql
ProductVariant {
  id: String (Primary Key)
  name: String (e.g., "Size", "Color")
  value: String (e.g., "Large", "Red")
  price: Float (Price adjustment)
  productId: String (Foreign Key)
}
```

### API Endpoints

- `GET /api/products/[id]/variations` - Get all variations
- `POST /api/products/[id]/variations` - Create variation
- `PUT /api/products/[id]/variations/[variationId]` - Update variation
- `DELETE /api/products/[id]/variations/[variationId]` - Delete variation

### Frontend Components

- `ProductVariationSelector` - Customer-facing variation selection
- `VariationsManagementModal` - Admin variation management
- `ProductTabs` - Includes variations in product details

## Best Practices

### Naming Conventions

- **Use Clear Names**: "Size", "Color", "Material" instead of "Option1", "Option2"
- **Consistent Values**: "Small/Medium/Large" not "S/Med/L"
- **Logical Pricing**: Use positive values for upgrades, negative for discounts

### Stock Management

- **Track Individually**: Each variation should have its own stock count
- **Update Regularly**: Keep stock levels current to avoid overselling
- **Use SKUs**: Assign unique SKUs for inventory tracking

### Customer Experience

- **Logical Order**: Present variations in logical order (Size before Color)
- **Clear Pricing**: Show price adjustments clearly (+₹50, -₹25)
- **Stock Indicators**: Show when variations are out of stock

## Troubleshooting

### Common Issues

1. **Variations Not Showing**: Check if variations are added to the product
2. **Price Not Updating**: Ensure price adjustments are numeric values
3. **Import Errors**: Validate JSON format in variations column
4. **Stock Issues**: Verify individual variation quantities

### Support

For technical support or feature requests, contact the development team.
