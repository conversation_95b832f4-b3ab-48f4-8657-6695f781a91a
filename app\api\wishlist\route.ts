import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../lib/auth";
import { wishlistNotifications } from "../../lib/notification-helpers";
import connectDB from "../../lib/mongoose";
import { WishlistItem, Product } from "../../lib/models";
import { Types } from "mongoose";

// GET /api/wishlist - Get user's wishlist items
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await connectDB();

    const userId =
      Types.ObjectId.isValid(session.user.id)
        ? new Types.ObjectId(session.user.id)
        : session.user.id;

    const wishlistItems = await WishlistItem.find({ userId })
      .populate([{ path: "product", select: "name slug price shortDescription images rating reviews" }])
      .sort({ createdAt: -1 })
      .lean();

    const transformedItems = (wishlistItems || []).map((item: any) => ({
      _id: String(item.product?._id ?? ""),
      name: item.product?.name ?? "",
      slug: item.product?.slug ?? "",
      price: item.product?.price ?? 0,
      shortDescription: item.product?.shortDescription ?? "",
      image:
        (Array.isArray(item.product?.images) && item.product.images.length > 0
          ? item.product.images[0]?.url
          : undefined) || "/placeholder-product.jpg",
      rating: item.product?.rating ?? 0,
      reviews: item.product?.reviews ?? 0,
      wishlistItemId: String(item._id),
    }));

    return NextResponse.json({ items: transformedItems });
  } catch (error) {
    console.error("Error fetching wishlist:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

// POST /api/wishlist - Add item to wishlist
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { productId } = await request.json();

    if (!productId) {
      return NextResponse.json(
        { error: "Product ID is required" },
        { status: 400 },
      );
    }

    await connectDB();

    const userIdObj =
      Types.ObjectId.isValid(session.user.id)
        ? new Types.ObjectId(session.user.id)
        : session.user.id;

    const productIdObj =
      Types.ObjectId.isValid(productId) ? new Types.ObjectId(productId) : productId;

    // Check if product exists
    const product = await Product.findById(productIdObj)
      .select("_id name price")
      .lean<{ _id: Types.ObjectId; name?: string; price?: number } | null>();

    if (!product) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 });
    }

    // Check if item is already in wishlist (unique compound)
    const existingItem = await WishlistItem.findOne({
      userId: userIdObj,
      productId: productIdObj,
    })
      .select("_id")
      .lean();

    if (existingItem) {
      return NextResponse.json(
        { error: "Item already in wishlist" },
        { status: 400 },
      );
    }

    // Add to wishlist
    const wishlistItemDoc = await WishlistItem.create({
      userId: userIdObj,
      productId: productIdObj,
    });

    // Send wishlist added notification
    try {
      await wishlistNotifications.itemAdded(String(userIdObj), {
        productId: String(product._id),
        productName: product.name ?? "",
        price: product.price ?? undefined,
        currency: "INR",
      });
    } catch (notificationError) {
      console.error(
        "Failed to send wishlist added notification:",
        notificationError,
      );
      // Don't fail the wishlist addition if notification fails
    }

    const wishlistItem = wishlistItemDoc.toObject();
    return NextResponse.json({
      success: true,
      item: {
        _id: String(wishlistItem._id),
        userId: String(wishlistItem.userId),
        productId: String(wishlistItem.productId),
      },
    });
  } catch (error) {
    console.error("Error adding to wishlist:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

// DELETE /api/wishlist - Remove item from wishlist
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const productId = searchParams.get("productId");

    if (!productId) {
      return NextResponse.json(
        { error: "Product ID is required" },
        { status: 400 },
      );
    }

    await connectDB();

    const userIdObj =
      Types.ObjectId.isValid(session.user.id)
        ? new Types.ObjectId(session.user.id)
        : session.user.id;

    const productIdObj =
      Types.ObjectId.isValid(productId) ? new Types.ObjectId(productId) : productId;

    // Find the specific wishlist item to be deleted
    const wishlistItem = await WishlistItem.findOne({
      userId: userIdObj,
      productId: productIdObj,
    })
      .populate([{ path: "product", select: "name" }])
      .lean();

    if (!wishlistItem) {
      return NextResponse.json(
        { error: "Item not found in wishlist" },
        { status: 404 },
      );
    }

    // Remove from wishlist
    await WishlistItem.deleteOne({ _id: (wishlistItem as any)._id });

    // Send wishlist removed notification
    try {
      if ((wishlistItem as any).product) {
        await wishlistNotifications.itemRemoved(String(userIdObj), {
          productId: String(
            (wishlistItem as any).product._id ??
            (wishlistItem as any).product.id ??
            productIdObj
          ),
          productName: (wishlistItem as any).product.name ?? "",
        });
      }
    } catch (notificationError) {
      console.error(
        "Failed to send wishlist removed notification:",
        notificationError,
      );
      // Don't fail the wishlist removal if notification fails
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error removing from wishlist:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
