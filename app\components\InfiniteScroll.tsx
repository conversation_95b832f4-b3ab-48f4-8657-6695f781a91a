"use client";

import React, { useEffect, useRef, useCallback } from "react";
import { Loader2 } from "lucide-react";

interface InfiniteScrollProps {
  hasMore: boolean;
  loading: boolean;
  onLoadMore: () => void;
  threshold?: number;
  children: React.ReactNode;
}

const InfiniteScroll: React.FC<InfiniteScrollProps> = ({
  hasMore,
  loading,
  onLoadMore,
  threshold = 100,
  children,
}) => {
  const observerRef = useRef<HTMLDivElement>(null);
  const loadingRef = useRef(false);

  const handleObserver = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      const [target] = entries;
      if (target.isIntersecting && hasMore && !loading && !loadingRef.current) {
        loadingRef.current = true;
        onLoadMore();
        setTimeout(() => {
          loadingRef.current = false;
        }, 1000);
      }
    },
    [hasMore, loading, onLoadMore],
  );

  useEffect(() => {
    const element = observerRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(handleObserver, {
      threshold: 0.1,
      rootMargin: `${threshold}px`,
    });

    observer.observe(element);

    return () => {
      if (element) {
        observer.unobserve(element);
      }
    };
  }, [handleObserver, threshold]);

  return (
    <div>
      {children}

      {/* Loading trigger element */}
      <div ref={observerRef} className="w-full py-8">
        {loading && (
          <div className="flex items-center justify-center">
            <div className="flex items-center space-x-2 text-gray-600">
              <Loader2 className="w-5 h-5 animate-spin" />
              <span className="text-sm font-medium">
                Loading more products...
              </span>
            </div>
          </div>
        )}

        {!hasMore && !loading && (
          <div className="text-center py-8">
            <div className="inline-flex items-center px-4 py-2 bg-gray-100 rounded-full">
              <span className="text-sm text-gray-600 font-medium">
                You've reached the end of our collection
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default InfiniteScroll;
