"use client";

import React, { useState, useEffect } from "react";
import { Plus, Edit, Trash2, X, Save, ArrowUp, ArrowDown } from "lucide-react";

interface ProductFAQ {
  id: string;
  question: string;
  answer: string;
  position: number;
  isActive: boolean;
}

interface Product {
  id: string;
  name: string;
}

interface FAQManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product;
}

const FAQManagementModal: React.FC<FAQManagementModalProps> = ({
  isOpen,
  onClose,
  product,
}) => {
  const [faqs, setFaqs] = useState<ProductFAQ[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingFAQ, setEditingFAQ] = useState<ProductFAQ | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState({
    question: "",
    answer: "",
  });

  useEffect(() => {
    if (isOpen && product) {
      fetchFAQs();
    }
  }, [isOpen, product]);

  const fetchFAQs = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/products/${product.id}/faqs`);
      const result = await response.json();

      if (result.success) {
        setFaqs(result.data);
      }
    } catch (error) {
      console.error("Error fetching FAQs:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddFAQ = async () => {
    if (!formData.question.trim() || !formData.answer.trim()) {
      alert("Please fill in both question and answer");
      return;
    }

    try {
      const response = await fetch(`/api/products/${product.id}/faqs`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        setFaqs((prev) => [...prev, result.data]);
        setFormData({ question: "", answer: "" });
        setShowAddForm(false);
      } else {
        alert("Failed to add FAQ");
      }
    } catch (error) {
      console.error("Error adding FAQ:", error);
      alert("Failed to add FAQ");
    }
  };

  const handleUpdateFAQ = async (faq: ProductFAQ) => {
    try {
      const response = await fetch(
        `/api/products/${product.id}/faqs/${faq.id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(faq),
        },
      );

      const result = await response.json();

      if (result.success) {
        setFaqs((prev) => prev.map((f) => (f.id === faq.id ? result.data : f)));
        setEditingFAQ(null);
      } else {
        alert("Failed to update FAQ");
      }
    } catch (error) {
      console.error("Error updating FAQ:", error);
      alert("Failed to update FAQ");
    }
  };

  const handleDeleteFAQ = async (faqId: string) => {
    if (!confirm("Are you sure you want to delete this FAQ?")) return;

    try {
      const response = await fetch(
        `/api/products/${product.id}/faqs/${faqId}`,
        {
          method: "DELETE",
        },
      );

      const result = await response.json();

      if (result.success) {
        setFaqs((prev) => prev.filter((f) => f.id !== faqId));
      } else {
        alert("Failed to delete FAQ");
      }
    } catch (error) {
      console.error("Error deleting FAQ:", error);
      alert("Failed to delete FAQ");
    }
  };

  const movePosition = async (faq: ProductFAQ, direction: "up" | "down") => {
    const currentIndex = faqs.findIndex((f) => f.id === faq.id);
    const newIndex = direction === "up" ? currentIndex - 1 : currentIndex + 1;

    if (newIndex < 0 || newIndex >= faqs.length) return;

    const newFaqs = [...faqs];
    [newFaqs[currentIndex], newFaqs[newIndex]] = [
      newFaqs[newIndex],
      newFaqs[currentIndex],
    ];

    // Update positions
    newFaqs[currentIndex].position = currentIndex;
    newFaqs[newIndex].position = newIndex;

    setFaqs(newFaqs);

    // Update in database
    await handleUpdateFAQ(newFaqs[newIndex]);
    await handleUpdateFAQ(newFaqs[currentIndex]);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            Manage FAQs - {product.name}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Add FAQ Button */}
          <div className="mb-6">
            <button
              onClick={() => setShowAddForm(true)}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
            >
              <Plus className="w-4 h-4" />
              <span>Add FAQ</span>
            </button>
          </div>

          {/* Add FAQ Form */}
          {showAddForm && (
            <div className="mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50">
              <h3 className="text-lg font-medium mb-4">Add New FAQ</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Question
                  </label>
                  <input
                    type="text"
                    value={formData.question}
                    onChange={(e) =>
                      setFormData({ ...formData, question: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                    placeholder="Enter the question"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Answer
                  </label>
                  <textarea
                    value={formData.answer}
                    onChange={(e) =>
                      setFormData({ ...formData, answer: e.target.value })
                    }
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                    placeholder="Enter the answer"
                  />
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={handleAddFAQ}
                    className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
                  >
                    <Save className="w-4 h-4" />
                    <span>Save FAQ</span>
                  </button>
                  <button
                    onClick={() => {
                      setShowAddForm(false);
                      setFormData({ question: "", answer: "" });
                    }}
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* FAQs List */}
          {loading ? (
            <div className="text-center py-8">
              <div className="text-gray-500">Loading FAQs...</div>
            </div>
          ) : faqs.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-500">
                No FAQs found. Add your first FAQ above.
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {faqs.map((faq, index) => (
                <FAQItem
                  key={faq.id}
                  faq={faq}
                  index={index}
                  totalCount={faqs.length}
                  isEditing={editingFAQ?.id === faq.id}
                  onEdit={setEditingFAQ}
                  onUpdate={handleUpdateFAQ}
                  onDelete={handleDeleteFAQ}
                  onMove={movePosition}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// FAQ Item Component
interface FAQItemProps {
  faq: ProductFAQ;
  index: number;
  totalCount: number;
  isEditing: boolean;
  onEdit: (faq: ProductFAQ | null) => void;
  onUpdate: (faq: ProductFAQ) => void;
  onDelete: (faqId: string) => void;
  onMove: (faq: ProductFAQ, direction: "up" | "down") => void;
}

const FAQItem: React.FC<FAQItemProps> = ({
  faq,
  index,
  totalCount,
  isEditing,
  onEdit,
  onUpdate,
  onDelete,
  onMove,
}) => {
  const [editData, setEditData] = useState({
    question: faq.question,
    answer: faq.answer,
  });

  const handleSave = () => {
    if (!editData.question.trim() || !editData.answer.trim()) {
      alert("Please fill in both question and answer");
      return;
    }

    onUpdate({
      ...faq,
      question: editData.question,
      answer: editData.answer,
    });
  };

  const handleCancel = () => {
    setEditData({
      question: faq.question,
      answer: faq.answer,
    });
    onEdit(null);
  };

  return (
    <div className="border border-gray-200 rounded-lg p-4 bg-white">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-2">
          <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded text-sm font-medium">
            #{index + 1}
          </span>
          <div className="flex space-x-1">
            <button
              onClick={() => onMove(faq, "up")}
              disabled={index === 0}
              className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Move up"
            >
              <ArrowUp className="w-4 h-4" />
            </button>
            <button
              onClick={() => onMove(faq, "down")}
              disabled={index === totalCount - 1}
              className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Move down"
            >
              <ArrowDown className="w-4 h-4" />
            </button>
          </div>
        </div>
        <div className="flex space-x-2">
          {isEditing ? (
            <>
              <button
                onClick={handleSave}
                className="p-1 text-green-600 hover:text-green-700"
                title="Save"
              >
                <Save className="w-4 h-4" />
              </button>
              <button
                onClick={handleCancel}
                className="p-1 text-gray-400 hover:text-gray-600"
                title="Cancel"
              >
                <X className="w-4 h-4" />
              </button>
            </>
          ) : (
            <>
              <button
                onClick={() => onEdit(faq)}
                className="p-1 text-blue-600 hover:text-blue-700"
                title="Edit"
              >
                <Edit className="w-4 h-4" />
              </button>
              <button
                onClick={() => onDelete(faq.id)}
                className="p-1 text-red-600 hover:text-red-700"
                title="Delete"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </>
          )}
        </div>
      </div>

      {isEditing ? (
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Question
            </label>
            <input
              type="text"
              value={editData.question}
              onChange={(e) =>
                setEditData({ ...editData, question: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Answer
            </label>
            <textarea
              value={editData.answer}
              onChange={(e) =>
                setEditData({ ...editData, answer: e.target.value })
              }
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      ) : (
        <div>
          <h4 className="font-medium text-gray-900 mb-2">{faq.question}</h4>
          <p className="text-gray-600 whitespace-pre-wrap">{faq.answer}</p>
        </div>
      )}
    </div>
  );
};

export default FAQManagementModal;
