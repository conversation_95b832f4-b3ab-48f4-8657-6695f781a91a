import { getServerSession } from "next-auth";
import { authOptions } from "@/app/lib/auth";
import { redirect } from "next/navigation";

export default async function ProfileLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getServerSession(authOptions);

  // If no session, redirect to login
  if (!session) {
    redirect("/login");
  }

  // If user is admin, redirect to admin panel
  if (session.user?.role === "ADMIN") {
    redirect("/admin");
  }

  return <>{children}</>;
}
