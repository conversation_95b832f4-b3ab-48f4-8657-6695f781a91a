"use client";

import React, { createContext, useContext, useState, useEffect } from "react";

interface FlashSaleSettings {
  showFlashSale: boolean;
  flashSaleEndDate: string | null;
  flashSalePercentage: number | null;
  flashSaleTitle: string | null;
  flashSaleSubtitle: string | null;
  flashSaleBackgroundColor: string | null;
}

interface FlashSaleContextType {
  flashSaleSettings: FlashSaleSettings | null;
  loading: boolean;
  isHydrated: boolean;
  refreshSettings: () => Promise<void>;
}

const FlashSaleContext = createContext<FlashSaleContextType | undefined>(
  undefined,
);

export function FlashSaleProvider({ children }: { children: React.ReactNode }) {
  const [flashSaleSettings, setFlashSaleSettings] =
    useState<FlashSaleSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [isHydrated, setIsHydrated] = useState(false);

  const fetchFlashSaleSettings = async () => {
    try {
      const response = await fetch("/api/homepage-settings");
      const data = await response.json();

      if (data.success && data.data.settings) {
        const settings = data.data.settings;
        const flashSaleData = {
          showFlashSale: settings.showFlashSale,
          flashSaleEndDate: settings.flashSaleEndDate,
          flashSalePercentage: settings.flashSalePercentage,
          flashSaleTitle: settings.flashSaleTitle,
          flashSaleSubtitle: settings.flashSaleSubtitle,
          flashSaleBackgroundColor: settings.flashSaleBackgroundColor,
        };

        setFlashSaleSettings(flashSaleData);

        // Store in localStorage for cart to access
        if (typeof window !== "undefined") {
          localStorage.setItem(
            "flashSaleSettings",
            JSON.stringify(flashSaleData),
          );
          // Dispatch custom event to notify cart of changes
          window.dispatchEvent(new Event("flashSaleSettingsUpdated"));
        }
      }
    } catch (error) {
      console.error("Error fetching flash sale settings:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFlashSaleSettings().then(() => {
      setIsHydrated(true);
    });
  }, []);

  const refreshSettings = async () => {
    setLoading(true);
    await fetchFlashSaleSettings();
  };

  return (
    <FlashSaleContext.Provider
      value={{ flashSaleSettings, loading, isHydrated, refreshSettings }}
    >
      {children}
    </FlashSaleContext.Provider>
  );
}

export function useFlashSale() {
  const context = useContext(FlashSaleContext);
  if (context === undefined) {
    throw new Error("useFlashSale must be used within a FlashSaleProvider");
  }
  return context;
}
