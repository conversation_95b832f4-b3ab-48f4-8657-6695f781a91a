import nodemailer from "nodemailer";
import { logger } from "./logger";

// Brevo API configuration
const BREVO_API_URL = "https://api.brevo.com/v3/smtp/email";

interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  from?: string;
}

// Send email using Brevo API
async function sendWithBrevoAPI(options: EmailOptions): Promise<void> {
  const brevoApiKey = process.env.BREVO_API_KEY;

  if (!brevoApiKey) {
    throw new Error("BREVO_API_KEY is not configured");
  }

  const payload = {
    sender: {
      name: process.env.FROM_NAME || "Herbalicious",
      email: process.env.FROM_EMAIL || "<EMAIL>",
    },
    to: [
      {
        email: options.to,
      },
    ],
    subject: options.subject,
    htmlContent: options.html,
  };

  const response = await fetch(BREVO_API_URL, {
    method: "POST",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
      "api-key": brevo<PERSON><PERSON><PERSON><PERSON>,
    },
    body: JSON.stringify(payload),
  });

  if (!response.ok) {
    const errorData = await response.text();
    logger.emailError(
      options.to,
      options.subject,
      new Error(`Brevo API error: ${response.status} - ${errorData}`),
    );
    throw new Error(`Failed to send email via Brevo API: ${response.status}`);
  }

  logger.emailSent(options.to, options.subject, "brevo-api");
}

// Create SMTP transporter (fallback)
const createTransporter = () => {
  // Try Brevo SMTP first
  if (process.env.SMTP_HOST) {
    return nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || "587"),
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });
  }

  // Development fallback
  if (process.env.NODE_ENV === "development") {
    return nodemailer.createTransport({
      host: "smtp.ethereal.email",
      port: 587,
      secure: false,
      auth: {
        user: "<EMAIL>",
        pass: "ethereal.pass",
      },
    });
  }

  throw new Error("No email configuration found");
};

// Send email using SMTP (fallback)
async function sendWithSMTP(options: EmailOptions): Promise<void> {
  const transporter = createTransporter();

  const mailOptions = {
    from: options.from || process.env.FROM_EMAIL || "<EMAIL>",
    to: options.to,
    subject: options.subject,
    html: options.html,
  };

  await transporter.sendMail(mailOptions);
  logger.emailSent(options.to, options.subject, "smtp");
}

// Main email sending function
export async function sendEmail(options: EmailOptions): Promise<void> {
  try {
    // Try Brevo API first if configured
    if (process.env.BREVO_API_KEY) {
      await sendWithBrevoAPI(options);
      return;
    }

    // Fallback to SMTP
    await sendWithSMTP(options);
  } catch (error) {
    logger.emailError(options.to, options.subject, error as Error);
    throw error;
  }
}

export async function sendPasswordResetEmail(
  email: string,
  resetToken: string,
) {
  const resetUrl = `${process.env.NEXTAUTH_URL}/reset-password?token=${resetToken}`;

  const html = `
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #2d5a27;">Password Reset Request</h2>
      <p>You have requested to reset your password for your Herbalicious account.</p>
      <p>Click the button below to reset your password:</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${resetUrl}" 
           style="background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
          Reset Password
        </a>
      </div>
      <p>If the button doesn't work, copy and paste this link into your browser:</p>
      <p style="word-break: break-all; color: #666;">${resetUrl}</p>
      <p style="color: #666; font-size: 14px; margin-top: 30px;">
        This link will expire in 1 hour. If you didn't request this password reset, please ignore this email.
      </p>
      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #999; font-size: 12px;">
        This email was sent from Herbalicious. Please do not reply to this email.
      </p>
    </div>
  `;

  await sendEmail({
    to: email,
    subject: "Password Reset Request - Herbalicious",
    html,
  });
}

export async function sendWelcomeEmail(email: string, name: string) {
  const html = `
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #2d5a27;">Welcome to Herbalicious, ${name}!</h2>
      <p>Thank you for joining our community of natural health enthusiasts.</p>
      <p>We're excited to have you on board and look forward to helping you discover the best herbal products for your wellness journey.</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${process.env.NEXTAUTH_URL}/shop" 
           style="background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
          Start Shopping
        </a>
      </div>
      <p>If you have any questions, feel free to contact our support team.</p>
      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #999; font-size: 12px;">
        This email was sent from Herbalicious. Please do not reply to this email.
      </p>
    </div>
  `;

  await sendEmail({
    to: email,
    subject: "Welcome to Herbalicious!",
    html,
  });
}

export async function sendOrderConfirmationEmail(
  email: string,
  orderDetails: {
    orderId: string;
    items: Array<{ name: string; quantity: number; price: number }>;
    total: number;
    shippingAddress: string;
  },
) {
  const itemsHtml = orderDetails.items
    .map(
      (item) => `
    <tr>
      <td style="padding: 8px; border-bottom: 1px solid #eee;">${item.name}</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: center;">${item.quantity}</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: right;">₹${item.price.toFixed(2)}</td>
    </tr>
  `,
    )
    .join("");

  const html = `
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #2d5a27;">Order Confirmation</h2>
      <p>Thank you for your order! Here are the details:</p>
      
      <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin: 0 0 10px 0;">Order #${orderDetails.orderId}</h3>
      </div>
      
      <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <thead>
          <tr style="background-color: #f5f5f5;">
            <th style="padding: 12px; text-align: left; border-bottom: 2px solid #ddd;">Item</th>
            <th style="padding: 12px; text-align: center; border-bottom: 2px solid #ddd;">Qty</th>
            <th style="padding: 12px; text-align: right; border-bottom: 2px solid #ddd;">Price</th>
          </tr>
        </thead>
        <tbody>
          ${itemsHtml}
        </tbody>
        <tfoot>
          <tr>
            <td colspan="2" style="padding: 12px; font-weight: bold; border-top: 2px solid #ddd;">Total:</td>
            <td style="padding: 12px; font-weight: bold; text-align: right; border-top: 2px solid #ddd;">₹${orderDetails.total.toFixed(2)}</td>
          </tr>
        </tfoot>
      </table>
      
      <div style="margin: 20px 0;">
        <h4>Shipping Address:</h4>
        <p style="background-color: #f9f9f9; padding: 10px; border-radius: 5px;">${orderDetails.shippingAddress}</p>
      </div>
      
      <p>We'll send you another email when your order ships.</p>
      
      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #999; font-size: 12px;">
        This email was sent from Herbalicious. Please do not reply to this email.
      </p>
    </div>
  `;

  await sendEmail({
    to: email,
    subject: `Order Confirmation - ${orderDetails.orderId}`,
    html,
  });
}

export async function sendOrderStatusUpdateEmail(
  email: string,
  orderData: {
    orderNumber: string;
    status: string;
  },
) {
  const statusMessages: Record<string, string> = {
    CONFIRMED: "Your order has been confirmed and is being prepared.",
    PROCESSING: "Your order is currently being processed.",
    SHIPPED: "Your order has been shipped and is on its way to you.",
    DELIVERED: "Your order has been delivered successfully.",
    CANCELLED: "Your order has been cancelled.",
    REFUNDED: "Your order has been refunded.",
  };

  const statusMessage =
    statusMessages[orderData.status] || "Your order status has been updated.";

  const html = `
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #2d5a27;">Order Status Update</h2>
      <p>Your order <strong>#${orderData.orderNumber}</strong> status has been updated.</p>
      
      <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin: 0 0 10px 0; color: #2d5a27;">Current Status: ${orderData.status}</h3>
        <p style="margin: 0;">${statusMessage}</p>
      </div>
      
      
      <p>Thank you for shopping with Herbalicious!</p>
      
      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #999; font-size: 12px;">
        This email was sent from Herbalicious. Please do not reply to this email.
      </p>
    </div>
  `;

  await sendEmail({
    to: email,
    subject: `Order Update - ${orderData.orderNumber}`,
    html,
  });
}

// Test email function for development
export async function sendTestEmail(email: string) {
  const html = `
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #2d5a27;">Email Configuration Test</h2>
      <p>This is a test email to verify that your email configuration is working correctly.</p>
      <p>If you received this email, your Brevo integration is working properly!</p>
      <p style="color: #666; font-size: 14px; margin-top: 30px;">
        Sent at: ${new Date().toISOString()}
      </p>
      <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #999; font-size: 12px;">
        This email was sent from Herbalicious for testing purposes.
      </p>
    </div>
  `;

  await sendEmail({
    to: email,
    subject: "Email Configuration Test - Herbalicious",
    html,
  });
}
