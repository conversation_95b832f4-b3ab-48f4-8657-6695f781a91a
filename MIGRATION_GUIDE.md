# Database Migration Guide

## ✅ Prisma → Mongoose Migration: COMPLETE

The Prisma to Mongoose migration has been **successfully completed**! The application now uses Mongoose with MongoDB instead of Prisma.

### Migration Status:
- ✅ All database models converted to Mongoose schemas
- ✅ All API routes migrated from Prisma to Mongoose
- ✅ Authentication system working with MongoDB
- ✅ Payment processing integrated with MongoDB
- ✅ Admin panel fully functional
- ✅ Code quality verified (<PERSON><PERSON><PERSON>, <PERSON>tti<PERSON>, TypeScript)

### Required Environment Variables:
```env
MONGODB_URI=mongodb+srv://username:<EMAIL>/database
MONGODB_DB=your-database-name
# ... (see .env.example for complete list)
```

### Breaking Changes:
- **Database**: Now uses MongoDB instead of PostgreSQL/Prisma
- **Environment**: `DATABASE_URL` → `MONGODB_URI` and `MONGODB_DB`
- **Dependencies**: Removed `@prisma/client` and `prisma`, added `mongoose`

---

# Xata to MongoDB Migration Guide

This guide will help you migrate your data from Xata to MongoDB safely and efficiently.

## Prerequisites

1. ✅ MongoDB connection string is configured in `.env`
2. ✅ Xata credentials are working in your current setup
3. ✅ All required npm packages are installed
4. ✅ Both databases are accessible

## Migration Process

### Step 1: Backup Your Xata Data (IMPORTANT!)

Before starting the migration, create a complete backup of your Xata data:

```bash
npm run backup:xata
```

This will:

- Create a `backups/` directory
- Export all tables to JSON files with timestamps
- Generate a backup summary

### Step 2: Test Database Connections

First, let's make sure both databases are accessible:

```bash
# Test Prisma connection to Xata
npx prisma db pull

# Test MongoDB connection
node -e "const { MongoClient } = require('mongodb'); require('dotenv').config(); const client = new MongoClient(process.env.MONGODB_URI); client.connect().then(() => { console.log('MongoDB connected!'); client.close(); });"
```

### Step 3: Run the Migration

Choose one of the migration options:

#### Option A: Simple Migration (Single Table Test)

```bash
npm run migrate:simple
```

#### Option B: Comprehensive Migration (All Tables)

```bash
npm run migrate:full
```

**Recommended:** Start with a simple migration to test the process, then run the comprehensive migration.

### Step 4: Verify Migration Success

After migration, verify that all data was transferred correctly:

```bash
npm run verify:migration
```

This will:

- Compare record counts between Xata and MongoDB
- Check data structure consistency
- Provide a detailed verification report

## Migration Features

### Safe Migration

- ✅ **No data deletion**: The migration scripts will NOT delete any existing MongoDB data
- ✅ **Backup first**: Always creates backups before migration
- ✅ **Error handling**: Gracefully handles duplicate keys and connection issues
- ✅ **Verification**: Includes verification scripts to ensure data integrity

### What Gets Migrated

The following tables/collections will be migrated:

- `users` - User accounts and profiles
- `userPreferences` - User settings and preferences
- `categories` - Product categories
- `products` - Product catalog
- `productImages` - Product image references
- `productVariants` - Product variations (size, color, etc.)
- `productFAQs` - Product frequently asked questions
- `orders` - Customer orders
- `orderItems` - Individual order line items
- `orderAddresses` - Shipping addresses for orders
- `addresses` - User saved addresses
- `reviews` - Product reviews and ratings
- `notifications` - User notifications
- `coupons` - Discount coupons
- `couponUsages` - Coupon usage tracking
- `productCategories` - Product-category relationships
- `wishlistItems` - User wishlist items
- `homepageSettings` - Homepage configuration
- `newsletterSubscribers` - Email subscribers
- `notificationTemplates` - Notification templates
- `settings` - Application settings
- `testimonials` - Customer testimonials
- `enquiries` - Customer inquiries

## Data Transformation

The migration handles:

- ✅ Date field conversion
- ✅ JSON field preservation
- ✅ Relationship ID mapping
- ✅ MongoDB document structure optimization

## Troubleshooting

### Common Issues

1. **Connection Timeout**
   - Check your internet connection
   - Verify database credentials
   - Ensure both databases are accessible

2. **Duplicate Key Errors**
   - The script handles duplicates gracefully
   - Existing data in MongoDB will be preserved
   - Check verification report for details

3. **Large Dataset Migration**
   - For large datasets, consider running migrations in smaller batches
   - Monitor system resources during migration

### If Migration Fails

1. Check the error messages in the console
2. Verify your database connections
3. Ensure you have sufficient permissions
4. Run the verification script to see which tables succeeded

### Rolling Back

If you need to start over:

1. Your Xata data remains untouched
2. Use your backup files in the `backups/` directory
3. You can safely delete MongoDB collections and re-run migration

## Post-Migration Steps

1. **Update Application Configuration**
   - Update your application to use MongoDB instead of Xata
   - Update connection strings and database clients
   - Test all application functionality

2. **Performance Optimization**
   - Add appropriate MongoDB indexes
   - Optimize queries for MongoDB document structure
   - Monitor performance metrics

3. **Final Verification**
   - Run comprehensive application tests
   - Verify all features work with MongoDB
   - Check data integrity in production scenarios

## Need Help?

If you encounter issues during migration:

1. Check the generated backup files
2. Review the migration logs
3. Run the verification script for detailed reporting
4. Consider migrating tables individually for debugging

## Important Notes

- 🚨 **NEVER run migration scripts on production without testing first**
- 🔒 **Always backup your data before migration**
- 📊 **Use the verification scripts to ensure data integrity**
- 🔄 **The migration is additive - it won't delete existing MongoDB data**

Good luck with your migration! 🚀
