import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../lib/auth";
import connectDB from "../../../lib/mongoose";
import { Newsletter } from "../../../lib/models";

// GET /api/newsletter/export - Export newsletter subscribers as CSV (Admin only)
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is admin
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    const { searchParams } = new URL(request.url);
    const isActive = searchParams.get("active");
    const source = searchParams.get("source");
    const format = searchParams.get("format") || "csv";

    await connectDB();
    
    // Build query
    const query: any = {};
    if (isActive !== null) {
      query.isActive = isActive === "true";
    }
    if (source) {
      query.source = source;
    }

    // Get all subscribers matching criteria
    const subscribers = await Newsletter.find(query)
      .sort({ createdAt: -1 })
      .lean();

    if (format === "csv") {
      // Generate CSV content
      const csvHeaders = [
        "Email",
        "Name",
        "Status",
        "Source",
        "Subscribed At",
        "Unsubscribed At",
      ];
      const csvRows = subscribers.map((subscriber) => [
        subscriber.email,
        subscriber.name || "",
        subscriber.isActive ? "Active" : "Inactive",
        subscriber.source || "",
        subscriber.createdAt ? new Date(subscriber.createdAt).toISOString() : "",
        subscriber.unsubscribedAt ? new Date(subscriber.unsubscribedAt).toISOString() : "",
      ]);

      const csvContent = [
        csvHeaders.join(","),
        ...csvRows.map((row) => row.map((field) => `"${field}"`).join(",")),
      ].join("\n");

      // Return CSV file
      return new NextResponse(csvContent, {
        status: 200,
        headers: {
          "Content-Type": "text/csv",
          "Content-Disposition": `attachment; filename="newsletter-subscribers-${new Date().toISOString().split("T")[0]}.csv"`,
        },
      });
    } else if (format === "json") {
      // Return JSON format
      return NextResponse.json({
        success: true,
        data: {
          subscribers,
          exportedAt: new Date().toISOString(),
          total: subscribers.length,
        },
      });
    } else {
      return NextResponse.json(
        { success: false, error: "Unsupported format. Use csv or json." },
        { status: 400 },
      );
    }
  } catch (error) {
    console.error("Error exporting newsletter subscribers:", error);
    return NextResponse.json(
      { success: false, error: "Failed to export newsletter subscribers" },
      { status: 500 },
    );
  }
}
