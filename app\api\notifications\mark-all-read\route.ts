import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../lib/auth";
import { logger } from "../../../lib/logger";

/**
 * POST /api/notifications/mark-all-read
 * Mark all notifications as read for the current user
 *
 * NOTE: This endpoint is temporarily disabled during Prisma to MongoDB migration
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    logger.info("Mark all as read API temporarily disabled during migration");

    // Return success with 0 count during migration
    return NextResponse.json({
      success: true,
      message: "Mark all as read temporarily disabled during migration",
      count: 0,
    });
  } catch (error) {
    logger.error("Failed to mark all notifications as read", error as Error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
