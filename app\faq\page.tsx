import React from "react";

const FAQPage = () => {
  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-2xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <h1 className="text-3xl font-extrabold text-gray-900">
          Frequently Asked Questions
        </h1>
        <div className="mt-6 prose prose-indigo text-gray-500">
          <div className="py-6">
            <h2 className="text-xl font-semibold">
              What is your return policy?
            </h2>
            <p className="mt-2">
              We have a 30-day return policy, which means you have 30 days after
              receiving your item to request a return. To be eligible for a
              return, your item must be in the same condition that you received
              it, unworn or unused, with tags, and in its original packaging.
              You’ll also need the receipt or proof of purchase.
            </p>
          </div>

          <div className="py-6">
            <h2 className="text-xl font-semibold">
              How long does shipping take?
            </h2>
            <p className="mt-2">
              Orders are typically processed within 1-2 business days. Shipping
              times may vary based on your location, but most orders arrive
              within 5-7 business days.
            </p>
          </div>

          <div className="py-6">
            <h2 className="text-xl font-semibold">
              Do you ship internationally?
            </h2>
            <p className="mt-2">
              Currently, we only ship within the country. We are working on
              expanding our shipping options to include international
              destinations in the near future.
            </p>
          </div>

          {/* Add more questions as needed */}
        </div>
      </div>
    </div>
  );
};

export default FAQPage;
