import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/app/lib/mongoose";
import {
  Product,
  Category,
  ProductImage,
  ProductVariant,
  Review,
} from "@/app/lib/models";
// Inline minimal asyncHandler since '@/app/lib/async-handler' does not exist
const asyncHandler = <T extends (...args: any[]) => Promise<Response>>(handler: T) =>
  (async (...args: Parameters<T>) => {
    try {
      return await handler(...args);
    } catch (err) {
      console.error(err);
      return NextResponse.json({ success: false, error: "Internal Server Error" }, { status: 500 });
    }
  }) as T;

// GET /api/products/optimized - Optimized paginated listing with lightweight related data
export const GET = asyncHandler(async (request: NextRequest) => {
  await connectDB();

  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get("page") || "1", 10);
  const limit = parseInt(searchParams.get("limit") || "10", 10);
  const category = searchParams.get("category");
  const search = searchParams.get("search");
  const sort = searchParams.get("sort") || "newest";
  const priceMin = searchParams.get("priceMin");
  const priceMax = searchParams.get("priceMax");

  const skip = (page - 1) * limit;

  // Build Mongoose where clause
  const where: any = { isActive: true };

  // Category filtering (typed lean to avoid array|object unions)
  type LeanCategoryId = { _id: any };
  if (category && category !== "all") {
    const categoryDoc = await Category.findOne({ slug: category })
      .select("_id")
      .lean<LeanCategoryId | null>();
    if (categoryDoc?._id) {
      where.categoryId = String(categoryDoc._id);
    }
  }

  // Search filtering
  if (search) {
    where.$or = [
      { name: { $regex: search, $options: "i" } },
      { shortDescription: { $regex: search, $options: "i" } },
    ];
  }

  // Price range filtering
  if (priceMin || priceMax) {
    where.price = {};
    if (priceMin) where.price.$gte = parseFloat(String(priceMin));
    if (priceMax) where.price.$lte = parseFloat(String(priceMax));
  }

  // Sorting
  let sortQuery: any = { createdAt: -1 };
  switch (sort) {
    case "name_asc":
      sortQuery = { name: 1 };
      break;
    case "name_desc":
      sortQuery = { name: -1 };
      break;
    case "price_asc":
      sortQuery = { price: 1 };
      break;
    case "price_desc":
      sortQuery = { price: -1 };
      break;
    case "newest":
      sortQuery = { createdAt: -1 };
      break;
    case "oldest":
      sortQuery = { createdAt: 1 };
      break;
  }

  // Query products and total in parallel
  type LeanProduct = {
    _id: any;
    name: string;
    slug: string;
    shortDescription?: string;
    price?: number;
    comparePrice?: number | null;
    isFeatured?: boolean;
    createdAt?: Date | string;
    categoryId?: string | null;
  };
  type LeanImage = { _id: any; productId: string; url: string; alt?: string | null; position?: number };
  type LeanVariant = { _id: any; productId: string; name: string; value: string; price: number | null; pricingMode: string };
  type LeanCategoryMin = { _id: any; name: string; slug: string };

  const [products, total] = await Promise.all([
    Product.find(where).sort(sortQuery).skip(skip).limit(limit).lean<LeanProduct[]>(),
    Product.countDocuments(where),
  ]);

  // Populate related minimal data for each product
  const transformedProducts = await Promise.all(
    products.map(async (product) => {
      const [cat, images, variants, reviewCount] = await Promise.all([
        product.categoryId
          ? Category.findById(product.categoryId).select("name slug").lean<LeanCategoryMin | null>()
          : null,
        ProductImage.find({ productId: String(product._id) })
          .sort({ position: 1 })
          .limit(1)
          .lean<LeanImage[]>(),
        ProductVariant.find({ productId: String(product._id) })
          .sort({ price: 1 })
          .limit(3)
          .lean<LeanVariant[]>(),
        Review.countDocuments({ productId: String(product._id) }),
      ]);

      return {
        id: String(product._id),
        name: product.name,
        slug: product.slug,
        shortDescription: product.shortDescription,
        price: product.price,
        comparePrice: product.comparePrice,
        isFeatured: product.isFeatured,
        createdAt: product.createdAt,
        category: cat,
        categories: cat ? [cat] : [],
        image: images[0] || null,
        reviewCount,
        variants,
      };
    }),
  );

  return NextResponse.json({
    success: true,
    data: transformedProducts,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
    },
  });
});
