import { NextRequest, NextResponse } from "next/server";
import connectDB from "../../../../lib/mongoose";
import { Product } from "../../../../lib/models";

// GET /api/products/[id]/faqs - Get all FAQs for a product
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    await connectDB();

    // ProductFAQ is stored as a subdocument collection on Product schema (embedded or referenced via a dedicated model).
    // Since ProductFAQ model is not exported, derive from Product where FAQs are stored.
    // Attempt 1: If FAQs are embedded on product.faqs
    const productWithFaqs = await Product.findById(params.id)
      .select("faqs")
      .lean<{ faqs?: Array<{ question: string; answer: string; position?: number; isActive?: boolean }> } | null>();

    const faqs = (productWithFaqs?.faqs || [])
      .filter((f) => f.isActive !== false)
      .sort((a, b) => (a.position ?? 0) - (b.position ?? 0));

    return NextResponse.json({
      success: true,
      data: faqs,
    });
  } catch (error) {
    console.error("Error fetching FAQs:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch FAQs" },
      { status: 500 },
    );
  }
}

// POST /api/products/[id]/faqs - Create a new FAQ for a product
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    await connectDB();

    const body = await request.json();
    const { question, answer, position } = body;

    if (!question || !answer) {
      return NextResponse.json(
        { success: false, error: "Question and answer are required" },
        { status: 400 },
      );
    }

    // Get the next position if not provided
    let faqPosition = position;
    if (faqPosition === undefined) {
      // Compute next position from existing product.faqs
      const current = await Product.findById(params.id).select("faqs").lean<{ faqs?: Array<{ position?: number }> } | null>();
      const maxPos = (current?.faqs || []).reduce((m, f) => Math.max(m, f.position ?? -1), -1);
      faqPosition = maxPos + 1;
    }

    // Push into product.faqs array
    const updateRes = await Product.findByIdAndUpdate(
      params.id,
      {
        $push: {
          faqs: {
            question,
            answer,
            position: faqPosition,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        },
      },
      { new: true, projection: { faqs: 1 } }
    ).lean<{ faqs?: Array<any> } | null>();

    const faq = (updateRes?.faqs || []).find((f) => f.position === faqPosition) || {
      question,
      answer,
      position: faqPosition,
      isActive: true,
    };

    return NextResponse.json({
      success: true,
      data: faq,
      message: "FAQ created successfully",
    });
  } catch (error) {
    console.error("Error creating FAQ:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create FAQ" },
      { status: 500 },
    );
  }
}
