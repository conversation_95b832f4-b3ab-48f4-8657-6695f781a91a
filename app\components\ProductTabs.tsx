"use client";

import React, { useState } from "react";
import ProductFAQs from "./ProductFAQs";

interface Product {
  id: string;
  name: string;
  description?: string;
  shortDescription?: string;
}

interface ProductTabsProps {
  product: Product;
}

const ProductTabs: React.FC<ProductTabsProps> = ({ product }) => {
  const [activeTab, setActiveTab] = useState("description");

  const tabs = [
    {
      id: "description",
      label: "Description",
      content: (
        <div className="prose max-w-none">
          {product.description ? (
            <div className="text-gray-700 leading-relaxed whitespace-pre-wrap">
              {product.description}
            </div>
          ) : (
            <div className="text-gray-500 italic">
              No detailed description available for this product.
            </div>
          )}
        </div>
      ),
    },
    {
      id: "ingredients",
      label: "Ingredients",
      content: (
        <div className="prose max-w-none">
          <div className="text-gray-700 leading-relaxed">
            <p className="mb-4">
              <strong>Natural Ingredients:</strong> This product is formulated
              with carefully selected natural ingredients.
            </p>
            <p className="text-sm text-gray-600">
              For a complete list of ingredients, please refer to the product
              packaging or contact our customer service team.
            </p>
          </div>
        </div>
      ),
    },
    {
      id: "usage",
      label: "How to Use",
      content: (
        <div className="prose max-w-none">
          <div className="text-gray-700 leading-relaxed">
            <h4 className="font-semibold mb-3">Usage Instructions:</h4>
            <ol className="list-decimal list-inside space-y-2">
              <li>Clean the application area thoroughly</li>
              <li>Apply a small amount of product</li>
              <li>Gently massage until absorbed</li>
              <li>Use as directed on packaging</li>
            </ol>
            <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-800">
                <strong>Note:</strong> For specific usage instructions, please
                refer to the product packaging or consult with a healthcare
                professional.
              </p>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: "faqs",
      label: "FAQs",
      content: <ProductFAQs productId={product.id} />,
    },
  ];

  return (
    <div className="w-full">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? "border-green-500 text-green-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {tabs.find((tab) => tab.id === activeTab)?.content}
      </div>
    </div>
  );
};

export default ProductTabs;
