# Admin Panel Design Guide

## Brand Colors

- **Primary Green**: `green-600` (#059669)
- **Dark Green**: `green-700` (#047857)
- **Light Green**: `green-500` (#10b981)
- **Green Background**: `green-50` (#f0fdf4)
- **Green Border**: `green-200` (#bbf7d0)
- **Success/Active**: `green-600`
- **Error/Danger**: `red-600` (#dc2626)
- **Warning**: `orange-600` (#ea580c)
- **Info**: `gray-600` (#4b5563)
- **Background**: `gray-50` (#f9fafb)
- **Card Background**: `white`
- **Text Primary**: `gray-900` (#111827)
- **Text Secondary**: `gray-600` (#4b5563)
- **Border**: `gray-200` (#e5e7eb)

## Layout Structure

### Page Container

```tsx
<div className="min-h-screen bg-gray-50">
  <div className="p-6">{/* Page content */}</div>
</div>
```

### Page Header

```tsx
<div className="mb-8">
  <h1 className="text-3xl font-bold text-gray-900">{Title}</h1>
  <p className="text-gray-600 mt-2">{Description}</p>
</div>
```

### Card Component

```tsx
<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
  {/* Card content */}
</div>
```

## Component Standards

### Buttons

#### Primary Button (Actions)

```tsx
<button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium">
  Action
</button>
```

#### Secondary Button

```tsx
<button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium">
  Cancel
</button>
```

#### Icon Button

```tsx
<button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
  <Icon className="w-5 h-5" />
</button>
```

### Form Elements

#### Input Field

```tsx
<input
  type="text"
  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
  placeholder="Enter text..."
/>
```

#### Select Dropdown

```tsx
<select className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
  <option>Option 1</option>
</select>
```

#### Textarea

```tsx
<textarea
  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
  rows={4}
/>
```

### Tables

#### Table Container

```tsx
<div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
  <div className="overflow-x-auto">
    <table className="w-full">{/* Table content */}</table>
  </div>
</div>
```

#### Table Header

```tsx
<thead className="bg-gray-50 border-b border-gray-200">
  <tr>
    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
      Column Name
    </th>
  </tr>
</thead>
```

#### Table Body

```tsx
<tbody className="bg-white divide-y divide-gray-200">
  <tr className="hover:bg-gray-50">
    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
      Cell content
    </td>
  </tr>
</tbody>
```

### Status Badges

#### Success/Active

```tsx
<span className="px-3 py-1 text-xs font-medium rounded-full bg-green-100 text-green-700 border border-green-200">
  Active
</span>
```

#### Warning

```tsx
<span className="px-3 py-1 text-xs font-medium rounded-full bg-orange-100 text-orange-700 border border-orange-200">
  Warning
</span>
```

#### Error/Inactive

```tsx
<span className="px-3 py-1 text-xs font-medium rounded-full bg-red-100 text-red-700 border border-red-200">
  Inactive
</span>
```

#### Info/Neutral

```tsx
<span className="px-3 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-700 border border-gray-200">
  Info
</span>
```

### Modals

```tsx
<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
  <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
    <div className="p-6 border-b border-gray-200">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Modal Title</h2>
        <button className="text-gray-500 hover:text-gray-700">
          <X className="w-6 h-6" />
        </button>
      </div>
    </div>
    <div className="p-6">{/* Modal content */}</div>
  </div>
</div>
```

### Stats Cards

```tsx
<div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
  <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm text-gray-600">Label</p>
        <p className="text-2xl font-bold text-gray-900">Value</p>
      </div>
      <Icon className="w-8 h-8 text-green-600" />
    </div>
  </div>
</div>
```

### Loading State

```tsx
<div className="flex items-center justify-center py-12">
  <Loader2 className="w-8 h-8 animate-spin text-green-600" />
</div>
```

### Empty State

```tsx
<div className="text-center py-12">
  <Icon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
  <h3 className="text-lg font-medium text-gray-900 mb-2">No items found</h3>
  <p className="text-gray-600">Get started by creating your first item.</p>
</div>
```

### Error State

```tsx
<div className="bg-red-50 border border-red-200 rounded-xl p-4">
  <div className="flex items-center space-x-3">
    <AlertCircle className="w-5 h-5 text-red-600" />
    <p className="text-red-800">Error message here</p>
  </div>
</div>
```

### Success Message

```tsx
<div className="bg-green-50 border border-green-200 rounded-xl p-4">
  <div className="flex items-center space-x-3">
    <CheckCircle className="w-5 h-5 text-green-600" />
    <p className="text-green-800">Success message here</p>
  </div>
</div>
```

## Typography

- **Page Title**: `text-3xl font-bold text-gray-900`
- **Section Title**: `text-xl font-semibold text-gray-900`
- **Card Title**: `text-lg font-semibold text-gray-900`
- **Body Text**: `text-sm text-gray-600`
- **Small Text**: `text-xs text-gray-500`
- **Link**: `text-green-600 hover:text-green-700`

## Spacing

- **Page Padding**: `p-6`
- **Card Padding**: `p-6`
- **Section Margin**: `mb-8`
- **Element Spacing**: `space-y-4` or `space-x-4`
- **Grid Gap**: `gap-6`

## Icons

- Use Lucide React icons consistently
- Icon size in buttons: `w-5 h-5`
- Standalone icon size: `w-6 h-6`
- Large decorative icons: `w-8 h-8` or `w-12 h-12`

## Responsive Design

- Use responsive grid: `grid-cols-1 md:grid-cols-2 lg:grid-cols-4`
- Stack elements on mobile: `flex flex-col sm:flex-row`
- Hide on mobile when needed: `hidden sm:block`
- Responsive text: `text-2xl sm:text-3xl`

## Consistency Rules

1. Always use rounded corners: `rounded-lg` for buttons/inputs, `rounded-xl` for cards
2. Use consistent shadows: `shadow-sm` for cards
3. Maintain border style: `border border-gray-200`
4. Use transition for hover effects: `transition-colors`
5. Keep consistent padding/margin scales
6. Use green-600 as the primary action color
7. Use gray scale for neutral elements
8. Only use other colors (red, orange) for specific states (error, warning)
