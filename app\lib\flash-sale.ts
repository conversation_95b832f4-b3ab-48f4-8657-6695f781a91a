interface FlashSaleSettings {
  showFlashSale: boolean;
  flashSaleEndDate: string | null;
  flashSalePercentage: number | null;
}

export function isFlashSaleActive(settings: FlashSaleSettings | null): boolean {
  if (!settings || !settings.showFlashSale) {
    return false;
  }

  if (!settings.flashSaleEndDate) {
    return true; // If no end date, flash sale is always active when enabled
  }

  const now = new Date();
  const endDate = new Date(settings.flashSaleEndDate);

  return now < endDate;
}

export function calculateFlashSalePrice(
  originalPrice: number,
  discountPercentage: number,
): number {
  const discount = (originalPrice * discountPercentage) / 100;
  return Math.round(originalPrice - discount);
}

export function getFlashSalePrice(
  originalPrice: number,
  settings: FlashSaleSettings | null,
): {
  isOnSale: boolean;
  salePrice: number;
  originalPrice: number;
  discountPercentage: number;
} {
  const isOnSale = isFlashSaleActive(settings);
  const discountPercentage = settings?.flashSalePercentage || 0;

  return {
    isOnSale,
    salePrice: isOnSale
      ? calculateFlashSalePrice(originalPrice, discountPercentage)
      : originalPrice,
    originalPrice,
    discountPercentage: isOnSale ? discountPercentage : 0,
  };
}
