import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../lib/auth";
import connectDB from "../../lib/mongoose";
import { Newsletter } from "../../lib/models";

// POST /api/newsletter - Subscribe to newsletter
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, name, whatsapp, source = "homepage" } = body;

    if (!email) {
      return NextResponse.json(
        { success: false, error: "Email is required" },
        { status: 400 },
      );
    }

    await connectDB();
    
    // Check if email is already subscribed
    const existingSubscriber = await Newsletter.findOne({ email }).lean();

    if (existingSubscriber) {
      if ((existingSubscriber as any).isActive) {
        return NextResponse.json(
          { success: false, error: "Email is already subscribed" },
          { status: 409 },
        );
      } else {
        // Reactivate subscription
        const updatedSubscriber = await Newsletter.findOneAndUpdate(
          { email },
          {
            $set: {
              isActive: true,
              name: name || (existingSubscriber as any).name,
              whatsapp: whatsapp || (existingSubscriber as any).whatsapp,
              source,
              subscribedAt: new Date(),
              unsubscribedAt: null,
            }
          },
          { new: true }
        ).lean();

        return NextResponse.json({
          success: true,
          data: updatedSubscriber,
          message: "Successfully resubscribed to newsletter",
        });
      }
    }

    // Create new subscription
    const subscriber = await Newsletter.create({
      email,
      name,
      whatsapp,
      source,
      isActive: true,
    });
    
    const subscriberData = subscriber.toObject();

    return NextResponse.json({
      success: true,
      data: subscriber.toObject ? subscriber.toObject() : subscriber,
      message: "Successfully subscribed to newsletter",
    });
  } catch (error) {
    console.error("Error subscribing to newsletter:", error);
    return NextResponse.json(
      { success: false, error: "Failed to subscribe to newsletter" },
      { status: 500 },
    );
  }
}

// GET /api/newsletter - Get newsletter subscribers (Admin only)
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is admin
    if (!session || (session.user as any).role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");
    const isActive = searchParams.get("active");
    const source = searchParams.get("source");

    const skip = (page - 1) * limit;

    await connectDB();
    
    // Build query
    const query: any = {};
    if (isActive !== null) {
      query.isActive = isActive === "true";
    }
    if (source) {
      query.source = source;
    }

    // Get subscribers with pagination
    const [subscribers, total] = await Promise.all([
      Newsletter.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Newsletter.countDocuments(query),
    ]);

    // Get statistics
    const activeCount = await Newsletter.countDocuments({ isActive: true });
    const inactiveCount = await Newsletter.countDocuments({ isActive: false });

    return NextResponse.json({
      success: true,
      data: {
        subscribers,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
        stats: {
          total,
          active: activeCount,
          inactive: inactiveCount,
        },
      },
    });
  } catch (error) {
    console.error("Error fetching newsletter subscribers:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch newsletter subscribers" },
      { status: 500 },
    );
  }
}

// DELETE /api/newsletter - Unsubscribe from newsletter
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get("email");

    if (!email) {
      return NextResponse.json(
        { success: false, error: "Email is required" },
        { status: 400 },
      );
    }

    await connectDB();
    
    const subscriber = await Newsletter.findOneAndUpdate(
      { email },
      { 
        $set: {
          isActive: false,
          unsubscribedAt: new Date(),
        }
      },
      { new: true }
    ).lean();

    return NextResponse.json({
      success: true,
      data: subscriber,
      message: "Successfully unsubscribed from newsletter",
    });
  } catch (error) {
    console.error("Error unsubscribing from newsletter:", error);
    return NextResponse.json(
      { success: false, error: "Failed to unsubscribe from newsletter" },
      { status: 500 },
    );
  }
}
