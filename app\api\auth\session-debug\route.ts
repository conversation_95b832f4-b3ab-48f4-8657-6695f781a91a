import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../lib/auth";
import { getToken } from "next-auth/jwt";
import { User } from "@/app/lib/models";
import connectDB from "../../../lib/mongoose";

interface ExtendedSessionUser {
  id?: string;
  email?: string | null;
  name?: string | null;
  role?: string;
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    });

    if (!session?.user) {
      return NextResponse.json({
        authenticated: false,
        message: "No session found",
        token: token
          ? {
              sub: token.sub,
              email: token.email,
              role: token.role,
            }
          : null,
      });
    }

    // Check if user exists in database by email and by ID
    let dbUserByEmail = null;
    let dbUserById = null;

    await connectDB();

    if (session.user.email) {
      dbUserByEmail = await User.findOne(
        { email: session.user.email },
        "id email name role createdAt",
      );
    }

    if (session.user.id) {
      try {
        dbUserById = await User.findById(
          session.user.id,
          "id email name role createdAt",
        );
      } catch (error) {
        // ID might be invalid format - silently continue
      }
    }

    return NextResponse.json({
      authenticated: true,
      session: {
        user: {
          id: session.user.id,
          email: session.user.email,
          name: session.user.name,
          role: (session.user as ExtendedSessionUser).role,
        },
      },
      token: token
        ? {
            sub: token.sub,
            email: token.email,
            role: token.role,
          }
        : null,
      databaseLookups: {
        byEmail: dbUserByEmail,
        byId: dbUserById,
      },
      validation: {
        hasSessionId: !!session.user.id,
        hasSessionEmail: !!session.user.email,
        userExistsByEmail: !!dbUserByEmail,
        userExistsById: !!dbUserById,
        idsMatch: dbUserByEmail?.id === session.user.id,
        emailsMatch: dbUserByEmail?.email === session.user.email,
      },
    });
  } catch (error) {
    console.error("Session debug error:", error);
    return NextResponse.json(
      { error: "Failed to check session", details: error },
      { status: 500 },
    );
  }
}
