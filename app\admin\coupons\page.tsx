"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Tag,
  X,
  Loader2,
  AlertCircle,
} from "lucide-react";
import { Coupon, CouponCreateInput } from "../../types";

export default function CouponManagement() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [coupons, setCoupons] = useState<Coupon[]>([]);
  const [loading, setLoading] = useState(true);
  const [initialized, setInitialized] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingCoupon, setEditingCoupon] = useState<Coupon | null>(null);
  const [products, setProducts] = useState<
    Array<{ id: string; name: string; price: number }>
  >([]);
  const [categories, setCategories] = useState<
    Array<{ id: string; name: string }>
  >([]);
  const [formData, setFormData] = useState<CouponCreateInput>({
    code: "",
    name: "",
    description: "",
    type: "STORE_WIDE",
    discountType: "PERCENTAGE",
    discountValue: 0,
    minimumAmount: undefined,
    maximumDiscount: undefined,
    usageLimit: undefined,
    userUsageLimit: undefined,
    isActive: true,
    isStackable: false,
    showInModule: false,
    validFrom: new Date().toISOString().split("T")[0],
    validUntil: undefined,
    applicableProducts: [],
    applicableCategories: [],
    excludedProducts: [],
    excludedCategories: [],
    customerSegments: [],
    buyQuantity: 1,
    getQuantity: 1,
    buyProducts: [],
    buyCategories: [],
    getProducts: [],
    getCategories: [],
    maxApplications: 1,
    discountApplication: "FREE",
    getDiscountValue: 0,
  });

  const fetchCoupons = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/coupons?limit=100");
      if (response.ok) {
        const data = await response.json();
        setCoupons(data.coupons);
      }
    } catch (error) {
      console.error("Error fetching coupons:", error);
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchProducts = useCallback(async () => {
    try {
      const response = await fetch("/api/products?limit=100");
      if (response.ok) {
        const result = await response.json();
        console.log("Products API response:", result);
        // The API returns data in result.data
        const productsList = result.data || [];
        console.log("Products list:", productsList);
        setProducts(productsList);
      } else {
        console.error("Failed to fetch products:", response.status);
      }
    } catch (error) {
      console.error("Error fetching products:", error);
    }
  }, []);

  const fetchCategories = useCallback(async () => {
    try {
      const response = await fetch("/api/categories");
      if (response.ok) {
        const result = await response.json();
        console.log("Categories API response:", result);
        // Check if the API returns data in result.data or result.categories
        const categoriesList = result.data || result.categories || [];
        console.log("Categories list:", categoriesList);
        setCategories(categoriesList);
      } else {
        console.error("Failed to fetch categories:", response.status);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  }, []);

  useEffect(() => {
    if (status === "loading" || initialized) return;

    if (!session || (session.user as any)?.role !== "ADMIN") {
      router.push("/");
      return;
    }

    setInitialized(true);
    fetchCoupons();
    fetchProducts();
    fetchCategories();
  }, [
    session,
    status,
    initialized,
    fetchCoupons,
    fetchProducts,
    fetchCategories,
    router,
  ]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const url = editingCoupon
        ? `/api/coupons/${editingCoupon.id}`
        : "/api/coupons";
      const method = editingCoupon ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        await fetchCoupons();
        resetForm();
        setShowCreateModal(false);
        setEditingCoupon(null);
      } else {
        const error = await response.json();
        alert(error.error || "Failed to save coupon");
      }
    } catch (error) {
      console.error("Error saving coupon:", error);
      alert("Failed to save coupon");
    }
  };

  const handleDelete = async (couponId: string) => {
    if (!confirm("Are you sure you want to delete this coupon?")) return;

    try {
      const response = await fetch(`/api/coupons/${couponId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        await fetchCoupons();
      } else {
        alert("Failed to delete coupon");
      }
    } catch (error) {
      console.error("Error deleting coupon:", error);
      alert("Failed to delete coupon");
    }
  };

  const toggleCouponStatus = async (coupon: Coupon) => {
    try {
      const response = await fetch(`/api/coupons/${coupon.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ ...coupon, isActive: !coupon.isActive }),
      });

      if (response.ok) {
        await fetchCoupons();
      }
    } catch (error) {
      console.error("Error updating coupon status:", error);
    }
  };

  const resetForm = () => {
    setFormData({
      code: "",
      name: "",
      description: "",
      type: "STORE_WIDE",
      discountType: "PERCENTAGE",
      discountValue: 0,
      minimumAmount: undefined,
      maximumDiscount: undefined,
      usageLimit: undefined,
      userUsageLimit: undefined,
      isActive: true,
      isStackable: false,
      showInModule: false,
      validFrom: new Date().toISOString().split("T")[0],
      validUntil: undefined,
      applicableProducts: [],
      applicableCategories: [],
      excludedProducts: [],
      excludedCategories: [],
      customerSegments: [],
      buyQuantity: 1,
      getQuantity: 1,
      buyProducts: [],
      buyCategories: [],
      getProducts: [],
      getCategories: [],
      maxApplications: 1,
      discountApplication: "FREE",
      getDiscountValue: 0,
    });
  };

  const startEdit = (coupon: Coupon) => {
    setEditingCoupon(coupon);
    setFormData({
      code: coupon.code,
      name: coupon.name,
      description: coupon.description || "",
      type: coupon.type,
      discountType: coupon.discountType,
      discountValue: coupon.discountValue,
      minimumAmount: coupon.minimumAmount || undefined,
      maximumDiscount: coupon.maximumDiscount || undefined,
      usageLimit: coupon.usageLimit || undefined,
      userUsageLimit: coupon.userUsageLimit || undefined,
      isActive: coupon.isActive,
      isStackable: coupon.isStackable,
      showInModule: coupon.showInModule,
      validFrom: coupon.validFrom.split("T")[0],
      validUntil: coupon.validUntil
        ? coupon.validUntil.split("T")[0]
        : undefined,
      applicableProducts: coupon.applicableProducts,
      applicableCategories: coupon.applicableCategories,
      excludedProducts: coupon.excludedProducts,
      excludedCategories: coupon.excludedCategories,
      customerSegments: coupon.customerSegments,
      buyQuantity: coupon.buyQuantity || 1,
      getQuantity: coupon.getQuantity || 1,
      buyProducts: coupon.buyProducts || [],
      buyCategories: coupon.buyCategories || [],
      getProducts: coupon.getProducts || [],
      getCategories: coupon.getCategories || [],
      maxApplications: coupon.maxApplications || 1,
      discountApplication: coupon.discountApplication || "FREE",
      getDiscountValue: coupon.getDiscountValue || 0,
    });
    setShowCreateModal(true);
  };

  const getDiscountDisplay = (coupon: Coupon) => {
    if (coupon.discountType === "BUY_X_GET_Y") {
      const discountText =
        coupon.discountApplication === "FREE"
          ? "FREE"
          : coupon.discountApplication === "PERCENTAGE"
            ? `${coupon.getDiscountValue}% OFF`
            : `₹${coupon.getDiscountValue} OFF`;
      return `Buy ${coupon.buyQuantity} Get ${coupon.getQuantity} ${discountText}`;
    }

    switch (coupon.discountType) {
      case "PERCENTAGE":
        return `${coupon.discountValue}% OFF`;
      case "FIXED_AMOUNT":
        return `₹${coupon.discountValue} OFF`;
      case "FREE_SHIPPING":
        return "FREE SHIPPING";
      default:
        return "DISCOUNT";
    }
  };

  const getStatusColor = (coupon: Coupon) => {
    if (!coupon.isActive) return "bg-gray-100 text-gray-700 border-gray-200";

    const now = new Date();
    const validUntil = coupon.validUntil ? new Date(coupon.validUntil) : null;

    if (validUntil && validUntil < now)
      return "bg-red-100 text-red-700 border-red-200";
    if (
      validUntil &&
      validUntil.getTime() - now.getTime() < 3 * 24 * 60 * 60 * 1000
    ) {
      return "bg-orange-100 text-orange-700 border-orange-200";
    }

    return "bg-green-100 text-green-700 border-green-200";
  };

  const handleProductSelection = (
    productId: string,
    field:
      | "applicableProducts"
      | "excludedProducts"
      | "buyProducts"
      | "getProducts",
  ) => {
    const currentList = formData[field] || [];
    const newList = currentList.includes(productId)
      ? currentList.filter((id) => id !== productId)
      : [...currentList, productId];

    setFormData({ ...formData, [field]: newList });
  };

  const handleCategorySelection = (
    categoryId: string,
    field:
      | "applicableCategories"
      | "excludedCategories"
      | "buyCategories"
      | "getCategories",
  ) => {
    const currentList = formData[field] || [];
    const newList = currentList.includes(categoryId)
      ? currentList.filter((id) => id !== categoryId)
      : [...currentList, categoryId];

    setFormData({ ...formData, [field]: newList });
  };

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="p-6">
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-green-600" />
          </div>
        </div>
      </div>
    );
  }

  if (!session || (session.user as any)?.role !== "ADMIN") {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="p-6">
          <div className="bg-red-50 border border-red-200 rounded-xl p-4">
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <p className="text-red-800">
                You don't have permission to access this page.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="p-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Coupon Management
          </h1>
          <p className="text-gray-600 mt-2">
            Create and manage discount coupons
          </p>
        </div>

        {/* Actions Bar */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
              {coupons.length} {coupons.length === 1 ? "coupon" : "coupons"}{" "}
              total
            </div>
            <button
              onClick={() => {
                resetForm();
                setShowCreateModal(true);
              }}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
            >
              <Plus className="w-5 h-5" />
              <span>Create Coupon</span>
            </button>
          </div>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-green-600" />
          </div>
        ) : coupons.length === 0 ? (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12">
            <div className="text-center">
              <Tag className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No coupons found
              </h3>
              <p className="text-gray-600 mb-6">
                Get started by creating your first coupon
              </p>
              <button
                onClick={() => {
                  resetForm();
                  setShowCreateModal(true);
                }}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
              >
                Create Your First Coupon
              </button>
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Coupon
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Discount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Usage
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Validity
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {coupons.map((coupon) => (
                    <tr key={coupon.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {coupon.name}
                          </div>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700 border border-green-200">
                              {coupon.code}
                            </span>
                            <span className="text-xs text-gray-500">
                              {coupon.type.replace("_", " ")}
                            </span>
                            {coupon.isStackable && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700 border border-gray-200">
                                Stackable
                              </span>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-semibold text-green-600">
                          {getDiscountDisplay(coupon)}
                        </div>
                        {coupon.minimumAmount && (
                          <div className="text-xs text-gray-500">
                            Min: ₹{coupon.minimumAmount}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {coupon.usageCount}/{coupon.usageLimit || "∞"}
                        </div>
                        <div className="text-xs text-gray-500">
                          {coupon.usageLimit
                            ? `${Math.round((coupon.usageCount / coupon.usageLimit) * 100)}% used`
                            : "Unlimited"}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {new Date(coupon.validFrom).toLocaleDateString()}
                        </div>
                        {coupon.validUntil && (
                          <div className="text-xs text-gray-500">
                            Until{" "}
                            {new Date(coupon.validUntil).toLocaleDateString()}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(coupon)}`}
                        >
                          {coupon.isActive ? "Active" : "Inactive"}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => toggleCouponStatus(coupon)}
                            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                            title={coupon.isActive ? "Deactivate" : "Activate"}
                          >
                            {coupon.isActive ? (
                              <EyeOff className="w-5 h-5" />
                            ) : (
                              <Eye className="w-5 h-5" />
                            )}
                          </button>
                          <button
                            onClick={() => startEdit(coupon)}
                            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                            title="Edit"
                          >
                            <Edit className="w-5 h-5" />
                          </button>
                          <button
                            onClick={() => handleDelete(coupon.id)}
                            className="p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors"
                            title="Delete"
                          >
                            <Trash2 className="w-5 h-5" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Create/Edit Modal */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-bold text-gray-900">
                    {editingCoupon ? "Edit Coupon" : "Create New Coupon"}
                  </h2>
                  <button
                    onClick={() => {
                      setShowCreateModal(false);
                      setEditingCoupon(null);
                      resetForm();
                    }}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>
              </div>

              <form onSubmit={handleSubmit} className="p-6">
                <div className="space-y-6">
                  {/* Basic Information */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                      Basic Information
                    </h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Coupon Code *
                        </label>
                        <input
                          type="text"
                          value={formData.code}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              code: e.target.value.toUpperCase(),
                            })
                          }
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          required
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Coupon Name *
                        </label>
                        <input
                          type="text"
                          value={formData.name}
                          onChange={(e) =>
                            setFormData({ ...formData, name: e.target.value })
                          }
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          required
                        />
                      </div>
                    </div>

                    <div className="mt-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Description
                      </label>
                      <textarea
                        value={formData.description}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            description: e.target.value,
                          })
                        }
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                        rows={3}
                      />
                    </div>
                  </div>

                  {/* Coupon Type and Discount */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                      Discount Configuration
                    </h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Coupon Type *
                        </label>
                        <select
                          value={formData.type}
                          onChange={(e) => {
                            const newType = e.target.value as any;
                            const updates: any = { type: newType };

                            // When changing to BUY_X_GET_Y, set discount type to BUY_X_GET_Y
                            if (newType === "BUY_X_GET_Y") {
                              updates.discountType = "BUY_X_GET_Y";
                            }
                            // When changing from BUY_X_GET_Y to something else, reset discount type
                            else if (
                              formData.type === "BUY_X_GET_Y" &&
                              formData.discountType === "BUY_X_GET_Y"
                            ) {
                              updates.discountType = "PERCENTAGE";
                            }

                            setFormData({ ...formData, ...updates });
                          }}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          required
                        >
                          <option value="STORE_WIDE">Store Wide</option>
                          <option value="PRODUCT_SPECIFIC">
                            Product Specific
                          </option>
                          <option value="CATEGORY_SPECIFIC">
                            Category Specific
                          </option>
                          <option value="MINIMUM_PURCHASE">
                            Minimum Purchase
                          </option>
                          <option value="BUNDLE_DEAL">Bundle Deal</option>
                          <option value="FIRST_TIME_CUSTOMER">
                            First Time Customer
                          </option>
                          <option value="LOYALTY_REWARD">Loyalty Reward</option>
                          <option value="SEASONAL">Seasonal</option>
                          <option value="BUY_X_GET_Y">Buy X Get Y</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Discount Type *
                        </label>
                        <select
                          value={formData.discountType}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              discountType: e.target.value as any,
                            })
                          }
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          required
                        >
                          <option value="PERCENTAGE">Percentage</option>
                          <option value="FIXED_AMOUNT">Fixed Amount</option>
                          <option value="FREE_SHIPPING">Free Shipping</option>
                          {formData.type === "BUY_X_GET_Y" && (
                            <option value="BUY_X_GET_Y">Buy X Get Y</option>
                          )}
                        </select>
                      </div>
                    </div>

                    {/* Buy X Get Y Specific Fields */}
                    {formData.discountType === "BUY_X_GET_Y" && (
                      <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <h4 className="text-sm font-medium text-blue-900 mb-3">
                          Buy X Get Y Configuration
                        </h4>
                        <div className="grid grid-cols-3 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Buy Quantity *
                            </label>
                            <input
                              type="number"
                              value={formData.buyQuantity}
                              onChange={(e) =>
                                setFormData({
                                  ...formData,
                                  buyQuantity: parseInt(e.target.value) || 1,
                                })
                              }
                              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                              min="1"
                              required
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Get Quantity *
                            </label>
                            <input
                              type="number"
                              value={formData.getQuantity}
                              onChange={(e) =>
                                setFormData({
                                  ...formData,
                                  getQuantity: parseInt(e.target.value) || 1,
                                })
                              }
                              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                              min="1"
                              required
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Max Applications
                            </label>
                            <input
                              type="number"
                              value={formData.maxApplications}
                              onChange={(e) =>
                                setFormData({
                                  ...formData,
                                  maxApplications:
                                    parseInt(e.target.value) || 1,
                                })
                              }
                              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                              min="1"
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4 mt-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Discount Application *
                            </label>
                            <select
                              value={formData.discountApplication}
                              onChange={(e) =>
                                setFormData({
                                  ...formData,
                                  discountApplication: e.target.value as any,
                                })
                              }
                              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                            >
                              <option value="FREE">Free</option>
                              <option value="PERCENTAGE">Percentage Off</option>
                              <option value="FIXED_AMOUNT">
                                Fixed Amount Off
                              </option>
                            </select>
                          </div>

                          {formData.discountApplication !== "FREE" && (
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Discount Value *
                              </label>
                              <input
                                type="number"
                                value={formData.getDiscountValue}
                                onChange={(e) =>
                                  setFormData({
                                    ...formData,
                                    getDiscountValue:
                                      parseFloat(e.target.value) || 0,
                                  })
                                }
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                min="0"
                                step="0.01"
                                required
                              />
                            </div>
                          )}
                        </div>

                        {/* Buy Products/Categories Selection */}
                        <div className="mt-4">
                          <h5 className="text-sm font-medium text-gray-700 mb-2">
                            Buy Products/Categories
                          </h5>
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <label className="block text-xs text-gray-600 mb-1">
                                Select Buy Products
                              </label>
                              <div className="border border-gray-300 rounded-lg p-2 max-h-32 overflow-y-auto">
                                {products.map((product) => (
                                  <label
                                    key={product.id}
                                    className="flex items-center space-x-2 py-1"
                                  >
                                    <input
                                      type="checkbox"
                                      checked={
                                        formData.buyProducts?.includes(
                                          product.id,
                                        ) || false
                                      }
                                      onChange={() =>
                                        handleProductSelection(
                                          product.id,
                                          "buyProducts",
                                        )
                                      }
                                      className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                                    />
                                    <span className="text-sm text-gray-700">
                                      {product.name}
                                    </span>
                                  </label>
                                ))}
                              </div>
                            </div>

                            <div>
                              <label className="block text-xs text-gray-600 mb-1">
                                Select Buy Categories
                              </label>
                              <div className="border border-gray-300 rounded-lg p-2 max-h-32 overflow-y-auto">
                                {categories.map((category) => (
                                  <label
                                    key={category.id}
                                    className="flex items-center space-x-2 py-1"
                                  >
                                    <input
                                      type="checkbox"
                                      checked={
                                        formData.buyCategories?.includes(
                                          category.id,
                                        ) || false
                                      }
                                      onChange={() =>
                                        handleCategorySelection(
                                          category.id,
                                          "buyCategories",
                                        )
                                      }
                                      className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                                    />
                                    <span className="text-sm text-gray-700">
                                      {category.name}
                                    </span>
                                  </label>
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Get Products/Categories Selection */}
                        <div className="mt-4">
                          <h5 className="text-sm font-medium text-gray-700 mb-2">
                            Get Products/Categories
                          </h5>
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <label className="block text-xs text-gray-600 mb-1">
                                Select Get Products
                              </label>
                              <div className="border border-gray-300 rounded-lg p-2 max-h-32 overflow-y-auto">
                                {products.map((product) => (
                                  <label
                                    key={product.id}
                                    className="flex items-center space-x-2 py-1"
                                  >
                                    <input
                                      type="checkbox"
                                      checked={
                                        formData.getProducts?.includes(
                                          product.id,
                                        ) || false
                                      }
                                      onChange={() =>
                                        handleProductSelection(
                                          product.id,
                                          "getProducts",
                                        )
                                      }
                                      className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                                    />
                                    <span className="text-sm text-gray-700">
                                      {product.name}
                                    </span>
                                  </label>
                                ))}
                              </div>
                            </div>

                            <div>
                              <label className="block text-xs text-gray-600 mb-1">
                                Select Get Categories
                              </label>
                              <div className="border border-gray-300 rounded-lg p-2 max-h-32 overflow-y-auto">
                                {categories.map((category) => (
                                  <label
                                    key={category.id}
                                    className="flex items-center space-x-2 py-1"
                                  >
                                    <input
                                      type="checkbox"
                                      checked={
                                        formData.getCategories?.includes(
                                          category.id,
                                        ) || false
                                      }
                                      onChange={() =>
                                        handleCategorySelection(
                                          category.id,
                                          "getCategories",
                                        )
                                      }
                                      className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                                    />
                                    <span className="text-sm text-gray-700">
                                      {category.name}
                                    </span>
                                  </label>
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Standard Discount Value (non Buy X Get Y) */}
                    {formData.discountType !== "BUY_X_GET_Y" &&
                      formData.discountType !== "FREE_SHIPPING" && (
                        <div className="grid grid-cols-3 gap-4 mt-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Discount Value *
                            </label>
                            <input
                              type="number"
                              value={formData.discountValue}
                              onChange={(e) =>
                                setFormData({
                                  ...formData,
                                  discountValue: parseFloat(e.target.value),
                                })
                              }
                              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                              min="0"
                              step="0.01"
                              required
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Minimum Amount
                            </label>
                            <input
                              type="number"
                              value={formData.minimumAmount || ""}
                              onChange={(e) =>
                                setFormData({
                                  ...formData,
                                  minimumAmount: e.target.value
                                    ? parseFloat(e.target.value)
                                    : undefined,
                                })
                              }
                              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                              min="0"
                              step="0.01"
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Maximum Discount
                            </label>
                            <input
                              type="number"
                              value={formData.maximumDiscount || ""}
                              onChange={(e) =>
                                setFormData({
                                  ...formData,
                                  maximumDiscount: e.target.value
                                    ? parseFloat(e.target.value)
                                    : undefined,
                                })
                              }
                              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                              min="0"
                              step="0.01"
                            />
                          </div>
                        </div>
                      )}
                  </div>

                  {/* Product/Category Selection for non Buy X Get Y */}
                  {(formData.type === "PRODUCT_SPECIFIC" ||
                    formData.type === "CATEGORY_SPECIFIC") &&
                    formData.discountType !== "BUY_X_GET_Y" && (
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                          Applicable Items
                        </h3>
                        {formData.type === "PRODUCT_SPECIFIC" && (
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Select Products
                            </label>
                            <div className="border border-gray-300 rounded-lg p-3 max-h-48 overflow-y-auto">
                              {products.length === 0 ? (
                                <p className="text-sm text-gray-500 text-center py-4">
                                  No products available
                                </p>
                              ) : (
                                products.map((product) => (
                                  <label
                                    key={product.id}
                                    className="flex items-center space-x-2 py-1"
                                  >
                                    <input
                                      type="checkbox"
                                      checked={
                                        formData.applicableProducts?.includes(
                                          product.id,
                                        ) || false
                                      }
                                      onChange={() =>
                                        handleProductSelection(
                                          product.id,
                                          "applicableProducts",
                                        )
                                      }
                                      className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                                    />
                                    <span className="text-sm text-gray-700">
                                      {product.name} - ₹{product.price}
                                    </span>
                                  </label>
                                ))
                              )}
                            </div>
                          </div>
                        )}

                        {formData.type === "CATEGORY_SPECIFIC" && (
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Select Categories
                            </label>
                            <div className="border border-gray-300 rounded-lg p-3 max-h-48 overflow-y-auto">
                              {categories.map((category) => (
                                <label
                                  key={category.id}
                                  className="flex items-center space-x-2 py-1"
                                >
                                  <input
                                    type="checkbox"
                                    checked={
                                      formData.applicableCategories?.includes(
                                        category.id,
                                      ) || false
                                    }
                                    onChange={() =>
                                      handleCategorySelection(
                                        category.id,
                                        "applicableCategories",
                                      )
                                    }
                                    className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                                  />
                                  <span className="text-sm text-gray-700">
                                    {category.name}
                                  </span>
                                </label>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                  {/* Usage Limits */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                      Usage Limits
                    </h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Total Usage Limit
                        </label>
                        <input
                          type="number"
                          value={formData.usageLimit || ""}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              usageLimit: e.target.value
                                ? parseInt(e.target.value)
                                : undefined,
                            })
                          }
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          min="1"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Per User Usage Limit
                        </label>
                        <input
                          type="number"
                          value={formData.userUsageLimit || ""}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              userUsageLimit: e.target.value
                                ? parseInt(e.target.value)
                                : undefined,
                            })
                          }
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          min="1"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Validity Period */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                      Validity Period
                    </h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Valid From *
                        </label>
                        <input
                          type="date"
                          value={formData.validFrom}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              validFrom: e.target.value,
                            })
                          }
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          required
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Valid Until
                        </label>
                        <input
                          type="date"
                          value={formData.validUntil || ""}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              validUntil: e.target.value || undefined,
                            })
                          }
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Settings */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                      Settings
                    </h3>
                    <div className="flex items-center space-x-6">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.isActive}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              isActive: e.target.checked,
                            })
                          }
                          className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                        />
                        <span className="ml-2 text-sm text-gray-700">
                          Active
                        </span>
                      </label>

                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.isStackable}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              isStackable: e.target.checked,
                            })
                          }
                          className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                        />
                        <span className="ml-2 text-sm text-gray-700">
                          Stackable
                        </span>
                      </label>

                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.showInModule}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              showInModule: e.target.checked,
                            })
                          }
                          className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                        />
                        <span className="ml-2 text-sm text-gray-700">
                          Show in Module
                        </span>
                      </label>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => {
                      setShowCreateModal(false);
                      setEditingCoupon(null);
                      resetForm();
                    }}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
                  >
                    {editingCoupon ? "Update" : "Create"} Coupon
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
