"use client";

import React, { useState, useEffect } from "react";
import { Star, Check, X, User, Calendar, Loader2 } from "lucide-react";
import { Review, ReviewStatus } from "../../types";

const ReviewManagement: React.FC = () => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [status, setStatus] = useState<ReviewStatus>("PENDING");
  const [processing, setProcessing] = useState<string | null>(null);

  useEffect(() => {
    fetchReviews();
  }, [status]);

  const fetchReviews = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/reviews?status=${status}`);
      const data = await response.json();

      if (data.success) {
        setReviews(data.data);
      }
    } catch (error) {
      console.error("Error fetching reviews:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (
    reviewId: string,
    newStatus: "APPROVED" | "REJECTED",
  ) => {
    try {
      setProcessing(reviewId);
      const response = await fetch("/api/admin/reviews", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          reviewId,
          status: newStatus,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setReviews(reviews.filter((review) => review.id !== reviewId));
      }
    } catch (error) {
      console.error("Error updating review:", error);
    } finally {
      setProcessing(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="w-8 h-8 animate-spin text-green-600" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Review Management</h2>
        <div className="flex space-x-2">
          {(["PENDING", "APPROVED", "REJECTED"] as ReviewStatus[]).map(
            (tab) => (
              <button
                key={tab}
                onClick={() => setStatus(tab)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  status === tab
                    ? "bg-green-600 text-white"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}
              >
                {tab} ({reviews.filter((r) => r.status === tab).length})
              </button>
            ),
          )}
        </div>
      </div>

      {reviews.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-gray-400 mb-4">
            <Star className="w-12 h-12 mx-auto" />
          </div>
          <p className="text-gray-600">
            No {status.toLowerCase()} reviews found
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {reviews.map((review) => (
            <div
              key={review.id}
              className="bg-white rounded-lg shadow-sm border p-4"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                      <User className="w-4 h-4 text-gray-500" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">
                        {review.user?.name || "Anonymous"}
                      </p>
                      <p className="text-sm text-gray-500">
                        {review.user?.email}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 mb-2">
                    <div className="flex">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`w-4 h-4 ${
                            star <= review.rating
                              ? "text-yellow-400 fill-current"
                              : "text-gray-300"
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-gray-500">
                      {review.rating}/5
                    </span>
                  </div>

                  <div className="mb-2">
                    <p className="text-sm text-gray-600">
                      <strong>Product:</strong> {review.product?.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      <Calendar className="w-3 h-3 inline mr-1" />
                      {formatDate(review.createdAt)}
                    </p>
                  </div>

                  {review.title && (
                    <h4 className="font-medium text-gray-900 mb-1">
                      {review.title}
                    </h4>
                  )}
                  {review.content && (
                    <p className="text-gray-700 text-sm">{review.content}</p>
                  )}
                </div>

                <div className="flex space-x-2">
                  <button
                    onClick={() => handleStatusUpdate(review.id, "APPROVED")}
                    disabled={processing === review.id}
                    className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                    title="Approve"
                  >
                    <Check className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleStatusUpdate(review.id, "REJECTED")}
                    disabled={processing === review.id}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                    title="Reject"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ReviewManagement;
