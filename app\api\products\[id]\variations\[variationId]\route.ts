import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../../../lib/auth";
import connectDB from "../../../../../lib/mongoose";
import { Types } from "mongoose";
import { ProductVariant } from "../../../../../lib/models";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; variationId: string } },
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await connectDB();

    const { id: productId, variationId } = params;
    const body = await request.json();
    const { name, value, price, pricingMode } = body;

    if (!name || !value) {
      return NextResponse.json(
        { error: "Name and value are required" },
        { status: 400 },
      );
    }

    const validPricingModes: Array<"REPLACE" | "INCREMENT" | "FIXED"> = [
      "REPLACE",
      "INCREMENT",
      "FIXED",
    ];
    const validatedPricingMode =
      pricingMode && validPricingModes.includes(pricingMode)
        ? (pricingMode as "REPLACE" | "INCREMENT" | "FIXED")
        : "INCREMENT";

    // Narrow lean() typing to avoid array|object unions on existingVariation
    type LeanVariant = {
      _id: Types.ObjectId;
      productId: Types.ObjectId;
      name: string;
      value: string;
      price: number | null;
      pricingMode: "REPLACE" | "INCREMENT" | "FIXED";
    };

    const existingVariation = await ProductVariant.findById(
      Types.ObjectId.isValid(variationId) ? new Types.ObjectId(variationId) : variationId
    ).lean<LeanVariant | null>();
    if (!existingVariation) {
      return NextResponse.json(
        { error: "Variation not found" },
        { status: 404 },
      );
    }
    if (String(existingVariation.productId) !== String(productId)) {
      return NextResponse.json(
        { error: "Variation does not belong to this product" },
        { status: 400 },
      );
    }

    const duplicate = await ProductVariant.findOne({
      _id: { $ne: (Types.ObjectId.isValid(variationId) ? new Types.ObjectId(variationId) : variationId) },
      productId: (Types.ObjectId.isValid(productId) ? new Types.ObjectId(productId) : productId),
      name,
      value,
    }).lean();
    if (duplicate) {
      return NextResponse.json(
        { error: "Another variation with this name and value already exists" },
        { status: 400 },
      );
    }

    let parsedPrice: number | null = null;
    if (price !== undefined && price !== null && price !== "") {
      const numPrice =
        typeof price === "string" ? parseFloat(price) : Number(price);
      if (!isNaN(numPrice) && isFinite(numPrice)) {
        parsedPrice = numPrice;
      }
    }

    const updated = await ProductVariant.findByIdAndUpdate(
      (Types.ObjectId.isValid(variationId) ? new Types.ObjectId(variationId) : variationId),
      {
        name: name.trim(),
        value: value.trim(),
        price: parsedPrice,
        pricingMode: validatedPricingMode,
        updatedAt: new Date(),
      },
      { new: true },
    ).lean();

    return NextResponse.json({
      success: true,
      data: updated,
      message: "Variation updated successfully",
    });
  } catch (error) {
    console.error("Error updating variation:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to update variation",
      },
      { status: 500 },
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; variationId: string } },
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await connectDB();

    const { id: productId, variationId } = params;

    // Narrow lean() typing to avoid array|object unions on existingVariation
    type LeanVariant = {
      _id: Types.ObjectId;
      productId: Types.ObjectId;
      name: string;
      value: string;
      price: number | null;
      pricingMode: "REPLACE" | "INCREMENT" | "FIXED";
    };

    const existingVariation = await ProductVariant.findById(
      (Types.ObjectId.isValid(variationId) ? new Types.ObjectId(variationId) : variationId)
    ).lean<LeanVariant | null>();
    if (!existingVariation) {
      return NextResponse.json(
        { error: "Variation not found" },
        { status: 404 },
      );
    }

    if (String(existingVariation.productId) !== String(productId)) {
      return NextResponse.json(
        { error: "Variation does not belong to this product" },
        { status: 400 },
      );
    }

    await ProductVariant.deleteOne({
      _id: (Types.ObjectId.isValid(variationId) ? new Types.ObjectId(variationId) : variationId),
    });

    return NextResponse.json({
      success: true,
      message: "Variation deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting variation:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to delete variation",
      },
      { status: 500 },
    );
  }
}
