import { NextResponse } from "next/server";
import { logger } from "./logger";

export interface ErrorResponse {
  error: string;
  code?: string;
  requestId?: string;
}

// Error codes for client-facing errors
export const ERROR_CODES = {
  VALIDATION_ERROR: "VALIDATION_ERROR",
  AUTHENTICATION_ERROR: "AUTHENTICATION_ERROR",
  AUTHORIZATION_ERROR: "AUTHORIZATION_ERROR",
  NOT_FOUND: "NOT_FOUND",
  RATE_LIMIT_EXCEEDED: "RATE_LIMIT_EXCEEDED",
  SERVER_ERROR: "SERVER_ERROR",
  BAD_REQUEST: "BAD_REQUEST",
  CONFLICT: "CONFLICT",
} as const;

// Map of error messages for production
const PRODUCTION_ERROR_MESSAGES: Record<string, string> = {
  [ERROR_CODES.VALIDATION_ERROR]: "Invalid input provided",
  [ERROR_CODES.AUTHENTICATION_ERROR]: "Authentication required",
  [ERROR_CODES.AUTHORIZATION_ERROR]: "Insufficient permissions",
  [ERROR_CODES.NOT_FOUND]: "Resource not found",
  [ERROR_CODES.RATE_LIMIT_EXCEEDED]: "Too many requests",
  [ERROR_CODES.SERVER_ERROR]: "An error occurred processing your request",
  [ERROR_CODES.BAD_REQUEST]: "Invalid request",
  [ERROR_CODES.CONFLICT]: "Request conflicts with current state",
};

/**
 * Generate a unique request ID for error tracking
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
}

/**
 * Sanitize error message for production
 */
function sanitizeErrorMessage(error: any, isDevelopment: boolean): string {
  if (isDevelopment) {
    // In development, return the actual error message
    if (error instanceof Error) {
      return error.message;
    }
    return String(error);
  }

  // In production, return generic messages
  if (error.code && PRODUCTION_ERROR_MESSAGES[error.code]) {
    return PRODUCTION_ERROR_MESSAGES[error.code];
  }

  // Default generic message
  return PRODUCTION_ERROR_MESSAGES[ERROR_CODES.SERVER_ERROR];
}

/**
 * Global error handler for API routes
 */
export function handleApiError(
  error: any,
  isDevelopment = process.env.NODE_ENV === "development",
) {
  const requestId = generateRequestId();

  // Log the full error details server-side
  if (error instanceof Error) {
    logger.error("API Error", error, {
      requestId,
      timestamp: new Date().toISOString(),
      code: (error as any).code || ERROR_CODES.SERVER_ERROR,
    });
  } else {
    logger.error("API Error", undefined, {
      errorDetails: error,
      requestId,
      timestamp: new Date().toISOString(),
    });
  }

  // Determine status code
  let statusCode = 500;
  let errorCode = ERROR_CODES.SERVER_ERROR;

  if (error.statusCode) {
    statusCode = error.statusCode;
  } else if (error.code) {
    switch (error.code) {
      case ERROR_CODES.VALIDATION_ERROR:
      case ERROR_CODES.BAD_REQUEST:
        statusCode = 400;
        errorCode = error.code;
        break;
      case ERROR_CODES.AUTHENTICATION_ERROR:
        statusCode = 401;
        errorCode = error.code;
        break;
      case ERROR_CODES.AUTHORIZATION_ERROR:
        statusCode = 403;
        errorCode = error.code;
        break;
      case ERROR_CODES.NOT_FOUND:
        statusCode = 404;
        errorCode = error.code;
        break;
      case ERROR_CODES.CONFLICT:
        statusCode = 409;
        errorCode = error.code;
        break;
      case ERROR_CODES.RATE_LIMIT_EXCEEDED:
        statusCode = 429;
        errorCode = error.code;
        break;
    }
  }

  // Prepare response
  const response: ErrorResponse = {
    error: sanitizeErrorMessage(error, isDevelopment),
    code: errorCode,
    requestId,
  };

  // In development, include more details
  if (isDevelopment && error instanceof Error) {
    (response as any).stack = error.stack;
    (response as any).details = error;
  }

  return NextResponse.json(response, { status: statusCode });
}

/**
 * Custom error classes
 */
export class ApiError extends Error {
  constructor(
    message: string,
    public code: string = ERROR_CODES.SERVER_ERROR,
    public statusCode: number = 500,
  ) {
    super(message);
    this.name = "ApiError";
  }
}

export class ValidationError extends ApiError {
  constructor(message: string) {
    super(message, ERROR_CODES.VALIDATION_ERROR, 400);
    this.name = "ValidationError";
  }
}

export class AuthenticationError extends ApiError {
  constructor(message: string = "Authentication required") {
    super(message, ERROR_CODES.AUTHENTICATION_ERROR, 401);
    this.name = "AuthenticationError";
  }
}

export class AuthorizationError extends ApiError {
  constructor(message: string = "Insufficient permissions") {
    super(message, ERROR_CODES.AUTHORIZATION_ERROR, 403);
    this.name = "AuthorizationError";
  }
}

export class NotFoundError extends ApiError {
  constructor(resource: string = "Resource") {
    super(`${resource} not found`, ERROR_CODES.NOT_FOUND, 404);
    this.name = "NotFoundError";
  }
}

export class ConflictError extends ApiError {
  constructor(message: string) {
    super(message, ERROR_CODES.CONFLICT, 409);
    this.name = "ConflictError";
  }
}

export class RateLimitError extends ApiError {
  constructor(message: string = "Too many requests") {
    super(message, ERROR_CODES.RATE_LIMIT_EXCEEDED, 429);
    this.name = "RateLimitError";
  }
}

/**
 * Async error wrapper for API routes
 */
export function asyncHandler(fn: Function) {
  return async (req: Request, ...args: any[]) => {
    try {
      return await fn(req, ...args);
    } catch (error) {
      return handleApiError(error);
    }
  };
}
