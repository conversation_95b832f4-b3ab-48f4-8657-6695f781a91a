/* eslint-disable no-console */
import mongoose, { Types } from "mongoose";
import dotenv from "dotenv";
import bcrypt from "bcryptjs";

// Ensure env is loaded
dotenv.config({ path: ".env" });

async function connect() {
  const uri = process.env.MONGODB_URI || process.env.DATABASE_URL || "";
  if (!uri) {
    throw new Error("Missing MONGODB_URI or DATABASE_URL in environment.");
  }
  await mongoose.connect(uri, {
    dbName: process.env.MONGODB_DB || undefined,
  } as any);
  console.log("Connected to MongoDB");
}

// Import models after mongoose connection config is ready
import {
  User,
  Category,
  Product,
  ProductImage,
  ProductVariant,
  Review,
  Wishlist,
  WishlistItem,
  ProductCategory,
  Order,
  OrderItem,
  Address,
  HomepageSetting,
  Coupon,
  Newsletter,
  Notification,
  Testimonial,
  NotificationTemplate,
  Enquiry,
} from "../app/lib/models";

async function dropCollections() {
  // Best-effort drops (ignore errors if missing)
  const collections = [
    "user",
    "category",
    "product",
    "images",
    "variants",
    "reviews",
    "wishlist",
    "wishlistitems",
    "productcategories",
    "order",
    "items",
    "address",
    "homepagesetting",
    "coupons",
    "newsletter",
    "notifications",
    "testimonials",
    "notification_templates",
    "enquiry",
  ];

  const db = mongoose.connection.db;
  if (!db) return;

  for (const name of collections) {
    try {
      const exists = await db.listCollections({ name }).toArray();
      if (exists.length) {
        await db.dropCollection(name);
        console.log(`Dropped collection: ${name}`);
      }
    } catch (err) {
      console.warn(`Skip drop ${name}: ${(err as Error).message}`);
    }
  }
}

async function seed() {
  console.log("Seeding minimal demo data with ObjectId references...");
 
  // Hash demo passwords
  const adminPlain = "demo-admin";
  const customerPlain = "demo-customer";
  const salt = await bcrypt.genSalt(10);
  const adminHash = await bcrypt.hash(adminPlain, salt);
  const customerHash = await bcrypt.hash(customerPlain, salt);
 
  // Create admin user
  const admin = await User.create({
    email: "<EMAIL>",
    name: "Admin",
    role: "ADMIN",
    password: adminHash,
  });
 
  // Create customer user
  const customer = await User.create({
    email: "<EMAIL>",
    name: "Customer",
    role: "CUSTOMER",
    password: customerHash,
  });

  // Categories
  const faceCare = await Category.create({
    name: "Face Care",
    slug: "face-care",
    isActive: true,
  });
  const bodyCare = await Category.create({
    name: "Body Care",
    slug: "body-care",
    isActive: true,
  });

  // Products
  const p1 = await Product.create({
    name: "Anti-Pimple Pack",
    slug: "anti-pimple-pack",
    shortDescription: "Herbal anti-acne skincare pack",
    description: "A curated pack aimed at reducing acne with herbal actives.",
    isActive: true,
    isFeatured: true,
    categoryId: faceCare._id as Types.ObjectId,
    price: 499,
  });

  const p2 = await Product.create({
    name: "Body Butter",
    slug: "body-butter",
    shortDescription: "Deeply hydrating body butter",
    description: "Shea and cocoa butter rich formula for intense hydration.",
    isActive: true,
    isFeatured: false,
    categoryId: bodyCare._id as Types.ObjectId,
    price: 699,
  });

  // Images
  await ProductImage.insertMany([
    {
      productId: p1._id as Types.ObjectId,
      url: "/uploads/anti-pimple-pack-1.jpg",
      alt: "Anti-Pimple Pack",
      position: 0,
    },
    {
      productId: p2._id as Types.ObjectId,
      url: "/uploads/body-butter-1.jpg",
      alt: "Body Butter",
      position: 0,
    },
  ]);

  // Variants
  await ProductVariant.insertMany([
    {
      productId: p1._id as Types.ObjectId,
      name: "Size",
      value: "100g",
      pricingMode: "REPLACE",
      price: 499,
    },
    {
      productId: p2._id as Types.ObjectId,
      name: "Size",
      value: "200g",
      pricingMode: "REPLACE",
      price: 699,
    },
  ]);

  // Reviews
  await Review.insertMany([
    {
      productId: p1._id as Types.ObjectId,
      userId: customer._id as Types.ObjectId,
      rating: 5,
      title: "Works great!",
      comment: "Saw visible results in two weeks.",
      isApproved: true,
    },
  ]);

  // ProductCategory (if used)
  await ProductCategory.insertMany([
    {
      productId: p1._id as Types.ObjectId,
      categoryId: faceCare._id as Types.ObjectId,
    },
    {
      productId: p2._id as Types.ObjectId,
      categoryId: bodyCare._id as Types.ObjectId,
    },
  ]);

  // Wishlist and items (demo)
  const wl = await Wishlist.create({
    userId: customer._id as Types.ObjectId,
    productId: p1._id as Types.ObjectId,
  });
  await WishlistItem.create({
    userId: customer._id as Types.ObjectId,
    productId: p2._id as Types.ObjectId,
  });

  // Address
  const addr = await Address.create({
    userId: customer._id as Types.ObjectId,
    firstName: "John",
    lastName: "Doe",
    address1: "123 Demo Street",
    city: "Mumbai",
    state: "MH",
    postalCode: "400001",
    country: "IN",
    phone: "+911234567890",
    isDefault: true,
  });

  // Order
  const order = await Order.create({
    orderNumber: "ORD-1001",
    status: "DELIVERED",
    paymentStatus: "PAID",
    subtotal: 1198,
    tax: 0,
    shipping: 0,
    discount: 0,
    total: 1198,
    currency: "INR",
    userId: customer._id as Types.ObjectId,
    addressId: addr._id as Types.ObjectId,
  });

  await OrderItem.insertMany([
    {
      orderId: order._id as Types.ObjectId,
      productId: p1._id as Types.ObjectId,
      quantity: 1,
      price: 499,
      total: 499,
    },
    {
      orderId: order._id as Types.ObjectId,
      productId: p2._id as Types.ObjectId,
      quantity: 1,
      price: 699,
      total: 699,
    },
  ]);

  // Homepage settings
  await HomepageSetting.create({
    productOfTheMonthId: p1._id as Types.ObjectId,
    bannerText: "Glow Naturally",
    bannerCtaText: "Shop Now",
    bannerCtaLink: "/shop",
    isActive: true,
  });

  // Newsletter, Coupon, NotificationTemplate, Enquiry, Testimonial (optional demo)
  await Newsletter.create({ email: "<EMAIL>", isActive: true });
  await Coupon.create({
    code: "DEMO10",
    name: "10% Off",
    type: "PERCENTAGE",
    value: 10,
    isActive: true,
    validFrom: new Date(),
  });
  await NotificationTemplate.create({
    name: "order-delivered",
    type: "ORDER",
    title: "Order Delivered",
    message: "Your order has been delivered. Hope you love it!",
    isActive: true,
  });
  await Enquiry.create({
    name: "Prospect",
    email: "<EMAIL>",
    subject: "Bulk order",
    message: "Looking for bulk pricing.",
    status: "NEW",
  });
  await Testimonial.create({
    name: "Happy Customer",
    content: "Great products with natural ingredients!",
    rating: 5,
    isActive: true,
    order: 1,
  });

  console.log("Seeding complete.");
}

async function main() {
  try {
    await connect();
    await dropCollections();
    await seed();
    console.log("Reset and seed completed successfully.");
  } catch (err) {
    console.error("Reset and seed failed:", err);
    process.exitCode = 1;
  } finally {
    await mongoose.disconnect();
  }
}

main();