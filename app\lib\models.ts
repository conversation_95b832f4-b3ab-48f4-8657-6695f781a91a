import mongoose, { Schema, Document, Types } from "mongoose";

/**
 * IMPORTANT: Migration to MongoDB ObjectId FKs
 * - All interface _id types are now Types.ObjectId
 * - All foreign keys (userId, productId, categoryId, etc.) are Types.ObjectId
 * - Relevant schema fields updated to Schema.Types.ObjectId with proper refs
 * Data loss acceptable as per instruction (demo). Legacy string FK data will not be compatible.
 */

// User Schema
export interface IUser extends Document {
  _id: Types.ObjectId;
  email: string;
  name?: string;
  phone?: string;
  avatar?: string;
  password?: string;
  role: "ADMIN" | "CUSTOMER";
  emailVerified?: Date;
  resetToken?: string;
  resetTokenExpiry?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const UserSchema = new Schema<IUser>(
  {
    email: { type: String, required: true, unique: true },
    name: String,
    phone: String,
    avatar: String,
    password: String,
    role: { type: String, enum: ["ADMIN", "CUSTOMER"], default: "CUSTOMER" },
    emailVerified: Date,
    resetToken: String,
    resetTokenExpiry: Date,
  },
  { timestamps: true, collection: "user" }
);

// Product Schema
export interface IProduct extends Document {
  _id: Types.ObjectId;
  name: string;
  slug: string;
  description?: string;
  shortDescription?: string;
  comparePrice?: number;
  costPrice?: number;
  weight?: number;
  dimensions?: string;
  isActive: boolean;
  isFeatured: boolean;
  metaTitle?: string;
  metaDescription?: string;
  categoryId?: Types.ObjectId;
  price?: number;
  createdAt: Date;
  updatedAt: Date;
}

const ProductSchema = new Schema<IProduct>(
  {
    name: { type: String, required: true },
    slug: { type: String, required: true, unique: true },
    description: String,
    shortDescription: String,
    comparePrice: Number,
    costPrice: Number,
    weight: Number,
    dimensions: String,
    isActive: { type: Boolean, default: true },
    isFeatured: { type: Boolean, default: false },
    metaTitle: String,
    metaDescription: String,
    categoryId: { type: Schema.Types.ObjectId, ref: "Category" },
    price: Number,
  },
  { timestamps: true, collection: "product" }
);

// Category Schema
export interface ICategory extends Document {
  _id: Types.ObjectId;
  name: string;
  slug: string;
  description?: string;
  parentId?: Types.ObjectId;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const CategorySchema = new Schema<ICategory>(
  {
    name: { type: String, required: true, unique: true },
    slug: { type: String, required: true, unique: true },
    description: String,
    parentId: { type: Schema.Types.ObjectId, ref: "Category" },
    isActive: { type: Boolean, default: true },
  },
  { timestamps: true, collection: "category" }
);

// Order Schema
export interface IOrder extends Document {
  _id: Types.ObjectId;
  orderNumber: string;
  status:
    | "PENDING"
    | "CONFIRMED"
    | "PROCESSING"
    | "SHIPPED"
    | "DELIVERED"
    | "CANCELLED"
    | "REFUNDED";
  paymentStatus: "PENDING" | "PAID" | "FAILED" | "REFUNDED";
  paymentMethod?: string;
  paymentId?: string;
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  total: number;
  currency: string;
  notes?: string;
  userId: Types.ObjectId;
  couponCodes: string[];
  couponDiscount: number;
  flashSaleDiscount: number;
  estimatedDelivery?: string;
  trackingNumber?: string;
  addressId?: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const OrderSchema = new Schema<IOrder>(
  {
    orderNumber: { type: String, required: true, unique: true },
    status: {
      type: String,
      enum: [
        "PENDING",
        "CONFIRMED",
        "PROCESSING",
        "SHIPPED",
        "DELIVERED",
        "CANCELLED",
        "REFUNDED",
      ],
      default: "PENDING",
    },
    paymentStatus: {
      type: String,
      enum: ["PENDING", "PAID", "FAILED", "REFUNDED"],
      default: "PENDING",
    },
    paymentMethod: String,
    paymentId: String,
    subtotal: { type: Number, required: true },
    tax: { type: Number, default: 0 },
    shipping: { type: Number, default: 0 },
    discount: { type: Number, default: 0 },
    total: { type: Number, required: true },
    currency: { type: String, default: "INR" },
    notes: String,
    userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
    couponCodes: [String],
    couponDiscount: { type: Number, default: 0 },
    flashSaleDiscount: { type: Number, default: 0 },
    estimatedDelivery: String,
    trackingNumber: String,
    addressId: { type: Schema.Types.ObjectId, ref: "Address" },
  },
  { timestamps: true, collection: "order" }
);

// HomepageSetting Schema
export interface IHomepageSetting extends Document {
  _id: Types.ObjectId;
  productOfTheMonthId?: Types.ObjectId;
  bannerText?: string;
  bannerCtaText?: string;
  bannerCtaLink?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const HomepageSettingSchema = new Schema<IHomepageSetting>(
  {
    productOfTheMonthId: { type: Schema.Types.ObjectId, ref: "Product" },
    bannerText: String,
    bannerCtaText: String,
    bannerCtaLink: String,
    isActive: { type: Boolean, default: true },
  },
  { timestamps: true, collection: "homepageSetting" }
);

// Testimonials Schema
export interface ITestimonial extends Document {
  _id: Types.ObjectId;
  name: string;
  content: string;
  rating: number;
  image?: string;
  position?: string;
  company?: string;
  isActive: boolean;
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

const TestimonialSchema = new Schema<ITestimonial>(
  {
    name: { type: String, required: true },
    content: { type: String, required: true },
    rating: { type: Number, default: 5, min: 1, max: 5 },
    image: String,
    position: String,
    company: String,
    isActive: { type: Boolean, default: true },
    order: { type: Number, default: 0 },
  },
  { timestamps: true, collection: "testimonials" }
);

// Product Image Schema
export interface IProductImage extends Document {
  _id: Types.ObjectId;
  productId: Types.ObjectId;
  url: string;
  alt?: string;
  position: number;
  createdAt: Date;
  updatedAt: Date;
}

const ProductImageSchema = new Schema<IProductImage>(
  {
    productId: { type: Schema.Types.ObjectId, ref: "Product", required: true },
    url: { type: String, required: true },
    alt: String,
    position: { type: Number, default: 0 },
  },
  { timestamps: true, collection: "images" }
);

// Product Variant Schema
export interface IProductVariant extends Document {
  _id: Types.ObjectId;
  productId: Types.ObjectId;
  name: string;
  value: string;
  price?: number;
  pricingMode: "REPLACE" | "INCREMENT" | "FIXED";
  createdAt: Date;
  updatedAt: Date;
}

const ProductVariantSchema = new Schema<IProductVariant>(
  {
    productId: { type: Schema.Types.ObjectId, ref: "Product", required: true },
    name: { type: String, required: true },
    value: { type: String, required: true },
    price: Number,
    pricingMode: {
      type: String,
      enum: ["REPLACE", "INCREMENT", "FIXED"],
      default: "REPLACE",
    },
  },
  { timestamps: true, collection: "variants" }
);

// Review Schema
export interface IReview extends Document {
  _id: Types.ObjectId;
  productId: Types.ObjectId;
  userId: Types.ObjectId;
  rating: number;
  title?: string;
  comment?: string;
  isApproved: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const ReviewSchema = new Schema<IReview>(
  {
    productId: { type: Schema.Types.ObjectId, ref: "Product", required: true },
    userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
    rating: { type: Number, required: true, min: 1, max: 5 },
    title: String,
    comment: String,
    isApproved: { type: Boolean, default: false },
  },
  { timestamps: true, collection: "reviews" }
);

// Address Schema
export interface IAddress extends Document {
  _id: Types.ObjectId;
  userId: Types.ObjectId;
  firstName: string;
  lastName: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone: string;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const AddressSchema = new Schema<IAddress>(
  {
    userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    address1: { type: String, required: true },
    address2: String,
    city: { type: String, required: true },
    state: { type: String, required: true },
    postalCode: { type: String, required: true },
    country: { type: String, required: true },
    phone: { type: String, required: true },
    isDefault: { type: Boolean, default: false },
  },
  { timestamps: true, collection: "address" }
);

// Order Item Schema
export interface IOrderItem extends Document {
  _id: Types.ObjectId;
  orderId: Types.ObjectId;
  productId: Types.ObjectId;
  quantity: number;
  price: number;
  total: number;
  createdAt: Date;
  updatedAt: Date;
}

const OrderItemSchema = new Schema<IOrderItem>(
  {
    orderId: { type: Schema.Types.ObjectId, ref: "Order", required: true },
    productId: { type: Schema.Types.ObjectId, ref: "Product", required: true },
    quantity: { type: Number, required: true },
    price: { type: Number, required: true },
    total: { type: Number, required: true },
  },
  { timestamps: true, collection: "items" }
);

// Notification Schema
export interface INotification extends Document {
  _id: Types.ObjectId;
  userId: Types.ObjectId;
  title: string;
  message: string;
  type: "ORDER" | "PRODUCT" | "SYSTEM" | "MARKETING";
  isRead: boolean;
  data?: any;
  createdAt: Date;
  updatedAt: Date;
}

const NotificationSchema = new Schema<INotification>(
  {
    userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
    title: { type: String, required: true },
    message: { type: String, required: true },
    type: {
      type: String,
      enum: ["ORDER", "PRODUCT", "SYSTEM", "MARKETING"],
      required: true,
    },
    isRead: { type: Boolean, default: false },
    data: Schema.Types.Mixed,
  },
  { timestamps: true, collection: "notifications" }
);

// Coupon Schema
export interface ICoupon extends Document {
  _id: Types.ObjectId;
  code: string;
  name: string;
  description?: string;
  type: "PERCENTAGE" | "FIXED";
  value: number;
  minimumAmount?: number;
  maximumDiscount?: number;
  usageLimit?: number;
  usedCount: number;
  isActive: boolean;
  validFrom: Date;
  validUntil?: Date;
  userUsageLimit?: number;
  isStackable?: boolean;
  showInModule?: boolean;
  applicableProducts?: Types.ObjectId[];
  applicableCategories?: Types.ObjectId[];
  excludedProducts?: Types.ObjectId[];
  excludedCategories?: Types.ObjectId[];
  customerSegments?: string[];
  createdAt: Date;
  updatedAt: Date;
}

const CouponSchema = new Schema<ICoupon>(
  {
    code: { type: String, required: true, unique: true },
    name: { type: String, required: true },
    description: String,
    type: { type: String, enum: ["PERCENTAGE", "FIXED"], required: true },
    value: { type: Number, required: true },
    minimumAmount: Number,
    maximumDiscount: Number,
    usageLimit: Number,
    usedCount: { type: Number, default: 0 },
    isActive: { type: Boolean, default: true },
    validFrom: { type: Date, required: true },
    validUntil: Date,
    userUsageLimit: Number,
    isStackable: Boolean,
    showInModule: Boolean,
    applicableProducts: [{ type: Schema.Types.ObjectId, ref: "Product" }],
    applicableCategories: [{ type: Schema.Types.ObjectId, ref: "Category" }],
    excludedProducts: [{ type: Schema.Types.ObjectId, ref: "Product" }],
    excludedCategories: [{ type: Schema.Types.ObjectId, ref: "Category" }],
    customerSegments: [String],
  },
  { timestamps: true, collection: "coupons" }
);

// Wishlist Schema
export interface IWishlist extends Document {
  _id: Types.ObjectId;
  userId: Types.ObjectId;
  productId: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const WishlistSchema = new Schema<IWishlist>(
  {
    userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
    productId: { type: Schema.Types.ObjectId, ref: "Product", required: true },
  },
  { timestamps: true, collection: "wishlist" }
);

// Newsletter Schema
export interface INewsletter extends Document {
  _id: Types.ObjectId;
  email: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const NewsletterSchema = new Schema<INewsletter>(
  {
    email: { type: String, required: true, unique: true },
    isActive: { type: Boolean, default: true },
  },
  { timestamps: true, collection: "newsletter" }
);

// Product Category Schema (for many-to-many relationship)
export interface IProductCategory extends Document {
  _id: Types.ObjectId;
  productId: Types.ObjectId;
  categoryId: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const ProductCategorySchema = new Schema<IProductCategory>(
  {
    productId: { type: Schema.Types.ObjectId, ref: "Product", required: true },
    categoryId: { type: Schema.Types.ObjectId, ref: "Category", required: true },
  },
  { timestamps: true, collection: "productCategories" }
);

// WishlistItem Schema (alias kept for compatibility if used)
export interface IWishlistItem extends Document {
  _id: Types.ObjectId;
  userId: Types.ObjectId;
  productId: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const WishlistItemSchema = new Schema<IWishlistItem>(
  {
    userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
    productId: { type: Schema.Types.ObjectId, ref: "Product", required: true },
  },
  { timestamps: true, collection: "wishlistItems" }
);

// Create and export models
export const User =
  mongoose.models.User || mongoose.model<IUser>("User", UserSchema);
export const Product =
  mongoose.models.Product || mongoose.model<IProduct>("Product", ProductSchema);
export const Category =
  mongoose.models.Category ||
  mongoose.model<ICategory>("Category", CategorySchema);
export const Order =
  mongoose.models.Order || mongoose.model<IOrder>("Order", OrderSchema);
export const HomepageSetting =
  mongoose.models.HomepageSetting ||
  mongoose.model<IHomepageSetting>("HomepageSetting", HomepageSettingSchema);
export const Testimonial =
  mongoose.models.Testimonial ||
  mongoose.model<ITestimonial>("Testimonial", TestimonialSchema);
export const ProductImage =
  mongoose.models.ProductImage ||
  mongoose.model<IProductImage>("ProductImage", ProductImageSchema);
export const ProductVariant =
  mongoose.models.ProductVariant ||
  mongoose.model<IProductVariant>("ProductVariant", ProductVariantSchema);
export const Review =
  mongoose.models.Review || mongoose.model<IReview>("Review", ReviewSchema);
export const Address =
  mongoose.models.Address || mongoose.model<IAddress>("Address", AddressSchema);
export const OrderItem =
  mongoose.models.OrderItem ||
  mongoose.model<IOrderItem>("OrderItem", OrderItemSchema);
export const Notification =
  mongoose.models.Notification ||
  mongoose.model<INotification>("Notification", NotificationSchema);
export const Coupon =
  mongoose.models.Coupon || mongoose.model<ICoupon>("Coupon", CouponSchema);
export const Wishlist =
  mongoose.models.Wishlist ||
  mongoose.model<IWishlist>("Wishlist", WishlistSchema);
export const Newsletter =
  mongoose.models.Newsletter ||
  mongoose.model<INewsletter>("Newsletter", NewsletterSchema);
export const ProductCategory =
  mongoose.models.ProductCategory ||
  mongoose.model<IProductCategory>("ProductCategory", ProductCategorySchema);
export const WishlistItem =
  mongoose.models.WishlistItem ||
  mongoose.model<IWishlistItem>("WishlistItem", WishlistItemSchema);

// NotificationTemplate Schema
export interface INotificationTemplate extends Document {
  _id: Types.ObjectId;
  name: string;
  type: string;
  title: string;
  message: string;
  emailSubject?: string;
  emailTemplate?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const NotificationTemplateSchema = new Schema<INotificationTemplate>(
  {
    name: { type: String, required: true, unique: true },
    type: { type: String, required: true },
    title: { type: String, required: true },
    message: { type: String, required: true },
    emailSubject: String,
    emailTemplate: String,
    isActive: { type: Boolean, default: true },
  },
  { timestamps: true, collection: "notification_templates" }
);

export const NotificationTemplate =
  mongoose.models.NotificationTemplate ||
  mongoose.model<INotificationTemplate>(
    "NotificationTemplate",
    NotificationTemplateSchema
  );

// Enquiry Schema
export interface IEnquiry extends Document {
  _id: Types.ObjectId;
  name: string;
  email: string;
  subject: string;
  message: string;
  status: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

const EnquirySchema = new Schema<IEnquiry>(
  {
    name: { type: String, required: true },
    email: { type: String, required: true },
    subject: { type: String, required: true },
    message: { type: String, required: true },
    status: { type: String, default: "NEW" },
    notes: String,
  },
  { timestamps: true, collection: "enquiry" }
);

export const Enquiry =
  mongoose.models.Enquiry ||
  mongoose.model<IEnquiry>("Enquiry", EnquirySchema);
