import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../lib/auth";
import connectDB from "../../../lib/mongoose";
import { User, Review } from "../../../lib/models";
import { logger } from "../../../lib/logger";

// Define valid review status values
const VALID_REVIEW_STATUSES = ["PENDING", "APPROVED", "REJECTED"] as const;
type ValidReviewStatus = (typeof VALID_REVIEW_STATUSES)[number];

function isValidReviewStatus(status: string): status is ValidReviewStatus {
  return VALID_REVIEW_STATUSES.includes(status as ValidReviewStatus);
}

export async function GET(request: NextRequest) {
  try {
    await connectDB();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Get user from database to verify role and existence
    let user;
    if (session.user.id) {
      user = await User.findById(session.user.id)
        .select("id role email")
        .lean();
    } else if (session.user.email) {
      user = await User.findOne({ email: session.user.email })
        .select("id role email")
        .lean();
    }

    if (!user || (user as any).role !== "ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 401 },
      );
    }

    const { searchParams } = new URL(request.url);
    const statusParam = searchParams.get("status") || "PENDING";
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");

    // Validate the status parameter
    const status: ValidReviewStatus = isValidReviewStatus(statusParam)
      ? statusParam
      : "PENDING";

    const skip = (page - 1) * limit;

    logger.info("Admin reviews API temporarily disabled during migration");

    // Return empty results during migration
    return NextResponse.json({
      success: true,
      data: [],
      pagination: {
        page,
        limit,
        total: 0,
        pages: 0,
      },
    });
  } catch (error) {
    logger.error("Error fetching reviews:", error as Error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch reviews" },
      { status: 500 },
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    await connectDB();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Get user from database to verify role and existence
    let user;
    if (session.user.id) {
      user = await User.findById(session.user.id)
        .select("id role email")
        .lean();
    } else if (session.user.email) {
      user = await User.findOne({ email: session.user.email })
        .select("id role email")
        .lean();
    }

    if (!user || (user as any).role !== "ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 401 },
      );
    }

    logger.info(
      "Admin review update API temporarily disabled during migration",
    );

    return NextResponse.json({
      success: true,
      data: null,
      message: "Review update temporarily disabled during migration",
    });
  } catch (error) {
    logger.error("Error updating review:", error as Error);
    return NextResponse.json(
      { success: false, error: "Failed to update review" },
      { status: 500 },
    );
  }
}
