import { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import * as bcrypt from "bcryptjs";
import connectDB from "./mongoose";
import { User, type IUser } from "./models";

interface ExtendedUser {
  id: string;
  email: string;
  name?: string | null;
  role: string;
}

export const authOptions: NextAuthOptions = {
  // Remove PrismaAdapter when using JWT strategy
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Invalid credentials");
        }

        await connectDB();
        const user = await User.findOne({ email: credentials.email });

        if (!user || !user.password) {
          throw new Error("Invalid credentials");
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password,
        );

        if (!isPasswordValid) {
          throw new Error("Invalid credentials");
        }

        return {
          id: user._id.toString(),
          email: user.email,
          name: user.name,
          role: user.role,
        } as ExtendedUser;
      },
    }),
  ],
  // Use JWT sessions, ensure cookie config aligns with NextAuth defaults
  session: {
    strategy: "jwt",
    maxAge: 7 * 24 * 60 * 60, // 7 days default
    updateAge: 24 * 60 * 60, // Update session every 24 hours
  },
  jwt: {
    maxAge: 7 * 24 * 60 * 60, // 7 days default
  },
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === 'production' ? `__Secure-next-auth.session-token` : `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        domain: process.env.NODE_ENV === 'production' ? process.env.NEXTAUTH_URL?.replace('https://', '').replace('http://', '') : undefined
      }
    }
  },
  callbacks: {
    async jwt({ token, user, account, profile }) {
      console.log("JWT callback called with:", {
        hasToken: !!token,
        hasUser: !!user,
        hasAccount: !!account,
        accountProvider: account?.provider,
        userId: user?.id,
        userEmail: user?.email,
      });

      // Handle initial sign in
      if (account && user) {
        // For OAuth providers, create/update user in database
        if (account.provider === "google") {
          try {
            const email = user.email || (profile as any)?.email;
            if (!email) {
              throw new Error("No email found for Google account");
            }

            await connectDB();
            // Check if user exists
            let dbUser = await User.findOne({ email });

            // Create user if doesn't exist
            if (!dbUser) {
              dbUser = await User.create({
                email,
                name:
                  user.name || (profile as any)?.name || email.split("@")[0],
                // Don't save Google profile picture
                role: "CUSTOMER",
                emailVerified: new Date(), // OAuth users are considered verified
              });
            }

            if (dbUser) {
              // Set token properties
              token.sub = dbUser._id.toString();
              token.role = dbUser.role;
              token.email = dbUser.email;
              token.name = dbUser.name;
            }
          } catch (error) {
            console.error("Error handling Google sign in:", error);
            throw error; // This will prevent sign in
          }
        } else if (user) {
          // For credentials provider
          console.log("Processing credentials user:", user);
          token.sub = user.id;
          token.role = (user as ExtendedUser).role;
          token.email = user.email;
          token.name = user.name;
        }
      }

      console.log("JWT token being returned:", {
        sub: token.sub,
        email: token.email,
        role: token.role,
        name: token.name,
      });

      return token;
    },
    async session({ session, token }) {
      // Populate session with user data from token
      console.log("Session callback called with:", {
        hasSession: !!session,
        hasToken: !!token,
        tokenSub: token?.sub,
        tokenRole: token?.role,
        tokenEmail: token?.email,
        tokenName: token?.name,
      });

      if (token) {
        session.user = {
          id: (token.sub as string) ?? "",
          role: (token.role as string) ?? "CUSTOMER",
          email: (token.email as string) ?? "",
          name: (token.name as string | null) ?? null,
          image: null,
        } as any;
        
        console.log("Session populated with:", session.user);
      }
      
      console.log("Session being returned:", session);
      return session;
    },
    async redirect({ url, baseUrl }) {
      // Prefer explicit callbackUrl if provided, else dashboard/home depending on role via query
      try {
        const u = new URL(url, baseUrl);
        // If NextAuth default callback cookie/param is present, respect relative paths
        if (u.origin === "null") return baseUrl;
        if (u.pathname.startsWith("/")) return `${baseUrl}${u.pathname}${u.search}${u.hash}`;
        if (u.origin === baseUrl) return u.toString();
      } catch {
        // ignore parsing issues and fallback
      }
      return baseUrl;
    },
  },
  events: {
    async signIn({ user, account, profile, isNewUser }) {
      // Log sign in events in development only
      if (process.env.NODE_ENV === "development") {
        console.log("Sign in event:", {
          userId: user.id,
          email: user.email,
          provider: account?.provider,
          isNewUser,
        });
      }
    },
  },
  pages: {
    signIn: "/login",
    signOut: "/",
    error: "/login",
  },
  secret: process.env.NEXTAUTH_SECRET,
  // Ensure correct base URL to generate/validate callback & cookie domains
  // NEXTAUTH_URL must match the browser origin, e.g. http://localhost:3000
  // Enable debug in development to see what's happening
  debug: process.env.NODE_ENV === "development",
};
