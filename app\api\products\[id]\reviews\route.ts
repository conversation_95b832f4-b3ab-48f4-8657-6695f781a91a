import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../../lib/auth";
import connectDB from "../../../../lib/mongoose";
import { Product, Review, User, Order, OrderItem } from "../../../../lib/models";
import {
  AuthenticationError,
  ConflictError,
  ValidationError,
  asyncHandler,
} from "../../../../lib/errors";
import { logger } from "../../../../lib/logger";
import { reviewNotifications } from "../../../../lib/notification-helpers";
import { sanitizeText, sanitizeHtml } from "../../../../lib/sanitize";

type ReviewStatus = "PENDING" | "APPROVED" | "REJECTED";

export const GET = asyncHandler(
  async (request: NextRequest, { params }: { params: { id: string } }) => {
    const productId = params.id;
    logger.apiRequest("GET", `/api/products/${productId}/reviews`);

    const session = await getServerSession(authOptions);
    const currentUserId = session?.user?.id;

    await connectDB();

    const orClauses: any[] = [{ status: "APPROVED" }];
    if (currentUserId) {
      orClauses.push({ status: "PENDING", userId: currentUserId });
    }

    // Lean types to avoid array|object union issues
    interface LeanUserSummary {
      _id: any;
      name?: string;
      avatar?: string;
    }
    interface LeanReviewDoc {
      _id: any;
      userId: any;
      productId: any;
      rating: number;
      title?: string | null;
      content?: string | null;
      isVerified?: boolean;
      status?: ReviewStatus;
      createdAt?: Date | string;
      updatedAt?: Date | string;
    }

    const reviews = await Review.find({
      productId,
      $or: orClauses,
    })
      .sort({ createdAt: -1 })
      .lean<LeanReviewDoc[]>();

    // attach user summary
    const userIds = Array.from(new Set(reviews.map((r) => String(r.userId))));
    const users = await User.find({ _id: { $in: userIds } })
      .select("_id name avatar")
      .lean<LeanUserSummary[]>();
    const byId = new Map(users.map((u) => [String(u._id), u]));
    const enriched = reviews.map((r) => {
      const u = byId.get(String(r.userId));
      return {
        ...r,
        user: u
          ? {
              id: String(u._id),
              name: u.name ?? "",
              avatar: u.avatar,
            }
          : null,
      };
    });

    logger.info("Product reviews fetched", {
      productId,
      count: enriched.length,
      includesPending: currentUserId ? "yes" : "no",
    });

    return NextResponse.json({
      success: true,
      data: enriched,
    });
  },
);

export const POST = asyncHandler(
  async (request: NextRequest, { params }: { params: { id: string } }) => {
    const productId = params.id;
    logger.apiRequest("POST", `/api/products/${productId}/reviews`);

    const session = await getServerSession(authOptions);

    if (!session?.user) {
      throw new AuthenticationError();
    }

    const body = await request.json();
    const { rating, title, content } = body;

    // Validate required fields
    if (!rating || rating < 1 || rating > 5) {
      throw new ValidationError("Rating must be between 1 and 5");
    }

    if (!session.user.id && !session.user.email) {
      throw new AuthenticationError(
        "Invalid session. Please log out and log back in.",
      );
    }

    await connectDB();

    // Try to resolve user id
    let userDoc = null as any;
    if (session.user.id) {
      userDoc = await User.findById(session.user.id)
        .select("_id email name")
        .lean();
    } else if (session.user.email) {
      userDoc = await User.findOne({ email: session.user.email })
        .select("_id email name")
        .lean();
    }
    const userId = userDoc?._id;
    if (!userDoc || !userId) {
      throw new AuthenticationError(
        "User not found. Please log out and log back in.",
      );
    }

    // Verify the product exists (lean typed to ensure 'name' is available)
    interface LeanProductSummary {
      _id: any;
      name?: string;
    }
    const productExists = await Product.findById(productId)
      .select("_id name")
      .lean<LeanProductSummary | null>();
    if (!productExists) {
      return NextResponse.json(
        { success: false, error: "Product not found" },
        { status: 404 },
      );
    }

    // Check if user has already reviewed this product
    const existingReview = await Review.findOne({
      userId: userId,
      productId: productId,
    }).lean();

    if (existingReview) {
      throw new ConflictError("You have already reviewed this product");
    }

    // Check if user has purchased this product (PAID)
    const hasPurchased = await Order.findOne({
      userId: userId,
      paymentStatus: "PAID",
      // ensure there is an order item with this productId
      _id: {
        $in: await OrderItem.find({ productId: productId }).distinct("orderId"),
      },
    }).lean();

    // Sanitize user input to prevent XSS
    const sanitizedTitle = title ? sanitizeText(title) : null;
    const sanitizedContent = content ? sanitizeHtml(content) : null;

    const created = await Review.create({
      rating,
      title: sanitizedTitle,
      content: sanitizedContent,
      isVerified: !!hasPurchased,
      status: "PENDING" as ReviewStatus,
      userId: userId,
      productId: productId,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Attach user projection to response
    const responseReview = {
      ...created.toObject(),
      user: {
        id: userDoc._id,
        name: userDoc.name,
        avatar: (userDoc as any).avatar,
      },
    };

    logger.info("Review created", {
      reviewId: String(created._id),
      productId,
      userId,
      rating,
      isVerified: !!hasPurchased,
    });

    // Send review submitted notification
    try {
      await reviewNotifications.reviewSubmitted(userId, {
        productId,
        productName: productExists?.name ?? "",
        rating,
      });
    } catch (notificationError) {
      logger.error(
        "Failed to send review submitted notification",
        notificationError as Error,
      );
      // Don't fail the review creation if notification fails
    }

    return NextResponse.json({
      success: true,
      data: responseReview,
      message: "Review submitted for approval",
    });
  },
);
