"use client";

import React, { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import {
  Mail,
  User,
  Calendar,
  MessageSquare,
  Search,
  Filter,
  ChevronLeft,
  ChevronRight,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
} from "lucide-react";

interface Enquiry {
  id: string;
  name: string;
  email: string;
  subject: string;
  message: string;
  status: "NEW" | "IN_PROGRESS" | "RESOLVED" | "CLOSED";
  notes: string | null;
  createdAt: string;
  updatedAt: string;
}

const statusConfig = {
  NEW: { label: "New", color: "bg-blue-100 text-blue-800", icon: AlertCircle },
  IN_PROGRESS: {
    label: "In Progress",
    color: "bg-yellow-100 text-yellow-800",
    icon: Clock,
  },
  RESOLVED: {
    label: "Resolved",
    color: "bg-green-100 text-green-800",
    icon: CheckCircle,
  },
  CLOSED: {
    label: "Closed",
    color: "bg-gray-100 text-gray-800",
    icon: XCircle,
  },
};

export default function EnquiryPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [enquiries, setEnquiries] = useState<Enquiry[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedEnquiry, setSelectedEnquiry] = useState<Enquiry | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("ALL");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    if (status === "loading") return;
    if (!session || session.user.role !== "ADMIN") {
      router.push("/admin");
      return;
    }
    fetchEnquiries();
  }, [session, status, router, currentPage, statusFilter, searchTerm]);

  const fetchEnquiries = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
        ...(statusFilter !== "ALL" && { status: statusFilter }),
        ...(searchTerm && { search: searchTerm }),
      });

      const response = await fetch(`/api/enquiries?${params}`);
      if (!response.ok) throw new Error("Failed to fetch enquiries");

      const data = await response.json();
      setEnquiries(data.enquiries);
      setTotalPages(data.pagination.totalPages);
    } catch (error) {
      console.error("Error fetching enquiries:", error);
    } finally {
      setLoading(false);
    }
  };

  const updateEnquiry = async (
    id: string,
    updates: { status?: string; notes?: string },
  ) => {
    try {
      setIsUpdating(true);
      const response = await fetch(`/api/enquiries/${id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(updates),
      });

      if (!response.ok) throw new Error("Failed to update enquiry");

      const data = await response.json();
      setEnquiries((prev) => prev.map((e) => (e.id === id ? data.enquiry : e)));
      if (selectedEnquiry?.id === id) {
        setSelectedEnquiry(data.enquiry);
      }
    } catch (error) {
      console.error("Error updating enquiry:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  const deleteEnquiry = async (id: string) => {
    if (!confirm("Are you sure you want to delete this enquiry?")) return;

    try {
      const response = await fetch(`/api/enquiries/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete enquiry");

      setEnquiries((prev) => prev.filter((e) => e.id !== id));
      if (selectedEnquiry?.id === id) {
        setSelectedEnquiry(null);
      }
    } catch (error) {
      console.error("Error deleting enquiry:", error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (status === "loading" || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">
        Contact Enquiries
      </h1>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search by name, email, subject, or message..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Filter className="text-gray-400 h-5 w-5" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <option value="ALL">All Status</option>
              <option value="NEW">New</option>
              <option value="IN_PROGRESS">In Progress</option>
              <option value="RESOLVED">Resolved</option>
              <option value="CLOSED">Closed</option>
            </select>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Enquiry List */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-sm">
            <div className="p-4 border-b">
              <h2 className="font-semibold text-gray-900">Enquiries</h2>
            </div>
            <div className="divide-y">
              {enquiries.map((enquiry) => {
                const StatusIcon = statusConfig[enquiry.status].icon;
                return (
                  <div
                    key={enquiry.id}
                    onClick={() => setSelectedEnquiry(enquiry)}
                    className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${
                      selectedEnquiry?.id === enquiry.id ? "bg-green-50" : ""
                    }`}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-medium text-gray-900 truncate">
                        {enquiry.name}
                      </h3>
                      <span
                        className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${statusConfig[enquiry.status].color}`}
                      >
                        <StatusIcon className="h-3 w-3" />
                        {statusConfig[enquiry.status].label}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 truncate">
                      {enquiry.subject}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {formatDate(enquiry.createdAt)}
                    </p>
                  </div>
                );
              })}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="p-4 border-t flex items-center justify-between">
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(1, prev - 1))
                  }
                  disabled={currentPage === 1}
                  className="p-2 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronLeft className="h-5 w-5" />
                </button>
                <span className="text-sm text-gray-600">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(totalPages, prev + 1))
                  }
                  disabled={currentPage === totalPages}
                  className="p-2 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronRight className="h-5 w-5" />
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Enquiry Details */}
        <div className="lg:col-span-2">
          {selectedEnquiry ? (
            <div className="bg-white rounded-lg shadow-sm">
              <div className="p-6 border-b">
                <div className="flex items-start justify-between">
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">
                      {selectedEnquiry.subject}
                    </h2>
                    <div className="mt-2 space-y-1">
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <User className="h-4 w-4" />
                        <span>{selectedEnquiry.name}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Mail className="h-4 w-4" />
                        <a
                          href={`mailto:${selectedEnquiry.email}`}
                          className="text-green-600 hover:underline"
                        >
                          {selectedEnquiry.email}
                        </a>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Calendar className="h-4 w-4" />
                        <span>{formatDate(selectedEnquiry.createdAt)}</span>
                      </div>
                    </div>
                  </div>
                  <select
                    value={selectedEnquiry.status}
                    onChange={(e) =>
                      updateEnquiry(selectedEnquiry.id, {
                        status: e.target.value,
                      })
                    }
                    disabled={isUpdating}
                    className={`px-3 py-1 rounded-full text-sm font-medium ${statusConfig[selectedEnquiry.status].color} border-0 focus:ring-2 focus:ring-green-500`}
                  >
                    {Object.entries(statusConfig).map(([value, config]) => (
                      <option key={value} value={value}>
                        {config.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="p-6 border-b">
                <h3 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  Message
                </h3>
                <p className="text-gray-700 whitespace-pre-wrap">
                  {selectedEnquiry.message}
                </p>
              </div>

              <div className="p-6">
                <h3 className="font-medium text-gray-900 mb-3">Admin Notes</h3>
                <textarea
                  value={selectedEnquiry.notes || ""}
                  onChange={(e) =>
                    setSelectedEnquiry({
                      ...selectedEnquiry,
                      notes: e.target.value,
                    })
                  }
                  placeholder="Add internal notes about this enquiry..."
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
                  rows={4}
                />
                <div className="mt-4 flex gap-3">
                  <button
                    onClick={() =>
                      updateEnquiry(selectedEnquiry.id, {
                        notes: selectedEnquiry.notes || "",
                      })
                    }
                    disabled={isUpdating}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Save Notes
                  </button>
                  <button
                    onClick={() => deleteEnquiry(selectedEnquiry.id)}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                  >
                    Delete Enquiry
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-sm p-12 text-center">
              <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Select an enquiry to view details</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
