import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../lib/auth";
import { listR2Files } from "../../../lib/r2";

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const folder = searchParams.get("folder") || undefined;
    const maxKeys = parseInt(searchParams.get("maxKeys") || "100");

    // List files from R2
    const files = await listR2Files(folder, maxKeys);

    return NextResponse.json({
      success: true,
      files: files,
    });
  } catch (error) {
    console.error("Error listing R2 files:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to list files from R2",
        files: [],
      },
      { status: 500 },
    );
  }
}
