import { NextRequest, NextResponse } from "next/server";
import * as bcrypt from "bcryptjs";
import { z } from "zod";
import { connectDB, User } from "@/app/lib/db";
import { authLimiter, withRateLimit } from "@/app/lib/rate-limit";

const resetPasswordSchema = z.object({
  token: z.string().min(1, "Reset token is required"),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      "Password must contain at least one uppercase letter, one lowercase letter, and one number",
    ),
});

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting (10 attempts per 15 minutes per IP)
    await withRateLimit(request, authLimiter, 10);

    const body = await request.json();

    // Validate input
    const validatedData = resetPasswordSchema.parse(body);
    const { token, password } = validatedData;

    await connectDB();

    // Find user with valid reset token and not expired
    const now = new Date();
    const user = await User.findOne({
      resetToken: token,
      resetTokenExpiry: { $gt: now },
    }).lean();

    if (!user) {
      return NextResponse.json(
        { error: "Invalid or expired reset token" },
        { status: 400 },
      );
    }

    // Hash new password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Update user password and clear reset token
    await User.updateOne(
      { _id: (user as any)._id },
      { $set: { password: hashedPassword }, $unset: { resetToken: "", resetTokenExpiry: "" } }
    );

    return NextResponse.json(
      { message: "Password reset successfully" },
      { status: 200 },
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.issues },
        { status: 400 },
      );
    }

    if (error instanceof Error && error.message.includes("Rate limit")) {
      return NextResponse.json(
        { error: "Too many password reset attempts. Please try again later." },
        { status: 429 },
      );
    }

    console.error("Reset password error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
