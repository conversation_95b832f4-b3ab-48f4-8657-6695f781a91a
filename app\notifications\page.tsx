"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import {
  ArrowLeft,
  Bell,
  BellOff,
  CheckCheck,
  ShoppingBag,
  Heart,
  Star,
  MessageSquare,
  AlertCircle,
  Loader2,
  Package,
  Truck,
  CheckCircle,
  XCircle,
  TrendingDown,
  Sparkles,
  Clock,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import Layout from "../components/Layout";
import { useNotifications } from "../context/NotificationContext";

const NotificationsPage = () => {
  const router = useRouter();
  const { data: session, status } = useSession();
  const { unreadCount, markAsRead, markAllAsRead } = useNotifications();

  const [notifications, setNotifications] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  // Redirect if not authenticated
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  // Fetch notifications
  const fetchNotificationsWithPagination = async (page = 1) => {
    setLoading(true);
    setError(null);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "20",
      });

      const response = await fetch(`/api/notifications?${params}`);
      const data = await response.json();

      if (data.success) {
        setNotifications(data.data.notifications);
        setTotalPages(data.data.pagination.totalPages);
        setTotalCount(data.data.pagination.totalCount);
        setCurrentPage(page);
      } else {
        setError(data.message || "Failed to fetch notifications.");
      }
    } catch (err) {
      console.error("Error fetching notifications:", err);
      setError("An unexpected error occurred.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (session?.user?.id) {
      fetchNotificationsWithPagination(1);
    }
  }, [session?.user?.id]);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "ORDER_PLACED":
        return <ShoppingBag className="w-5 h-5" />;
      case "ORDER_CONFIRMED":
        return <CheckCircle className="w-5 h-5" />;
      case "ORDER_PROCESSING":
        return <Package className="w-5 h-5" />;
      case "ORDER_SHIPPED":
        return <Truck className="w-5 h-5" />;
      case "ORDER_DELIVERED":
        return <CheckCircle className="w-5 h-5" />;
      case "ORDER_CANCELLED":
        return <XCircle className="w-5 h-5" />;
      case "WISHLIST_ADDED":
      case "WISHLIST_REMOVED":
        return <Heart className="w-5 h-5" />;
      case "PRICE_DROP_ALERT":
        return <TrendingDown className="w-5 h-5" />;
      case "REVIEW_REQUEST":
      case "REVIEW_SUBMITTED":
        return <Star className="w-5 h-5" />;
      case "ADMIN_MESSAGE":
      case "BROADCAST":
        return <MessageSquare className="w-5 h-5" />;
      case "PROMOTIONAL":
        return <Sparkles className="w-5 h-5" />;
      default:
        return <Bell className="w-5 h-5" />;
    }
  };

  const getNotificationStyle = (type: string) => {
    switch (type) {
      case "ORDER_PLACED":
      case "ORDER_CONFIRMED":
      case "ORDER_PROCESSING":
        return {
          iconBg: "bg-green-600",
          iconColor: "text-white",
          badge: "bg-green-100 text-green-700 border-green-200",
        };
      case "ORDER_SHIPPED":
      case "ORDER_DELIVERED":
        return {
          iconBg: "bg-green-700",
          iconColor: "text-white",
          badge: "bg-green-100 text-green-800 border-green-200",
        };
      case "ORDER_CANCELLED":
        return {
          iconBg: "bg-gray-600",
          iconColor: "text-white",
          badge: "bg-gray-100 text-gray-700 border-gray-200",
        };
      case "PRICE_DROP_ALERT":
        return {
          iconBg: "bg-green-600",
          iconColor: "text-white",
          badge: "bg-green-100 text-green-700 border-green-200",
        };
      case "WISHLIST_ADDED":
      case "WISHLIST_REMOVED":
        return {
          iconBg: "bg-green-500",
          iconColor: "text-white",
          badge: "bg-green-50 text-green-700 border-green-200",
        };
      case "REVIEW_REQUEST":
      case "REVIEW_SUBMITTED":
        return {
          iconBg: "bg-green-600",
          iconColor: "text-white",
          badge: "bg-green-100 text-green-700 border-green-200",
        };
      case "ADMIN_MESSAGE":
      case "BROADCAST":
      case "PROMOTIONAL":
        return {
          iconBg: "bg-green-700",
          iconColor: "text-white",
          badge: "bg-green-100 text-green-800 border-green-200",
        };
      default:
        return {
          iconBg: "bg-gray-500",
          iconColor: "text-white",
          badge: "bg-gray-100 text-gray-700 border-gray-200",
        };
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return "Just now";
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800)
      return `${Math.floor(diffInSeconds / 86400)}d ago`;

    return date.toLocaleDateString("en-US", { month: "short", day: "numeric" });
  };

  const formatNotificationType = (type: string) => {
    const typeMap: { [key: string]: string } = {
      ORDER_PLACED: "Order Placed",
      ORDER_CONFIRMED: "Order Confirmed",
      ORDER_PROCESSING: "Processing",
      ORDER_SHIPPED: "Shipped",
      ORDER_DELIVERED: "Delivered",
      ORDER_CANCELLED: "Cancelled",
      WISHLIST_ADDED: "Wishlist",
      WISHLIST_REMOVED: "Wishlist",
      PRICE_DROP_ALERT: "Price Drop",
      REVIEW_REQUEST: "Review Request",
      REVIEW_SUBMITTED: "Review",
      ADMIN_MESSAGE: "Message",
      BROADCAST: "Announcement",
      PROMOTIONAL: "Promotion",
      SYSTEM: "System",
    };
    return typeMap[type] || type.replace("_", " ");
  };

  const handleNotificationClick = async (notification: any) => {
    if (!notification.isRead) {
      await markAsRead(notification.id);
    }

    // Navigate based on notification type
    if (notification.type.startsWith("ORDER_")) {
      if (notification.data?.orderId) {
        router.push(`/order-history`);
      }
    } else if (
      notification.type === "PRICE_DROP_ALERT" &&
      notification.data?.productId
    ) {
      router.push(`/product/${notification.data.productId}`);
    }
  };

  if (status === "loading") {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="w-8 h-8 animate-spin text-green-600" />
        </div>
      </Layout>
    );
  }

  if (!session?.user) {
    return null;
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 py-8">
          {/* Header */}
          <div className="mb-6">
            <button
              onClick={() => router.back()}
              className="inline-flex items-center space-x-2 text-gray-500 hover:text-gray-800 mb-4 group"
            >
              <ArrowLeft className="w-4 h-4 group-hover:-translate-x-1 transition-transform" />
              <span className="font-medium text-sm">Back</span>
            </button>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <h1 className="text-2xl font-bold text-gray-900">
                  Notifications
                </h1>
                {unreadCount > 0 && (
                  <span className="px-3 py-1 text-xs font-bold text-white bg-green-600 rounded-full">
                    {unreadCount} New
                  </span>
                )}
              </div>

              {notifications.length > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="flex items-center space-x-2 px-3 py-1.5 bg-white text-gray-600 hover:text-gray-800 hover:bg-gray-50 border border-gray-200 rounded-lg transition-all duration-200 text-sm font-medium"
                >
                  <CheckCheck className="w-4 h-4" />
                  <span>Mark all as read</span>
                </button>
              )}
            </div>
          </div>

          {/* Notifications List */}
          <div className="space-y-4">
            {loading ? (
              <div className="bg-white rounded-2xl shadow-sm p-12 text-center">
                <Loader2 className="w-12 h-12 animate-spin text-green-600 mx-auto mb-4" />
                <p className="text-gray-600 text-lg">
                  Loading your notifications...
                </p>
              </div>
            ) : error ? (
              <div className="bg-white rounded-2xl shadow-sm p-12 text-center">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <AlertCircle className="w-8 h-8 text-red-500" />
                </div>
                <p className="text-gray-900 font-semibold text-lg mb-2">
                  Oops! Something went wrong
                </p>
                <p className="text-gray-600 mb-6">{error}</p>
                <button
                  onClick={() => fetchNotificationsWithPagination(currentPage)}
                  className="px-6 py-3 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors font-medium"
                >
                  Try again
                </button>
              </div>
            ) : notifications.length === 0 ? (
              <div className="bg-white rounded-2xl shadow-sm p-16 text-center">
                <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <BellOff className="w-12 h-12 text-gray-400" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  No notifications yet
                </h3>
                <p className="text-gray-600 text-lg mb-8">
                  We'll notify you when something important happens
                </p>
                <button
                  onClick={() => router.push("/shop")}
                  className="px-8 py-3 bg-green-600 text-white rounded-full hover:bg-green-700 transition-colors font-medium"
                >
                  Start Shopping
                </button>
              </div>
            ) : (
              <>
                {notifications.map((notification) => {
                  const style = getNotificationStyle(notification.type);
                  return (
                    <div
                      key={notification.id}
                      onClick={() => handleNotificationClick(notification)}
                      className={`bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer border ${
                        !notification.isRead
                          ? "border-green-500"
                          : "border-gray-200"
                      }`}
                    >
                      <div className="p-4">
                        <div className="flex items-center space-x-4">
                          <div
                            className={`w-10 h-10 rounded-lg ${style.iconBg} flex items-center justify-center flex-shrink-0`}
                          >
                            <div className={style.iconColor}>
                              {getNotificationIcon(notification.type)}
                            </div>
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-1">
                              <h3 className="text-md font-semibold text-gray-800 group-hover:text-green-600 transition-colors">
                                {notification.title}
                              </h3>
                              <div className="flex items-center space-x-2">
                                {!notification.isRead && (
                                  <div className="w-2.5 h-2.5 bg-green-500 rounded-full flex-shrink-0"></div>
                                )}
                                <span className="text-xs text-gray-500">
                                  {formatTimeAgo(notification.createdAt)}
                                </span>
                              </div>
                            </div>

                            <p className="text-sm text-gray-600 leading-snug">
                              {notification.message}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </>
            )}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-6">
              <div className="flex items-center justify-between">
                <p className="text-sm text-gray-500">
                  Page {currentPage} of {totalPages}
                </p>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() =>
                      fetchNotificationsWithPagination(currentPage - 1)
                    }
                    disabled={currentPage === 1}
                    className="px-3 py-1.5 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed border border-gray-200 text-sm font-medium"
                  >
                    Previous
                  </button>

                  <button
                    onClick={() =>
                      fetchNotificationsWithPagination(currentPage + 1)
                    }
                    disabled={currentPage === totalPages}
                    className="px-3 py-1.5 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed border border-gray-200 text-sm font-medium"
                  >
                    Next
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default NotificationsPage;
