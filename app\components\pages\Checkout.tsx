"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession, signIn } from "next-auth/react";
import {
  ArrowLeft,
  ArrowRight,
  Check,
  CreditCard,
  Truck,
  Shield,
  Wallet,
  Banknote,
  User,
  Edit3,
  LogIn,
  Tag,
} from "lucide-react";
import { useCart } from "../../context/CartContext";
import PaymentButton from "../PaymentButton";

interface Address {
  id: string;
  firstName: string;
  lastName: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
  isDefault: boolean;
}

const Checkout: React.FC = () => {
  const router = useRouter();
  const { data: session, status } = useSession();
  const { state, dispatch } = useCart();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    // Shipping Information
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    state: "",
    zipCode: "",
    country: "India",

    // Payment Information
    paymentMethod: "online", // 'online' or 'cod'

    // Billing same as shipping
    billingDifferent: false,
    billingAddress: "",
    billingCity: "",
    billingState: "",
    billingZipCode: "",
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isProcessing, setIsProcessing] = useState(false);
  const [userAddresses, setUserAddresses] = useState<Address[]>([]);
  const [selectedAddressId, setSelectedAddressId] = useState<string>("");
  const [isEditingAddress, setIsEditingAddress] = useState(false);
  const [showLoginForm, setShowLoginForm] = useState(false);
  const [loginData, setLoginData] = useState({ email: "", password: "" });
  const [loginError, setLoginError] = useState("");
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [loadingUserData, setLoadingUserData] = useState(false);

  // Fetch user data and addresses when logged in
  useEffect(() => {
    const fetchUserData = async () => {
      if (session?.user?.id && status === "authenticated") {
        setLoadingUserData(true);
        try {
          const response = await fetch(`/api/users/${session.user.id}`);
          if (response.ok) {
            const userData = await response.json();
            const user = userData.data;

            // Set user addresses
            setUserAddresses(user.addresses || []);

            // Find default address or use first address
            const defaultAddress =
              user.addresses?.find((addr: Address) => addr.isDefault) ||
              user.addresses?.[0];

            if (defaultAddress) {
              setSelectedAddressId(defaultAddress.id);
              // Prefill form with default address
              setFormData((prev) => ({
                ...prev,
                firstName: defaultAddress.firstName,
                lastName: defaultAddress.lastName,
                email: user.email,
                phone: defaultAddress.phone || user.phone || "",
                address: defaultAddress.address1,
                city: defaultAddress.city,
                state: defaultAddress.state,
                zipCode: defaultAddress.postalCode,
                country: defaultAddress.country,
              }));
            } else {
              // Just prefill user info if no addresses
              setFormData((prev) => ({
                ...prev,
                email: user.email,
                phone: user.phone || "",
                firstName: user.name?.split(" ")[0] || "",
                lastName: user.name?.split(" ").slice(1).join(" ") || "",
              }));
            }
          }
        } catch (error) {
          console.error("Error fetching user data:", error);
        } finally {
          setLoadingUserData(false);
        }
      }
    };

    fetchUserData();
  }, [session?.user?.id, status]);

  const steps = [
    { id: 1, name: "Shipping", icon: Truck },
    { id: 2, name: "Payment", icon: CreditCard },
    { id: 3, name: "Review", icon: Shield },
  ];

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
  ) => {
    const { name, value, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]:
        type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const handleAddressSelect = (addressId: string) => {
    const address = userAddresses.find((addr) => addr.id === addressId);
    if (address) {
      setSelectedAddressId(addressId);
      setFormData((prev) => ({
        ...prev,
        firstName: address.firstName,
        lastName: address.lastName,
        phone: address.phone || prev.phone,
        address: address.address1,
        city: address.city,
        state: address.state,
        zipCode: address.postalCode,
        country: address.country,
      }));
      setIsEditingAddress(false);
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoggingIn(true);
    setLoginError("");

    try {
      const result = await signIn("credentials", {
        email: loginData.email,
        password: loginData.password,
        redirect: false,
      });

      if (result?.error) {
        setLoginError("Invalid email or password");
      } else {
        setShowLoginForm(false);
        setLoginData({ email: "", password: "" });
      }
    } catch (error) {
      setLoginError("An error occurred during login");
    } finally {
      setIsLoggingIn(false);
    }
  };

  const handleLoginInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setLoginData((prev) => ({ ...prev, [name]: value }));
    if (loginError) setLoginError("");
  };

  const validateStep = (step: number) => {
    const newErrors: Record<string, string> = {};

    if (step === 1) {
      if (!formData.firstName) newErrors.firstName = "First name is required";
      if (!formData.lastName) newErrors.lastName = "Last name is required";
      if (!formData.email) newErrors.email = "Email is required";
      if (!formData.phone) newErrors.phone = "Phone is required";
      if (!formData.address) newErrors.address = "Address is required";
      if (!formData.city) newErrors.city = "City is required";
      if (!formData.state) newErrors.state = "State is required";
      if (!formData.zipCode) newErrors.zipCode = "ZIP code is required";
    }

    if (step === 2) {
      if (!formData.paymentMethod)
        newErrors.paymentMethod = "Please select a payment method";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep((prev) => Math.min(prev + 1, 3));
    }
  };

  const prevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1));
  };

  const handlePaymentSuccess = (orderId: string) => {
    setIsProcessing(false);
    dispatch({ type: "CLEAR_CART" });
    router.push(`/order-confirmation?orderId=${orderId}`);
  };

  const handlePaymentError = (error: string) => {
    setIsProcessing(false);
    setErrors({ payment: error });
  };

  const handleCODOrder = async () => {
    if (!validateStep(2)) return;

    setIsProcessing(true);

    try {
      // Create COD order
      const orderResponse = await fetch("/api/orders", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cartItems: state.items.map((item) => ({
            productId: item.product.id,
            quantity: item.quantity,
            price: item.product.price,
          })),
          shippingAddress: {
            firstName: formData.firstName,
            lastName: formData.lastName,
            address1: formData.address,
            address2: "",
            city: formData.city,
            state: formData.state,
            postalCode: formData.zipCode,
            country: formData.country,
            phone: formData.phone,
          },
          totalAmount: displayTotal,
          paymentMethod: "COD",
          appliedCoupons: [],
          flashSaleDiscount: state.flashSaleDiscount,
        }),
      });

      if (!orderResponse.ok) {
        const errorData = await orderResponse.json();
        throw new Error(errorData.message || "Failed to create order");
      }

      const orderData = await orderResponse.json();
      handlePaymentSuccess(orderData.order.id);
    } catch (error) {
      handlePaymentError((error as Error).message || "Failed to create order");
    }
  };

  const subtotal = state.subtotal;
  const shipping = 0;
  const codCharges = formData.paymentMethod === "cod" ? 50 : 0;
  const displayTotal = state.finalTotal + codCharges;

  if (state.items.length === 0) {
    return (
      <div className="lg:grid lg:grid-cols-12 lg:gap-8">
        <div className="lg:col-span-12 text-center py-16">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">
            Your cart is empty
          </h2>
          <p className="text-gray-600 mb-6">
            Add some products before checkout
          </p>
          <button
            onClick={() => router.push("/shop")}
            className="bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors"
          >
            Continue Shopping
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="lg:grid lg:grid-cols-12 lg:gap-8">
      {/* Mobile Layout */}
      <div className="lg:hidden">
        {/* Header */}
        <div className="sticky top-16 bg-white z-30 px-4 py-4 border-b">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={() => router.back()}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-xl font-bold text-gray-800">Checkout</h1>
            <div></div>
          </div>

          {/* Progress Steps */}
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    currentStep >= step.id
                      ? "bg-green-600 text-white"
                      : "bg-gray-200 text-gray-500"
                  }`}
                >
                  {currentStep > step.id ? (
                    <Check className="w-4 h-4" />
                  ) : (
                    <step.icon className="w-4 h-4" />
                  )}
                </div>
                <span
                  className={`ml-2 text-sm font-medium ${
                    currentStep >= step.id ? "text-green-600" : "text-gray-500"
                  }`}
                >
                  {step.name}
                </span>
                {index < steps.length - 1 && (
                  <div
                    className={`w-8 h-0.5 mx-2 ${
                      currentStep > step.id ? "bg-green-600" : "bg-gray-200"
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="px-4 py-6">
          {/* Login Section for Non-Authenticated Users */}
          {status !== "authenticated" && !showLoginForm && (
            <div className="mb-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <User className="w-5 h-5 text-blue-600" />
                  <div>
                    <h3 className="font-medium text-blue-900">
                      Have an account?
                    </h3>
                    <p className="text-sm text-blue-700">
                      Login to use saved addresses and faster checkout
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setShowLoginForm(true)}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
                >
                  <LogIn className="w-4 h-4" />
                  <span>Login</span>
                </button>
              </div>
            </div>
          )}

          {/* Login Form */}
          {showLoginForm && status !== "authenticated" && (
            <div className="mb-6 p-4 bg-white rounded-xl border border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-800">
                  Login to Your Account
                </h3>
                <button
                  onClick={() => setShowLoginForm(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ×
                </button>
              </div>

              <form onSubmit={handleLogin} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={loginData.email}
                    onChange={handleLoginInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Password
                  </label>
                  <input
                    type="password"
                    name="password"
                    value={loginData.password}
                    onChange={handleLoginInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                {loginError && (
                  <p className="text-red-500 text-sm">{loginError}</p>
                )}

                <div className="flex space-x-3">
                  <button
                    type="submit"
                    disabled={isLoggingIn}
                    className="flex-1 bg-blue-600 text-white py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50"
                  >
                    {isLoggingIn ? "Logging in..." : "Login"}
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowLoginForm(false)}
                    className="px-4 py-2 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* Step 1: Shipping Information */}
          {currentStep === 1 && (
            <div className="space-y-4">
              {/* Address Selection for Logged-in Users */}
              {status === "authenticated" &&
                userAddresses.length > 0 &&
                !isEditingAddress && (
                  <div className="mb-6">
                    <div className="flex items-center justify-between mb-4">
                      <h2 className="text-lg font-semibold text-gray-800">
                        Select Shipping Address
                      </h2>
                      <button
                        onClick={() => setIsEditingAddress(true)}
                        className="flex items-center space-x-2 text-green-600 hover:text-green-700 font-medium"
                      >
                        <Edit3 className="w-4 h-4" />
                        <span>Edit Details</span>
                      </button>
                    </div>

                    <div className="space-y-3">
                      {userAddresses.map((address) => (
                        <label
                          key={address.id}
                          className={`flex items-start p-4 border-2 rounded-xl cursor-pointer transition-all ${
                            selectedAddressId === address.id
                              ? "border-green-600 bg-green-50"
                              : "border-gray-200 hover:border-gray-300"
                          }`}
                        >
                          <input
                            type="radio"
                            name="selectedAddress"
                            value={address.id}
                            checked={selectedAddressId === address.id}
                            onChange={() => handleAddressSelect(address.id)}
                            className="sr-only"
                          />
                          <div
                            className={`w-5 h-5 rounded-full border-2 mr-3 mt-0.5 flex items-center justify-center ${
                              selectedAddressId === address.id
                                ? "border-green-600"
                                : "border-gray-300"
                            }`}
                          >
                            {selectedAddressId === address.id && (
                              <div className="w-3 h-3 rounded-full bg-green-600"></div>
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className="font-medium text-gray-800">
                                {address.firstName} {address.lastName}
                              </span>
                              {address.isDefault && (
                                <span className="px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full">
                                  Default
                                </span>
                              )}
                            </div>
                            <p className="text-sm text-gray-600">
                              {address.address1}
                              {address.address2 && `, ${address.address2}`}
                            </p>
                            <p className="text-sm text-gray-600">
                              {address.city}, {address.state}{" "}
                              {address.postalCode}
                            </p>
                            {address.phone && (
                              <p className="text-sm text-gray-600">
                                {address.phone}
                              </p>
                            )}
                          </div>
                        </label>
                      ))}
                    </div>
                  </div>
                )}

              {/* Manual Address Entry */}
              {(status !== "authenticated" ||
                userAddresses.length === 0 ||
                isEditingAddress) && (
                <>
                  <div className="flex items-center justify-between">
                    <h2 className="text-lg font-semibold text-gray-800 mb-4">
                      {status === "authenticated" && userAddresses.length > 0
                        ? "Edit Shipping Details"
                        : "Shipping Information"}
                    </h2>
                    {status === "authenticated" &&
                      userAddresses.length > 0 &&
                      isEditingAddress && (
                        <button
                          onClick={() => setIsEditingAddress(false)}
                          className="text-gray-600 hover:text-gray-800 font-medium"
                        >
                          Use Saved Address
                        </button>
                      )}
                  </div>

                  {loadingUserData && (
                    <div className="flex items-center justify-center py-4">
                      <div className="w-6 h-6 border-2 border-green-600 border-t-transparent rounded-full animate-spin"></div>
                      <span className="ml-2 text-gray-600">
                        Loading your details...
                      </span>
                    </div>
                  )}
                </>
              )}

              {/* Form Fields */}
              {(status !== "authenticated" ||
                userAddresses.length === 0 ||
                isEditingAddress) &&
                !loadingUserData && (
                  <>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          First Name
                        </label>
                        <input
                          type="text"
                          name="firstName"
                          value={formData.firstName}
                          onChange={handleInputChange}
                          className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                            errors.firstName
                              ? "border-red-500"
                              : "border-gray-300"
                          }`}
                        />
                        {errors.firstName && (
                          <p className="text-red-500 text-xs mt-1">
                            {errors.firstName}
                          </p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Last Name
                        </label>
                        <input
                          type="text"
                          name="lastName"
                          value={formData.lastName}
                          onChange={handleInputChange}
                          className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                            errors.lastName
                              ? "border-red-500"
                              : "border-gray-300"
                          }`}
                        />
                        {errors.lastName && (
                          <p className="text-red-500 text-xs mt-1">
                            {errors.lastName}
                          </p>
                        )}
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Email
                      </label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                          errors.email ? "border-red-500" : "border-gray-300"
                        }`}
                      />
                      {errors.email && (
                        <p className="text-red-500 text-xs mt-1">
                          {errors.email}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Phone
                      </label>
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                          errors.phone ? "border-red-500" : "border-gray-300"
                        }`}
                      />
                      {errors.phone && (
                        <p className="text-red-500 text-xs mt-1">
                          {errors.phone}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Address
                      </label>
                      <input
                        type="text"
                        name="address"
                        value={formData.address}
                        onChange={handleInputChange}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                          errors.address ? "border-red-500" : "border-gray-300"
                        }`}
                      />
                      {errors.address && (
                        <p className="text-red-500 text-xs mt-1">
                          {errors.address}
                        </p>
                      )}
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          City
                        </label>
                        <input
                          type="text"
                          name="city"
                          value={formData.city}
                          onChange={handleInputChange}
                          className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                            errors.city ? "border-red-500" : "border-gray-300"
                          }`}
                        />
                        {errors.city && (
                          <p className="text-red-500 text-xs mt-1">
                            {errors.city}
                          </p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          State
                        </label>
                        <input
                          type="text"
                          name="state"
                          value={formData.state}
                          onChange={handleInputChange}
                          className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                            errors.state ? "border-red-500" : "border-gray-300"
                          }`}
                        />
                        {errors.state && (
                          <p className="text-red-500 text-xs mt-1">
                            {errors.state}
                          </p>
                        )}
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        ZIP Code
                      </label>
                      <input
                        type="text"
                        name="zipCode"
                        value={formData.zipCode}
                        onChange={handleInputChange}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                          errors.zipCode ? "border-red-500" : "border-gray-300"
                        }`}
                      />
                      {errors.zipCode && (
                        <p className="text-red-500 text-xs mt-1">
                          {errors.zipCode}
                        </p>
                      )}
                    </div>
                  </>
                )}
            </div>
          )}

          {/* Step 2: Payment Information */}
          {currentStep === 2 && (
            <div className="space-y-4">
              <h2 className="text-lg font-semibold text-gray-800 mb-4">
                Payment Method
              </h2>

              <div className="space-y-3">
                {/* Online Payment Option */}
                <label
                  className={`flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all ${
                    formData.paymentMethod === "online"
                      ? "border-green-600 bg-green-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                >
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="online"
                    checked={formData.paymentMethod === "online"}
                    onChange={handleInputChange}
                    className="sr-only"
                  />
                  <div
                    className={`w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center ${
                      formData.paymentMethod === "online"
                        ? "border-green-600"
                        : "border-gray-300"
                    }`}
                  >
                    {formData.paymentMethod === "online" && (
                      <div className="w-3 h-3 rounded-full bg-green-600"></div>
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <Wallet className="w-5 h-5 text-gray-600" />
                      <span className="font-medium text-gray-800">
                        Online Payment
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      Pay securely using Credit/Debit Card, UPI, Net Banking, or
                      Wallets
                    </p>
                  </div>
                </label>

                {/* Cash on Delivery Option */}
                <label
                  className={`flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all ${
                    formData.paymentMethod === "cod"
                      ? "border-green-600 bg-green-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                >
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="cod"
                    checked={formData.paymentMethod === "cod"}
                    onChange={handleInputChange}
                    className="sr-only"
                  />
                  <div
                    className={`w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center ${
                      formData.paymentMethod === "cod"
                        ? "border-green-600"
                        : "border-gray-300"
                    }`}
                  >
                    {formData.paymentMethod === "cod" && (
                      <div className="w-3 h-3 rounded-full bg-green-600"></div>
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <Banknote className="w-5 h-5 text-gray-600" />
                      <span className="font-medium text-gray-800">
                        Cash on Delivery
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      Pay when your order is delivered to your doorstep
                    </p>
                  </div>
                </label>
              </div>

              {errors.paymentMethod && (
                <p className="text-red-500 text-sm mt-2">
                  {errors.paymentMethod}
                </p>
              )}

              {/* Additional info for online payment */}
              {formData.paymentMethod === "online" && (
                <div className="mt-4 p-4 bg-blue-50 rounded-xl">
                  <p className="text-sm text-blue-800">
                    <strong>Note:</strong> You will be redirected to a secure
                    payment gateway after reviewing your order.
                  </p>
                </div>
              )}

              {/* Additional info for COD */}
              {formData.paymentMethod === "cod" && (
                <div className="mt-4 p-4 bg-yellow-50 rounded-xl">
                  <p className="text-sm text-yellow-800">
                    <strong>Note:</strong> Please keep exact change ready for
                    delivery. Additional charges may apply for COD orders.
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Step 3: Review Order */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <h2 className="text-lg font-semibold text-gray-800">
                Review Your Order
              </h2>

              {/* Order Items */}
              <div className="space-y-4">
                {state.items.map((item) => (
                  <div
                    key={item.product.id}
                    className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg"
                  >
                    <div className="w-16 h-16 bg-gray-200 rounded-lg"></div>
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-800">
                        {item.product.name}
                      </h3>
                      <p className="text-sm text-gray-600">
                        Qty: {item.quantity}
                      </p>
                    </div>
                    <span className="font-medium text-gray-900">
                      ₹{(item.product.price * item.quantity).toFixed(2)}
                    </span>
                  </div>
                ))}
              </div>

              {/* Order Summary */}
              <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                <div className="flex justify-between text-gray-600">
                  <span>Subtotal</span>
                  <span>₹{state.subtotal.toFixed(2)}</span>
                </div>

                {state.flashSaleDiscount > 0 && (
                  <div className="flex justify-between text-red-600">
                    <span>
                      Flash Sale Discount ({state.flashSalePercentage}% OFF)
                    </span>
                    <span>-₹{state.flashSaleDiscount.toFixed(2)}</span>
                  </div>
                )}

                {state.coupons.totalDiscount > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>Coupon Discount</span>
                    <span>-₹{state.coupons.totalDiscount.toFixed(2)}</span>
                  </div>
                )}

                <div className="flex justify-between text-gray-600">
                  <span>Shipping</span>
                  <span>Free</span>
                </div>

                {formData.paymentMethod === "cod" && (
                  <div className="flex justify-between text-gray-600">
                    <span>COD Charges</span>
                    <span>₹{codCharges.toFixed(2)}</span>
                  </div>
                )}

                <div className="border-t border-gray-300 pt-2">
                  <div className="flex justify-between font-bold text-gray-900">
                    <span>Total</span>
                    <span>₹{displayTotal.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-8 pt-6 border-t">
            {currentStep > 1 && (
              <button
                onClick={prevStep}
                className="flex items-center space-x-2 px-6 py-3 border border-gray-300 rounded-full font-medium text-gray-700 hover:bg-gray-50 transition-colors"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back</span>
              </button>
            )}

            <div className="ml-auto">
              {currentStep < 3 ? (
                <button
                  onClick={nextStep}
                  className="flex items-center space-x-2 bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors"
                >
                  <span>Continue</span>
                  <ArrowRight className="w-4 h-4" />
                </button>
              ) : (
                <div className="space-y-3">
                  {formData.paymentMethod === "online" ? (
                    <PaymentButton
                      cartItems={state.items.map((item) => ({
                        productId: item.product.id,
                        quantity: item.quantity,
                        price: item.product.price,
                        product: {
                          name: item.product.name,
                          price: item.product.price,
                        },
                      }))}
                      shippingAddress={{
                        firstName: formData.firstName,
                        lastName: formData.lastName,
                        address1: formData.address,
                        address2: "",
                        city: formData.city,
                        state: formData.state,
                        postalCode: formData.zipCode,
                        country: formData.country,
                        phone: formData.phone,
                      }}
                      totalAmount={state.finalTotal}
                      appliedCoupons={[]}
                      flashSaleDiscount={state.flashSaleDiscount}
                      onSuccess={handlePaymentSuccess}
                      onError={handlePaymentError}
                      disabled={isProcessing}
                      className="rounded-full"
                    />
                  ) : (
                    <button
                      onClick={handleCODOrder}
                      disabled={isProcessing}
                      className="flex items-center justify-center space-x-2 bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors disabled:opacity-50 w-full"
                    >
                      {isProcessing ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          <span>Processing...</span>
                        </>
                      ) : (
                        <>
                          <span>Place COD Order</span>
                          <ArrowRight className="w-4 h-4" />
                        </>
                      )}
                    </button>
                  )}
                  {errors.payment && (
                    <p className="text-red-500 text-sm text-center">
                      {errors.payment}
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Desktop Layout - Enhanced with same features */}
      <div className="hidden lg:block lg:col-span-12">
        <div className="py-8">
          <div className="flex items-center mb-8">
            <button
              onClick={() => router.back()}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>Back to Cart</span>
            </button>
          </div>

          <h1 className="text-4xl font-bold text-gray-800 mb-8">Checkout</h1>

          {/* Login Section for Desktop */}
          {status !== "authenticated" && !showLoginForm && (
            <div className="mb-8 p-6 bg-blue-50 rounded-xl border border-blue-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <User className="w-6 h-6 text-blue-600" />
                  <div>
                    <h3 className="text-lg font-medium text-blue-900">
                      Have an account?
                    </h3>
                    <p className="text-blue-700">
                      Login to use saved addresses and faster checkout
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setShowLoginForm(true)}
                  className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-colors"
                >
                  <LogIn className="w-5 h-5" />
                  <span>Login</span>
                </button>
              </div>
            </div>
          )}

          {/* Login Form for Desktop */}
          {showLoginForm && status !== "authenticated" && (
            <div className="mb-8 p-6 bg-white rounded-xl border border-gray-200">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-800">
                  Login to Your Account
                </h3>
                <button
                  onClick={() => setShowLoginForm(false)}
                  className="text-gray-500 hover:text-gray-700 text-xl"
                >
                  ×
                </button>
              </div>

              <form onSubmit={handleLogin} className="grid grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={loginData.email}
                    onChange={handleLoginInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Password
                  </label>
                  <input
                    type="password"
                    name="password"
                    value={loginData.password}
                    onChange={handleLoginInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div className="col-span-2">
                  {loginError && (
                    <p className="text-red-500 text-sm mb-4">{loginError}</p>
                  )}

                  <div className="flex space-x-4">
                    <button
                      type="submit"
                      disabled={isLoggingIn}
                      className="bg-blue-600 text-white px-8 py-3 rounded-xl font-medium hover:bg-blue-700 transition-colors disabled:opacity-50"
                    >
                      {isLoggingIn ? "Logging in..." : "Login"}
                    </button>
                    <button
                      type="button"
                      onClick={() => setShowLoginForm(false)}
                      className="px-8 py-3 border border-gray-300 rounded-xl font-medium text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </form>
            </div>
          )}

          {/* Progress Steps */}
          <div className="flex items-center justify-center mb-12">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div
                  className={`w-12 h-12 rounded-full flex items-center justify-center ${
                    currentStep >= step.id
                      ? "bg-green-600 text-white"
                      : "bg-gray-200 text-gray-500"
                  }`}
                >
                  {currentStep > step.id ? (
                    <Check className="w-6 h-6" />
                  ) : (
                    <step.icon className="w-6 h-6" />
                  )}
                </div>
                <span
                  className={`ml-3 text-lg font-medium ${
                    currentStep >= step.id ? "text-green-600" : "text-gray-500"
                  }`}
                >
                  {step.name}
                </span>
                {index < steps.length - 1 && (
                  <div
                    className={`w-24 h-0.5 mx-8 ${
                      currentStep > step.id ? "bg-green-600" : "bg-gray-200"
                    }`}
                  />
                )}
              </div>
            ))}
          </div>

          <div className="grid grid-cols-3 gap-12">
            {/* Form Section */}
            <div className="col-span-2">
              {/* Step 1: Shipping Information */}
              {currentStep === 1 && (
                <div className="space-y-6">
                  {/* Address Selection for Logged-in Users */}
                  {status === "authenticated" &&
                    userAddresses.length > 0 &&
                    !isEditingAddress && (
                      <div className="mb-8">
                        <div className="flex items-center justify-between mb-6">
                          <h2 className="text-2xl font-semibold text-gray-800">
                            Select Shipping Address
                          </h2>
                          <button
                            onClick={() => setIsEditingAddress(true)}
                            className="flex items-center space-x-2 text-green-600 hover:text-green-700 font-medium"
                          >
                            <Edit3 className="w-5 h-5" />
                            <span>Edit Details</span>
                          </button>
                        </div>

                        <div className="space-y-4">
                          {userAddresses.map((address) => (
                            <label
                              key={address.id}
                              className={`flex items-start p-6 border-2 rounded-xl cursor-pointer transition-all ${
                                selectedAddressId === address.id
                                  ? "border-green-600 bg-green-50"
                                  : "border-gray-200 hover:border-gray-300"
                              }`}
                            >
                              <input
                                type="radio"
                                name="selectedAddress"
                                value={address.id}
                                checked={selectedAddressId === address.id}
                                onChange={() => handleAddressSelect(address.id)}
                                className="sr-only"
                              />
                              <div
                                className={`w-6 h-6 rounded-full border-2 mr-4 mt-1 flex items-center justify-center ${
                                  selectedAddressId === address.id
                                    ? "border-green-600"
                                    : "border-gray-300"
                                }`}
                              >
                                {selectedAddressId === address.id && (
                                  <div className="w-3.5 h-3.5 rounded-full bg-green-600"></div>
                                )}
                              </div>
                              <div className="flex-1">
                                <div className="flex items-center space-x-3 mb-2">
                                  <span className="text-lg font-medium text-gray-800">
                                    {address.firstName} {address.lastName}
                                  </span>
                                  {address.isDefault && (
                                    <span className="px-3 py-1 bg-green-100 text-green-700 text-sm font-medium rounded-full">
                                      Default
                                    </span>
                                  )}
                                </div>
                                <p className="text-gray-600 mb-1">
                                  {address.address1}
                                  {address.address2 && `, ${address.address2}`}
                                </p>
                                <p className="text-gray-600 mb-1">
                                  {address.city}, {address.state}{" "}
                                  {address.postalCode}
                                </p>
                                {address.phone && (
                                  <p className="text-gray-600">
                                    {address.phone}
                                  </p>
                                )}
                              </div>
                            </label>
                          ))}
                        </div>
                      </div>
                    )}

                  {/* Manual Address Entry */}
                  {(status !== "authenticated" ||
                    userAddresses.length === 0 ||
                    isEditingAddress) && (
                    <>
                      <div className="flex items-center justify-between">
                        <h2 className="text-2xl font-semibold text-gray-800 mb-6">
                          {status === "authenticated" &&
                          userAddresses.length > 0
                            ? "Edit Shipping Details"
                            : "Shipping Information"}
                        </h2>
                        {status === "authenticated" &&
                          userAddresses.length > 0 &&
                          isEditingAddress && (
                            <button
                              onClick={() => setIsEditingAddress(false)}
                              className="text-gray-600 hover:text-gray-800 font-medium"
                            >
                              Use Saved Address
                            </button>
                          )}
                      </div>

                      {loadingUserData && (
                        <div className="flex items-center justify-center py-8">
                          <div className="w-8 h-8 border-2 border-green-600 border-t-transparent rounded-full animate-spin"></div>
                          <span className="ml-3 text-gray-600">
                            Loading your details...
                          </span>
                        </div>
                      )}

                      {!loadingUserData && (
                        <>
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                First Name
                              </label>
                              <input
                                type="text"
                                name="firstName"
                                value={formData.firstName}
                                onChange={handleInputChange}
                                className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${
                                  errors.firstName
                                    ? "border-red-500"
                                    : "border-gray-300"
                                }`}
                              />
                              {errors.firstName && (
                                <p className="text-red-500 text-sm mt-1">
                                  {errors.firstName}
                                </p>
                              )}
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Last Name
                              </label>
                              <input
                                type="text"
                                name="lastName"
                                value={formData.lastName}
                                onChange={handleInputChange}
                                className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${
                                  errors.lastName
                                    ? "border-red-500"
                                    : "border-gray-300"
                                }`}
                              />
                              {errors.lastName && (
                                <p className="text-red-500 text-sm mt-1">
                                  {errors.lastName}
                                </p>
                              )}
                            </div>
                          </div>

                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Email
                              </label>
                              <input
                                type="email"
                                name="email"
                                value={formData.email}
                                onChange={handleInputChange}
                                className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${
                                  errors.email
                                    ? "border-red-500"
                                    : "border-gray-300"
                                }`}
                              />
                              {errors.email && (
                                <p className="text-red-500 text-sm mt-1">
                                  {errors.email}
                                </p>
                              )}
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Phone
                              </label>
                              <input
                                type="tel"
                                name="phone"
                                value={formData.phone}
                                onChange={handleInputChange}
                                className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${
                                  errors.phone
                                    ? "border-red-500"
                                    : "border-gray-300"
                                }`}
                              />
                              {errors.phone && (
                                <p className="text-red-500 text-sm mt-1">
                                  {errors.phone}
                                </p>
                              )}
                            </div>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Address
                            </label>
                            <input
                              type="text"
                              name="address"
                              value={formData.address}
                              onChange={handleInputChange}
                              className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${
                                errors.address
                                  ? "border-red-500"
                                  : "border-gray-300"
                              }`}
                            />
                            {errors.address && (
                              <p className="text-red-500 text-sm mt-1">
                                {errors.address}
                              </p>
                            )}
                          </div>

                          <div className="grid grid-cols-3 gap-6">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                City
                              </label>
                              <input
                                type="text"
                                name="city"
                                value={formData.city}
                                onChange={handleInputChange}
                                className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${
                                  errors.city
                                    ? "border-red-500"
                                    : "border-gray-300"
                                }`}
                              />
                              {errors.city && (
                                <p className="text-red-500 text-sm mt-1">
                                  {errors.city}
                                </p>
                              )}
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                State
                              </label>
                              <input
                                type="text"
                                name="state"
                                value={formData.state}
                                onChange={handleInputChange}
                                className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${
                                  errors.state
                                    ? "border-red-500"
                                    : "border-gray-300"
                                }`}
                              />
                              {errors.state && (
                                <p className="text-red-500 text-sm mt-1">
                                  {errors.state}
                                </p>
                              )}
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                ZIP Code
                              </label>
                              <input
                                type="text"
                                name="zipCode"
                                value={formData.zipCode}
                                onChange={handleInputChange}
                                className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 ${
                                  errors.zipCode
                                    ? "border-red-500"
                                    : "border-gray-300"
                                }`}
                              />
                              {errors.zipCode && (
                                <p className="text-red-500 text-sm mt-1">
                                  {errors.zipCode}
                                </p>
                              )}
                            </div>
                          </div>
                        </>
                      )}
                    </>
                  )}
                </div>
              )}

              {/* Step 2: Payment Information */}
              {currentStep === 2 && (
                <div className="space-y-6">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-6">
                    Payment Method
                  </h2>

                  <div className="space-y-4">
                    {/* Online Payment Option */}
                    <label
                      className={`flex items-center p-6 border-2 rounded-xl cursor-pointer transition-all ${
                        formData.paymentMethod === "online"
                          ? "border-green-600 bg-green-50"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                    >
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="online"
                        checked={formData.paymentMethod === "online"}
                        onChange={handleInputChange}
                        className="sr-only"
                      />
                      <div
                        className={`w-6 h-6 rounded-full border-2 mr-4 flex items-center justify-center ${
                          formData.paymentMethod === "online"
                            ? "border-green-600"
                            : "border-gray-300"
                        }`}
                      >
                        {formData.paymentMethod === "online" && (
                          <div className="w-3.5 h-3.5 rounded-full bg-green-600"></div>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <Wallet className="w-6 h-6 text-gray-600" />
                          <span className="text-lg font-medium text-gray-800">
                            Online Payment
                          </span>
                        </div>
                        <p className="text-gray-600 mt-2">
                          Pay securely using Credit Card, Debit Card, UPI, Net
                          Banking, or Digital Wallets
                        </p>
                        <div className="flex flex-wrap gap-2 mt-3">
                          <span className="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm">
                            Credit Card
                          </span>
                          <span className="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm">
                            Debit Card
                          </span>
                          <span className="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm">
                            UPI
                          </span>
                          <span className="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm">
                            Net Banking
                          </span>
                        </div>
                      </div>
                    </label>

                    {/* Cash on Delivery Option */}
                    <label
                      className={`flex items-center p-6 border-2 rounded-xl cursor-pointer transition-all ${
                        formData.paymentMethod === "cod"
                          ? "border-green-600 bg-green-50"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                    >
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="cod"
                        checked={formData.paymentMethod === "cod"}
                        onChange={handleInputChange}
                        className="sr-only"
                      />
                      <div
                        className={`w-6 h-6 rounded-full border-2 mr-4 flex items-center justify-center ${
                          formData.paymentMethod === "cod"
                            ? "border-green-600"
                            : "border-gray-300"
                        }`}
                      >
                        {formData.paymentMethod === "cod" && (
                          <div className="w-3.5 h-3.5 rounded-full bg-green-600"></div>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <Banknote className="w-6 h-6 text-gray-600" />
                          <span className="text-lg font-medium text-gray-800">
                            Cash on Delivery
                          </span>
                        </div>
                        <p className="text-gray-600 mt-2">
                          Pay when your order is delivered to your doorstep
                        </p>
                        <p className="text-sm text-yellow-700 mt-2 bg-yellow-50 px-3 py-1 rounded-lg inline-block">
                          ₹50 additional COD charges apply
                        </p>
                      </div>
                    </label>
                  </div>

                  {errors.paymentMethod && (
                    <p className="text-red-500 text-sm mt-2">
                      {errors.paymentMethod}
                    </p>
                  )}

                  {/* Additional info for online payment */}
                  {formData.paymentMethod === "online" && (
                    <div className="mt-6 p-6 bg-blue-50 rounded-xl">
                      <h3 className="font-medium text-blue-900 mb-2">
                        Secure Payment Gateway
                      </h3>
                      <p className="text-blue-800">
                        You will be redirected to our secure payment partner
                        after reviewing your order. All major payment methods
                        are accepted.
                      </p>
                    </div>
                  )}

                  {/* Additional info for COD */}
                  {formData.paymentMethod === "cod" && (
                    <div className="mt-6 p-6 bg-yellow-50 rounded-xl">
                      <h3 className="font-medium text-yellow-900 mb-2">
                        Cash on Delivery Guidelines
                      </h3>
                      <ul className="text-yellow-800 space-y-1 list-disc list-inside">
                        <li>
                          Please keep exact change ready for smooth delivery
                        </li>
                        <li>Additional ₹50 will be charged for COD orders</li>
                        <li>Order cannot be cancelled once out for delivery</li>
                      </ul>
                    </div>
                  )}
                </div>
              )}

              {/* Step 3: Review Order */}
              {currentStep === 3 && (
                <div className="space-y-8">
                  <h2 className="text-2xl font-semibold text-gray-800">
                    Review Your Order
                  </h2>

                  {/* Shipping Info */}
                  <div className="bg-gray-50 rounded-xl p-6">
                    <h3 className="font-semibold text-gray-800 mb-4">
                      Shipping Address
                    </h3>
                    <p className="text-gray-600">
                      {formData.firstName} {formData.lastName}
                      <br />
                      {formData.address}
                      <br />
                      {formData.city}, {formData.state} {formData.zipCode}
                    </p>
                  </div>

                  {/* Payment Info */}
                  <div className="bg-gray-50 rounded-xl p-6">
                    <h3 className="font-semibold text-gray-800 mb-4">
                      Payment Method
                    </h3>
                    <p className="text-gray-600">
                      {formData.paymentMethod === "online" ? (
                        <>
                          <Wallet className="w-5 h-5 inline mr-2" />
                          Online Payment - Secure Gateway
                        </>
                      ) : (
                        <>
                          <Banknote className="w-5 h-5 inline mr-2" />
                          Cash on Delivery (COD)
                        </>
                      )}
                    </p>
                  </div>

                  {/* Order Items */}
                  <div className="space-y-4">
                    <h3 className="font-semibold text-gray-800">Order Items</h3>
                    {state.items.map((item) => (
                      <div
                        key={item.product.id}
                        className="flex items-center space-x-6 p-4 bg-gray-50 rounded-xl"
                      >
                        <div className="w-20 h-20 bg-gray-200 rounded-xl"></div>
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-800">
                            {item.product.name}
                          </h4>
                          <p className="text-gray-600">
                            Quantity: {item.quantity}
                          </p>
                        </div>
                        <span className="font-semibold text-gray-900">
                          ₹{(item.product.price * item.quantity).toFixed(2)}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Navigation Buttons */}
              <div className="flex justify-between mt-12 pt-8 border-t">
                {currentStep > 1 && (
                  <button
                    onClick={prevStep}
                    className="flex items-center space-x-2 px-8 py-4 border border-gray-300 rounded-xl font-medium text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    <ArrowLeft className="w-5 h-5" />
                    <span>Back</span>
                  </button>
                )}

                <div className="ml-auto">
                  {currentStep < 3 ? (
                    <button
                      onClick={nextStep}
                      className="flex items-center space-x-2 bg-green-600 text-white px-8 py-4 rounded-xl font-semibold hover:bg-green-700 transition-colors text-lg"
                    >
                      <span>Continue</span>
                      <ArrowRight className="w-5 h-5" />
                    </button>
                  ) : (
                    <div className="space-y-3">
                      {formData.paymentMethod === "online" ? (
                        <PaymentButton
                          cartItems={state.items.map((item) => ({
                            productId: item.product.id,
                            quantity: item.quantity,
                            price: item.product.price,
                            product: {
                              name: item.product.name,
                              price: item.product.price,
                            },
                          }))}
                          shippingAddress={{
                            firstName: formData.firstName,
                            lastName: formData.lastName,
                            address1: formData.address,
                            address2: "",
                            city: formData.city,
                            state: formData.state,
                            postalCode: formData.zipCode,
                            country: formData.country,
                            phone: formData.phone,
                          }}
                          totalAmount={state.finalTotal}
                          appliedCoupons={[]}
                          flashSaleDiscount={state.flashSaleDiscount}
                          onSuccess={handlePaymentSuccess}
                          onError={handlePaymentError}
                          disabled={isProcessing}
                          className="rounded-xl text-lg px-8 py-4"
                        />
                      ) : (
                        <button
                          onClick={handleCODOrder}
                          disabled={isProcessing}
                          className="flex items-center justify-center space-x-2 bg-green-600 text-white px-8 py-4 rounded-xl font-semibold hover:bg-green-700 transition-colors disabled:opacity-50 text-lg w-full"
                        >
                          {isProcessing ? (
                            <>
                              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                              <span>Processing...</span>
                            </>
                          ) : (
                            <>
                              <span>Place COD Order</span>
                              <ArrowRight className="w-5 h-5" />
                            </>
                          )}
                        </button>
                      )}
                      {errors.payment && (
                        <p className="text-red-500 text-sm text-center">
                          {errors.payment}
                        </p>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Order Summary Sidebar */}
            <div className="col-span-1">
              <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 sticky top-24">
                <h3 className="text-xl font-semibold text-gray-800 mb-6">
                  Order Summary
                </h3>

                <div className="space-y-4 mb-6">
                  {state.items.map((item) => (
                    <div
                      key={item.product.id}
                      className="flex items-center space-x-3"
                    >
                      <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-gray-800 text-sm line-clamp-1">
                          {item.product.name}
                        </p>
                        <p className="text-gray-500 text-xs">
                          Qty: {item.quantity}
                        </p>
                      </div>
                      <span className="font-medium text-gray-900 text-sm">
                        ₹{(item.product.price * item.quantity).toFixed(2)}
                      </span>
                    </div>
                  ))}
                </div>

                <div className="space-y-3 mb-6 pt-4 border-t border-gray-200">
                  <div className="flex justify-between text-gray-600">
                    <span>Subtotal</span>
                    <span>₹{state.subtotal.toFixed(2)}</span>
                  </div>

                  {state.flashSaleDiscount > 0 && (
                    <div className="flex justify-between text-red-600">
                      <span>
                        Flash Sale Discount ({state.flashSalePercentage}% OFF)
                      </span>
                      <span>-₹{state.flashSaleDiscount.toFixed(2)}</span>
                    </div>
                  )}

                  {state.coupons.totalDiscount > 0 && (
                    <div className="flex justify-between text-green-600">
                      <span>Coupon Discount</span>
                      <span>-₹{state.coupons.totalDiscount.toFixed(2)}</span>
                    </div>
                  )}

                  <div className="flex justify-between text-gray-600">
                    <span>Shipping</span>
                    <span className="text-green-600 font-medium">Free</span>
                  </div>

                  {formData.paymentMethod === "cod" && (
                    <div className="flex justify-between text-gray-600">
                      <span>COD Charges</span>
                      <span>₹{codCharges.toFixed(2)}</span>
                    </div>
                  )}

                  <div className="border-t border-gray-200 pt-3">
                    <div className="flex justify-between font-bold text-gray-900 text-lg">
                      <span>Total</span>
                      <span>₹{displayTotal.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Checkout;
