"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  Upload,
  Search,
  Filter,
  Download,
  Trash2,
  Eye,
  Copy,
  FolderPlus,
  Image as ImageIcon,
  Video,
  File,
  Grid3X3,
  List,
  RefreshCw,
  X,
  Check,
  AlertCircle,
} from "lucide-react";
import { formatFileSize } from "../../lib/r2";

interface MediaFile {
  key: string;
  name: string;
  size: number;
  type: string;
  url: string;
  lastModified: Date;
  folder?: string;
}

const MediaPage = () => {
  const [files, setFiles] = useState<MediaFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [filterType, setFilterType] = useState<
    "all" | "image" | "video" | "document"
  >("all");
  const [selectedFolder, setSelectedFolder] = useState<string>("all");
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showPreview, setShowPreview] = useState<MediaFile | null>(null);
  const [notification, setNotification] = useState<{
    type: "success" | "error";
    message: string;
  } | null>(null);

  // Load files from API
  const loadFiles = useCallback(async () => {
    try {
      setLoading(true);
      const folder = selectedFolder === "all" ? undefined : selectedFolder;
      const url = `/api/media/list${folder ? `?folder=${folder}` : ""}`;

      console.log("Loading files from:", url);
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setFiles(data.files || []);
        console.log(`Loaded ${data.files?.length || 0} files`);
      } else {
        throw new Error(data.error || "Failed to load files");
      }
    } catch (error) {
      console.error("Error loading files:", error);
      showNotification(
        "error",
        error instanceof Error ? error.message : "Failed to load files",
      );
      setFiles([]);
    } finally {
      setLoading(false);
    }
  }, [selectedFolder]);

  useEffect(() => {
    loadFiles();
  }, [loadFiles]);

  // Show notification
  const showNotification = (type: "success" | "error", message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 3000);
  };

  // Handle file upload
  const handleFileUpload = async (files: FileList) => {
    setUploading(true);
    console.log(`Starting upload of ${files.length} files`);

    const uploadPromises = Array.from(files).map(async (file) => {
      const formData = new FormData();
      formData.append("file", file);
      formData.append(
        "folder",
        selectedFolder === "all" ? "uploads" : selectedFolder,
      );

      try {
        console.log(`Uploading file: ${file.name} (${file.size} bytes)`);
        const response = await fetch("/api/media/upload", {
          method: "POST",
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log(`Upload result for ${file.name}:`, result);
        return result;
      } catch (error) {
        console.error(`Upload failed for ${file.name}:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : "Upload failed",
        };
      }
    });

    const results = await Promise.all(uploadPromises);
    const successful = results.filter((r) => r.success).length;
    const failed = results.filter((r) => !r.success).length;

    if (successful > 0) {
      showNotification(
        "success",
        `${successful} file(s) uploaded successfully`,
      );
      loadFiles();
    }

    if (failed > 0) {
      showNotification("error", `${failed} file(s) failed to upload`);
    }

    setUploading(false);
    setShowUploadModal(false);
  };

  // Handle file deletion
  const handleDeleteFiles = async (keys: string[]) => {
    if (!confirm(`Are you sure you want to delete ${keys.length} file(s)?`))
      return;

    try {
      console.log("Deleting files:", keys);

      const deletePromises = keys.map(async (key) => {
        const response = await fetch("/api/media/delete", {
          method: "DELETE",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ key }),
        });

        if (!response.ok) {
          throw new Error(`Failed to delete ${key}: ${response.status}`);
        }

        const result = await response.json();
        if (!result.success) {
          throw new Error(`Failed to delete ${key}: ${result.error}`);
        }

        return result;
      });

      await Promise.all(deletePromises);
      showNotification(
        "success",
        `${keys.length} file(s) deleted successfully`,
      );
      setSelectedFiles([]);
      loadFiles();
    } catch (error) {
      console.error("Delete error:", error);
      showNotification(
        "error",
        error instanceof Error ? error.message : "Failed to delete files",
      );
    }
  };

  // Copy URL to clipboard
  const copyToClipboard = (url: string) => {
    navigator.clipboard.writeText(url);
    showNotification("success", "URL copied to clipboard");
  };

  // Download file
  const downloadFile = (file: MediaFile) => {
    const link = document.createElement("a");
    link.href = file.url;
    link.download = file.name;
    link.target = "_blank";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    showNotification("success", "Download started");
  };

  // Filter files
  const filteredFiles = files.filter((file) => {
    const matchesSearch = file.name
      .toLowerCase()
      .includes(searchTerm.toLowerCase());

    // Fix filter type matching
    let matchesType = true;
    if (filterType !== "all") {
      if (filterType === "document") {
        matchesType = file.type === "document" || file.type === "other";
      } else {
        matchesType = file.type === filterType;
      }
    }

    return matchesSearch && matchesType;
  });

  // Get unique folders
  const folders = [
    "all",
    ...Array.from(new Set(files.map((f) => f.folder).filter(Boolean))),
  ];

  // Get file icon
  const getFileIcon = (type: string) => {
    switch (type) {
      case "image":
        return <ImageIcon className="w-5 h-5" />;
      case "video":
        return <Video className="w-5 h-5" />;
      default:
        return <File className="w-5 h-5" />;
    }
  };

  // Upload Modal Component
  const UploadModal = () => {
    const [dragOver, setDragOver] = useState(false);

    const handleDrop = (e: React.DragEvent) => {
      e.preventDefault();
      setDragOver(false);
      if (e.dataTransfer.files) {
        handleFileUpload(e.dataTransfer.files);
      }
    };

    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files) {
        handleFileUpload(e.target.files);
      }
    };

    if (!showUploadModal) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl p-6 w-full max-w-md mx-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">
              Upload Files
            </h3>
            <button
              onClick={() => setShowUploadModal(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragOver ? "border-green-500 bg-green-50" : "border-gray-300"
            }`}
            onDragOver={(e) => {
              e.preventDefault();
              setDragOver(true);
            }}
            onDragLeave={() => setDragOver(false)}
            onDrop={handleDrop}
          >
            <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-4">
              Drag and drop files here, or click to select
            </p>
            <input
              type="file"
              multiple
              onChange={handleFileSelect}
              className="hidden"
              id="file-upload"
              accept="image/*,video/*,.pdf"
            />
            <label
              htmlFor="file-upload"
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors cursor-pointer"
            >
              Select Files
            </label>
          </div>

          <p className="text-sm text-gray-500 mt-4">
            Supported formats: Images, Videos, PDFs (Max 10MB each)
          </p>
        </div>
      </div>
    );
  };

  // Preview Modal Component
  const PreviewModal = () => {
    if (!showPreview) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-auto">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">
              {showPreview.name}
            </h3>
            <button
              onClick={() => setShowPreview(null)}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <div className="mb-4">
            {showPreview.type === "image" ? (
              <img
                src={showPreview.url}
                alt={showPreview.name}
                className="max-w-full h-auto rounded-lg"
              />
            ) : showPreview.type === "video" ? (
              <video
                src={showPreview.url}
                controls
                className="max-w-full h-auto rounded-lg"
              />
            ) : (
              <div className="flex items-center justify-center h-64 bg-gray-100 rounded-lg">
                <File className="w-16 h-16 text-gray-400" />
              </div>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Size:</span>
              <span className="ml-2 text-gray-600">
                {formatFileSize(showPreview.size)}
              </span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Type:</span>
              <span className="ml-2 text-gray-600">{showPreview.type}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Modified:</span>
              <span className="ml-2 text-gray-600">
                {new Date(showPreview.lastModified).toLocaleDateString()}
              </span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Folder:</span>
              <span className="ml-2 text-gray-600">
                {showPreview.folder || "Root"}
              </span>
            </div>
          </div>

          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <span className="font-medium text-gray-700">URL:</span>
            <div className="flex items-center mt-1">
              <input
                type="text"
                value={showPreview.url}
                readOnly
                className="flex-1 px-3 py-1 text-sm border border-gray-300 rounded mr-2"
              />
              <button
                onClick={() => copyToClipboard(showPreview.url)}
                className="p-1 text-green-600 hover:text-green-700"
              >
                <Copy className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div>
      {/* Notification */}
      {notification && (
        <div
          className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg flex items-center ${
            notification.type === "success"
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          {notification.type === "success" ? (
            <Check className="w-5 h-5 mr-2" />
          ) : (
            <AlertCircle className="w-5 h-5 mr-2" />
          )}
          {notification.message}
        </div>
      )}

      {/* Header */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Media Library</h1>
            <p className="text-gray-600 mt-2">
              Manage your images, videos, and documents
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex items-center space-x-3">
            <button
              onClick={() => loadFiles()}
              disabled={loading}
              className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <RefreshCw
                className={`w-5 h-5 text-gray-600 ${loading ? "animate-spin" : ""}`}
              />
            </button>
            <button
              onClick={() => setShowUploadModal(true)}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center"
            >
              <Upload className="w-5 h-5 mr-2" />
              Upload Files
            </button>
          </div>
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div className="flex flex-col sm:flex-row sm:items-center gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search files..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            {/* Folder Filter */}
            <select
              value={selectedFolder}
              onChange={(e) => setSelectedFolder(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              {folders.map((folder) => (
                <option key={folder} value={folder}>
                  {folder === "all" ? "All Folders" : folder}
                </option>
              ))}
            </select>

            {/* Type Filter */}
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <option value="all">All Types</option>
              <option value="image">Images</option>
              <option value="video">Videos</option>
              <option value="document">Documents</option>
            </select>
          </div>

          <div className="flex items-center space-x-3">
            {/* View Mode Toggle */}
            <div className="flex border border-gray-300 rounded-lg">
              <button
                onClick={() => setViewMode("grid")}
                className={`p-2 ${viewMode === "grid" ? "bg-green-600 text-white" : "text-gray-600 hover:bg-gray-50"}`}
              >
                <Grid3X3 className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode("list")}
                className={`p-2 ${viewMode === "list" ? "bg-green-600 text-white" : "text-gray-600 hover:bg-gray-50"}`}
              >
                <List className="w-4 h-4" />
              </button>
            </div>

            {/* Bulk Actions */}
            {selectedFiles.length > 0 && (
              <button
                onClick={() => handleDeleteFiles(selectedFiles)}
                className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
              >
                <Trash2 className="w-5 h-5" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Files Display */}
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="w-8 h-8 text-gray-400 animate-spin" />
        </div>
      ) : filteredFiles.length === 0 ? (
        <div className="text-center py-12">
          <ImageIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500 text-lg">No files found</p>
          <p className="text-gray-400">Upload some files to get started</p>
        </div>
      ) : viewMode === "grid" ? (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {filteredFiles.map((file) => (
            <div
              key={file.key}
              className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow"
            >
              <div className="relative">
                <input
                  type="checkbox"
                  checked={selectedFiles.includes(file.key)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedFiles([...selectedFiles, file.key]);
                    } else {
                      setSelectedFiles(
                        selectedFiles.filter((k) => k !== file.key),
                      );
                    }
                  }}
                  className="absolute top-2 left-2 rounded border-gray-300 text-green-600 focus:ring-green-500 z-10"
                />

                <div className="aspect-square bg-gray-100 rounded-lg mb-3 flex items-center justify-center overflow-hidden">
                  {file.type === "image" ? (
                    <img
                      src={file.url}
                      alt={file.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="text-gray-400">
                      {getFileIcon(file.type)}
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-1">
                <p
                  className="text-sm font-medium text-gray-900 truncate"
                  title={file.name}
                >
                  {file.name}
                </p>
                <p className="text-xs text-gray-500">
                  {formatFileSize(file.size)}
                </p>
              </div>

              <div className="flex items-center justify-between mt-3">
                <button
                  onClick={() => setShowPreview(file)}
                  className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                  title="Preview"
                >
                  <Eye className="w-4 h-4" />
                </button>
                <button
                  onClick={() => downloadFile(file)}
                  className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                  title="Download"
                >
                  <Download className="w-4 h-4" />
                </button>
                <button
                  onClick={() => copyToClipboard(file.url)}
                  className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                  title="Copy URL"
                >
                  <Copy className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleDeleteFiles([file.key])}
                  className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                  title="Delete"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={
                        selectedFiles.length === filteredFiles.length &&
                        filteredFiles.length > 0
                      }
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedFiles(filteredFiles.map((f) => f.key));
                        } else {
                          setSelectedFiles([]);
                        }
                      }}
                      className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    File
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Size
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Modified
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredFiles.map((file) => (
                  <tr key={file.key} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <input
                        type="checkbox"
                        checked={selectedFiles.includes(file.key)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedFiles([...selectedFiles, file.key]);
                          } else {
                            setSelectedFiles(
                              selectedFiles.filter((k) => k !== file.key),
                            );
                          }
                        }}
                        className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                      />
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                          {file.type === "image" ? (
                            <img
                              src={file.url}
                              alt={file.name}
                              className="w-full h-full object-cover rounded-lg"
                            />
                          ) : (
                            <div className="text-gray-400">
                              {getFileIcon(file.type)}
                            </div>
                          )}
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {file.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {file.folder || "Root"}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900 capitalize">
                      {file.type}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {formatFileSize(file.size)}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      {new Date(file.lastModified).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => setShowPreview(file)}
                          className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                          title="Preview"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => downloadFile(file)}
                          className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                          title="Download"
                        >
                          <Download className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => copyToClipboard(file.url)}
                          className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                          title="Copy URL"
                        >
                          <Copy className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteFiles([file.key])}
                          className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                          title="Delete"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Stats */}
      <div className="mt-6 text-sm text-gray-500">
        Showing {filteredFiles.length} of {files.length} files
        {selectedFiles.length > 0 && ` • ${selectedFiles.length} selected`}
      </div>

      {/* Modals */}
      <UploadModal />
      <PreviewModal />
    </div>
  );
};

export default MediaPage;
