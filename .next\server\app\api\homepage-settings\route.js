"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/homepage-settings/route";
exports.ids = ["app/api/homepage-settings/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fhomepage-settings%2Froute&page=%2Fapi%2Fhomepage-settings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhomepage-settings%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fhomepage-settings%2Froute&page=%2Fapi%2Fhomepage-settings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhomepage-settings%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_project_app_api_homepage_settings_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/homepage-settings/route.ts */ \"(rsc)/./app/api/homepage-settings/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/homepage-settings/route\",\n        pathname: \"/api/homepage-settings\",\n        filename: \"route\",\n        bundlePath: \"app/api/homepage-settings/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\api\\\\homepage-settings\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_project_app_api_homepage_settings_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/homepage-settings/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fhomepage-settings%2Froute&page=%2Fapi%2Fhomepage-settings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhomepage-settings%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/homepage-settings/route.ts":
/*!********************************************!*\
  !*** ./app/api/homepage-settings/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _app_lib_mongoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/mongoose */ \"(rsc)/./app/lib/mongoose.ts\");\n/* harmony import */ var _app_lib_models__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/models */ \"(rsc)/./app/lib/models.ts\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_lib_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/auth */ \"(rsc)/./app/lib/auth.ts\");\n\n\n\n\n\n// GET /api/homepage-settings - Get homepage settings\nasync function GET() {\n    try {\n        await (0,_app_lib_mongoose__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        // Get homepage settings\n        const settings = await _app_lib_models__WEBPACK_IMPORTED_MODULE_2__.HomepageSetting.find().sort({\n            createdAt: -1\n        });\n        const currentSettings = settings.length > 0 ? settings[0] : null;\n        // Get categories with product counts\n        const categories = await _app_lib_models__WEBPACK_IMPORTED_MODULE_2__.Category.aggregate([\n            {\n                $lookup: {\n                    from: \"products\",\n                    localField: \"_id\",\n                    foreignField: \"categoryIds\",\n                    as: \"products\"\n                }\n            },\n            {\n                $addFields: {\n                    \"_count\": {\n                        \"products\": {\n                            $size: \"$products\"\n                        }\n                    }\n                }\n            },\n            {\n                $limit: 6\n            }\n        ]);\n        // Get featured product (first active product or by settings)\n        let featuredProduct = null;\n        if (currentSettings?.productOfTheMonthId) {\n            featuredProduct = await _app_lib_models__WEBPACK_IMPORTED_MODULE_2__.Product.findById(currentSettings.productOfTheMonthId).where({\n                isActive: true\n            }).lean();\n        }\n        if (!featuredProduct) {\n            featuredProduct = await _app_lib_models__WEBPACK_IMPORTED_MODULE_2__.Product.findOne({\n                isActive: true,\n                isFeatured: true\n            }).sort({\n                createdAt: -1\n            }).lean();\n        }\n        // Get bestsellers (top 4 active products)\n        const bestsellers = await _app_lib_models__WEBPACK_IMPORTED_MODULE_2__.Product.find({\n            isActive: true\n        }).sort({\n            createdAt: -1\n        }).limit(4).lean();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                settings: currentSettings,\n                featuredProduct,\n                bestsellers,\n                categories\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching homepage settings:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to fetch homepage settings\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/homepage-settings - Create or update homepage settings\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_3__.getServerSession)(_app_lib_auth__WEBPACK_IMPORTED_MODULE_4__.authOptions);\n        // Check if user is admin\n        if (!session || session.user.role !== \"ADMIN\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { // Hero Section\n        heroTitle, heroSubtitle, heroCtaText, heroCtaLink, heroSecondaryCtaText, heroSecondaryCtaLink, heroBadgeText, heroBackgroundColor, showHero, // Hero Trust Indicators\n        trustIndicator1Value, trustIndicator1Label, trustIndicator2Value, trustIndicator2Label, trustIndicator3Value, trustIndicator3Label, trustIndicator4Value, trustIndicator4Label, // Product of the Month\n        productOfTheMonthId, showProductOfMonth, // Promotional Banner\n        bannerText, bannerCtaText, bannerCtaLink, bannerBackgroundColor, showBanner, // Sections\n        showCategories, productSectionBgColor, bestsellerIds, showBestsellers, // Newsletter\n        newsletterTitle, newsletterSubtitle, showNewsletter, // Trust Badges\n        showTrustBadges, // Flash Sale Section\n        flashSaleTitle, flashSaleSubtitle, flashSaleEndDate, flashSaleBackgroundColor, flashSalePercentage, showFlashSale, // Testimonials Section\n        testimonialsTitle, testimonialsSubtitle, testimonialsBackgroundColor, showTestimonials, isActive } = body;\n        await (0,_app_lib_mongoose__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        // Check if homepage settings already exist\n        let settings = await _app_lib_models__WEBPACK_IMPORTED_MODULE_2__.HomepageSetting.findOne({\n            id: \"homepage-settings\"\n        });\n        // Create or update homepage settings\n        if (settings) {\n            // Update existing settings\n            settings = await _app_lib_models__WEBPACK_IMPORTED_MODULE_2__.HomepageSetting.findOneAndUpdate({\n                id: \"homepage-settings\"\n            }, {\n                $set: {\n                    // Hero Section\n                    ...heroTitle !== undefined && {\n                        heroTitle\n                    },\n                    ...heroSubtitle !== undefined && {\n                        heroSubtitle\n                    },\n                    ...heroCtaText !== undefined && {\n                        heroCtaText\n                    },\n                    ...heroCtaLink !== undefined && {\n                        heroCtaLink\n                    },\n                    ...heroSecondaryCtaText !== undefined && {\n                        heroSecondaryCtaText\n                    },\n                    ...heroSecondaryCtaLink !== undefined && {\n                        heroSecondaryCtaLink\n                    },\n                    ...heroBadgeText !== undefined && {\n                        heroBadgeText\n                    },\n                    ...heroBackgroundColor !== undefined && {\n                        heroBackgroundColor\n                    },\n                    ...showHero !== undefined && {\n                        showHero\n                    },\n                    // Hero Trust Indicators\n                    ...trustIndicator1Value !== undefined && {\n                        trustIndicator1Value\n                    },\n                    ...trustIndicator1Label !== undefined && {\n                        trustIndicator1Label\n                    },\n                    ...trustIndicator2Value !== undefined && {\n                        trustIndicator2Value\n                    },\n                    ...trustIndicator2Label !== undefined && {\n                        trustIndicator2Label\n                    },\n                    ...trustIndicator3Value !== undefined && {\n                        trustIndicator3Value\n                    },\n                    ...trustIndicator3Label !== undefined && {\n                        trustIndicator3Label\n                    },\n                    ...trustIndicator4Value !== undefined && {\n                        trustIndicator4Value\n                    },\n                    ...trustIndicator4Label !== undefined && {\n                        trustIndicator4Label\n                    },\n                    // Product of the Month\n                    ...productOfTheMonthId !== undefined && {\n                        productOfTheMonthId\n                    },\n                    ...showProductOfMonth !== undefined && {\n                        showProductOfMonth\n                    },\n                    // Promotional Banner\n                    ...bannerText !== undefined && {\n                        bannerText\n                    },\n                    ...bannerCtaText !== undefined && {\n                        bannerCtaText\n                    },\n                    ...bannerCtaLink !== undefined && {\n                        bannerCtaLink\n                    },\n                    ...bannerBackgroundColor !== undefined && {\n                        bannerBackgroundColor\n                    },\n                    ...showBanner !== undefined && {\n                        showBanner\n                    },\n                    // Sections\n                    ...showCategories !== undefined && {\n                        showCategories\n                    },\n                    ...productSectionBgColor !== undefined && {\n                        productSectionBgColor\n                    },\n                    ...bestsellerIds !== undefined && {\n                        bestsellerIds\n                    },\n                    ...showBestsellers !== undefined && {\n                        showBestsellers\n                    },\n                    // Newsletter\n                    ...newsletterTitle !== undefined && {\n                        newsletterTitle\n                    },\n                    ...newsletterSubtitle !== undefined && {\n                        newsletterSubtitle\n                    },\n                    ...showNewsletter !== undefined && {\n                        showNewsletter\n                    },\n                    // Trust Badges\n                    ...showTrustBadges !== undefined && {\n                        showTrustBadges\n                    },\n                    // Flash Sale Section\n                    ...flashSaleTitle !== undefined && {\n                        flashSaleTitle\n                    },\n                    ...flashSaleSubtitle !== undefined && {\n                        flashSaleSubtitle\n                    },\n                    ...flashSaleEndDate !== undefined && {\n                        flashSaleEndDate: flashSaleEndDate ? new Date(flashSaleEndDate) : null\n                    },\n                    ...flashSaleBackgroundColor !== undefined && {\n                        flashSaleBackgroundColor\n                    },\n                    ...flashSalePercentage !== undefined && {\n                        flashSalePercentage\n                    },\n                    ...showFlashSale !== undefined && {\n                        showFlashSale\n                    },\n                    // Testimonials Section\n                    ...testimonialsTitle !== undefined && {\n                        testimonialsTitle\n                    },\n                    ...testimonialsSubtitle !== undefined && {\n                        testimonialsSubtitle\n                    },\n                    ...testimonialsBackgroundColor !== undefined && {\n                        testimonialsBackgroundColor\n                    },\n                    ...showTestimonials !== undefined && {\n                        showTestimonials\n                    },\n                    ...isActive !== undefined && {\n                        isActive\n                    },\n                    updatedAt: new Date()\n                }\n            }, {\n                new: true\n            });\n        } else {\n            // Create new settings\n            settings = await _app_lib_models__WEBPACK_IMPORTED_MODULE_2__.HomepageSetting.create({\n                id: \"homepage-settings\",\n                // Hero Section\n                heroTitle: heroTitle || \"Natural Skincare Essentials\",\n                heroSubtitle: heroSubtitle || \"Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin\",\n                heroCtaText: heroCtaText || \"Shop Collection\",\n                heroCtaLink: heroCtaLink || \"/shop\",\n                heroSecondaryCtaText: heroSecondaryCtaText || \"View Categories\",\n                heroSecondaryCtaLink: heroSecondaryCtaLink || \"/categories\",\n                heroBadgeText: heroBadgeText || \"New Collection\",\n                heroBackgroundColor: heroBackgroundColor || \"#f0fdf4\",\n                showHero: showHero !== undefined ? showHero : true,\n                // Hero Trust Indicators\n                trustIndicator1Value: trustIndicator1Value || \"100%\",\n                trustIndicator1Label: trustIndicator1Label || \"Natural\",\n                trustIndicator2Value: trustIndicator2Value || \"500+\",\n                trustIndicator2Label: trustIndicator2Label || \"Happy Customers\",\n                trustIndicator3Value: trustIndicator3Value || \"50+\",\n                trustIndicator3Label: trustIndicator3Label || \"Products\",\n                trustIndicator4Value: trustIndicator4Value || \"4.8★\",\n                trustIndicator4Label: trustIndicator4Label || \"Rating\",\n                // Product of the Month\n                productOfTheMonthId,\n                showProductOfMonth: showProductOfMonth !== undefined ? showProductOfMonth : true,\n                // Promotional Banner\n                bannerText,\n                bannerCtaText,\n                bannerCtaLink,\n                bannerBackgroundColor: bannerBackgroundColor || \"#22c55e\",\n                showBanner: showBanner !== undefined ? showBanner : true,\n                // Sections\n                showCategories: showCategories !== undefined ? showCategories : true,\n                productSectionBgColor: productSectionBgColor || \"#f0fdf4\",\n                bestsellerIds: bestsellerIds || [],\n                showBestsellers: showBestsellers !== undefined ? showBestsellers : true,\n                // Newsletter\n                newsletterTitle: newsletterTitle || \"Stay Updated\",\n                newsletterSubtitle: newsletterSubtitle || \"Get the latest updates on new products and exclusive offers\",\n                showNewsletter: showNewsletter !== undefined ? showNewsletter : true,\n                // Trust Badges\n                showTrustBadges: showTrustBadges !== undefined ? showTrustBadges : true,\n                // Flash Sale Section\n                flashSaleTitle: flashSaleTitle || \"Weekend Flash Sale\",\n                flashSaleSubtitle: flashSaleSubtitle || `Get ${flashSalePercentage ?? 25}% off all natural skincare products`,\n                flashSaleEndDate: flashSaleEndDate ? new Date(flashSaleEndDate) : null,\n                flashSaleBackgroundColor: flashSaleBackgroundColor || \"#16a34a\",\n                flashSalePercentage: flashSalePercentage ?? 25,\n                showFlashSale: showFlashSale !== undefined ? showFlashSale : true,\n                // Testimonials Section\n                testimonialsTitle: testimonialsTitle || \"What Our Customers Say\",\n                testimonialsSubtitle: testimonialsSubtitle || \"Real reviews from real customers who love our natural skincare\",\n                testimonialsBackgroundColor: testimonialsBackgroundColor || \"#f0fdf4\",\n                showTestimonials: showTestimonials !== undefined ? showTestimonials : true,\n                isActive: isActive !== undefined ? isActive : true\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: settings,\n            message: \"Homepage settings updated successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error updating homepage settings:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to update homepage settings\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/homepage-settings/route.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/auth.ts":
/*!*************************!*\
  !*** ./app/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _mongoose__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./mongoose */ \"(rsc)/./app/lib/mongoose.ts\");\n/* harmony import */ var _models__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./models */ \"(rsc)/./app/lib/models.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Remove PrismaAdapter when using JWT strategy\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                await (0,_mongoose__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n                const user = await _models__WEBPACK_IMPORTED_MODULE_4__.User.findOne({\n                    email: credentials.email\n                });\n                if (!user || !user.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2__.compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                return {\n                    id: user._id.toString(),\n                    email: user.email,\n                    name: user.name,\n                    role: user.role\n                };\n            }\n        })\n    ],\n    // Use JWT sessions, ensure cookie config aligns with NextAuth defaults\n    session: {\n        strategy: \"jwt\",\n        maxAge: 7 * 24 * 60 * 60,\n        updateAge: 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 7 * 24 * 60 * 60\n    },\n    cookies: {\n        sessionToken: {\n            name:  false ? 0 : `next-auth.session-token`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: \"development\" === \"production\",\n                domain:  false ? 0 : undefined\n            }\n        }\n    },\n    callbacks: {\n        async jwt ({ token, user, account, profile }) {\n            console.log(\"JWT callback called with:\", {\n                hasToken: !!token,\n                hasUser: !!user,\n                hasAccount: !!account,\n                accountProvider: account?.provider,\n                userId: user?.id,\n                userEmail: user?.email\n            });\n            // Handle initial sign in\n            if (account && user) {\n                // For OAuth providers, create/update user in database\n                if (account.provider === \"google\") {\n                    try {\n                        const email = user.email || profile?.email;\n                        if (!email) {\n                            throw new Error(\"No email found for Google account\");\n                        }\n                        await (0,_mongoose__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n                        // Check if user exists\n                        let dbUser = await _models__WEBPACK_IMPORTED_MODULE_4__.User.findOne({\n                            email\n                        });\n                        // Create user if doesn't exist\n                        if (!dbUser) {\n                            dbUser = await _models__WEBPACK_IMPORTED_MODULE_4__.User.create({\n                                email,\n                                name: user.name || profile?.name || email.split(\"@\")[0],\n                                // Don't save Google profile picture\n                                role: \"CUSTOMER\",\n                                emailVerified: new Date()\n                            });\n                        }\n                        if (dbUser) {\n                            // Set token properties\n                            token.sub = dbUser._id.toString();\n                            token.role = dbUser.role;\n                            token.email = dbUser.email;\n                            token.name = dbUser.name;\n                        }\n                    } catch (error) {\n                        console.error(\"Error handling Google sign in:\", error);\n                        throw error; // This will prevent sign in\n                    }\n                } else if (user) {\n                    // For credentials provider\n                    console.log(\"Processing credentials user:\", user);\n                    token.sub = user.id;\n                    token.role = user.role;\n                    token.email = user.email;\n                    token.name = user.name;\n                }\n            }\n            console.log(\"JWT token being returned:\", {\n                sub: token.sub,\n                email: token.email,\n                role: token.role,\n                name: token.name\n            });\n            return token;\n        },\n        async session ({ session, token }) {\n            // Populate session with user data from token\n            console.log(\"Session callback called with:\", {\n                hasSession: !!session,\n                hasToken: !!token,\n                tokenSub: token?.sub,\n                tokenRole: token?.role,\n                tokenEmail: token?.email,\n                tokenName: token?.name\n            });\n            if (token) {\n                session.user = {\n                    id: token.sub ?? \"\",\n                    role: token.role ?? \"CUSTOMER\",\n                    email: token.email ?? \"\",\n                    name: token.name ?? null,\n                    image: null\n                };\n                console.log(\"Session populated with:\", session.user);\n            }\n            console.log(\"Session being returned:\", session);\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Prefer explicit callbackUrl if provided, else dashboard/home depending on role via query\n            try {\n                const u = new URL(url, baseUrl);\n                // If NextAuth default callback cookie/param is present, respect relative paths\n                if (u.origin === \"null\") return baseUrl;\n                if (u.pathname.startsWith(\"/\")) return `${baseUrl}${u.pathname}${u.search}${u.hash}`;\n                if (u.origin === baseUrl) return u.toString();\n            } catch  {\n            // ignore parsing issues and fallback\n            }\n            return baseUrl;\n        }\n    },\n    events: {\n        async signIn ({ user, account, profile, isNewUser }) {\n            // Log sign in events in development only\n            if (true) {\n                console.log(\"Sign in event:\", {\n                    userId: user.id,\n                    email: user.email,\n                    provider: account?.provider,\n                    isNewUser\n                });\n            }\n        }\n    },\n    pages: {\n        signIn: \"/login\",\n        signOut: \"/\",\n        error: \"/login\"\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    // Ensure correct base URL to generate/validate callback & cookie domains\n    // NEXTAUTH_URL must match the browser origin, e.g. http://localhost:3000\n    // Enable debug in development to see what's happening\n    debug: \"development\" === \"development\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/models.ts":
/*!***************************!*\
  !*** ./app/lib/models.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Address: () => (/* binding */ Address),\n/* harmony export */   Category: () => (/* binding */ Category),\n/* harmony export */   Coupon: () => (/* binding */ Coupon),\n/* harmony export */   Enquiry: () => (/* binding */ Enquiry),\n/* harmony export */   HomepageSetting: () => (/* binding */ HomepageSetting),\n/* harmony export */   Newsletter: () => (/* binding */ Newsletter),\n/* harmony export */   Notification: () => (/* binding */ Notification),\n/* harmony export */   NotificationTemplate: () => (/* binding */ NotificationTemplate),\n/* harmony export */   Order: () => (/* binding */ Order),\n/* harmony export */   OrderItem: () => (/* binding */ OrderItem),\n/* harmony export */   Product: () => (/* binding */ Product),\n/* harmony export */   ProductCategory: () => (/* binding */ ProductCategory),\n/* harmony export */   ProductImage: () => (/* binding */ ProductImage),\n/* harmony export */   ProductVariant: () => (/* binding */ ProductVariant),\n/* harmony export */   Review: () => (/* binding */ Review),\n/* harmony export */   Testimonial: () => (/* binding */ Testimonial),\n/* harmony export */   User: () => (/* binding */ User),\n/* harmony export */   Wishlist: () => (/* binding */ Wishlist),\n/* harmony export */   WishlistItem: () => (/* binding */ WishlistItem)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst UserSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    email: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    name: String,\n    phone: String,\n    avatar: String,\n    password: String,\n    role: {\n        type: String,\n        enum: [\n            \"ADMIN\",\n            \"CUSTOMER\"\n        ],\n        default: \"CUSTOMER\"\n    },\n    emailVerified: Date,\n    resetToken: String,\n    resetTokenExpiry: Date\n}, {\n    timestamps: true,\n    collection: \"user\"\n});\nconst ProductSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: true\n    },\n    slug: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    description: String,\n    shortDescription: String,\n    comparePrice: Number,\n    costPrice: Number,\n    weight: Number,\n    dimensions: String,\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    isFeatured: {\n        type: Boolean,\n        default: false\n    },\n    metaTitle: String,\n    metaDescription: String,\n    categoryId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Category\"\n    },\n    price: Number\n}, {\n    timestamps: true,\n    collection: \"product\"\n});\nconst CategorySchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    slug: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    description: String,\n    parentId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Category\"\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    }\n}, {\n    timestamps: true,\n    collection: \"category\"\n});\nconst OrderSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    orderNumber: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    status: {\n        type: String,\n        enum: [\n            \"PENDING\",\n            \"CONFIRMED\",\n            \"PROCESSING\",\n            \"SHIPPED\",\n            \"DELIVERED\",\n            \"CANCELLED\",\n            \"REFUNDED\"\n        ],\n        default: \"PENDING\"\n    },\n    paymentStatus: {\n        type: String,\n        enum: [\n            \"PENDING\",\n            \"PAID\",\n            \"FAILED\",\n            \"REFUNDED\"\n        ],\n        default: \"PENDING\"\n    },\n    paymentMethod: String,\n    paymentId: String,\n    subtotal: {\n        type: Number,\n        required: true\n    },\n    tax: {\n        type: Number,\n        default: 0\n    },\n    shipping: {\n        type: Number,\n        default: 0\n    },\n    discount: {\n        type: Number,\n        default: 0\n    },\n    total: {\n        type: Number,\n        required: true\n    },\n    currency: {\n        type: String,\n        default: \"INR\"\n    },\n    notes: String,\n    userId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    couponCodes: [\n        String\n    ],\n    couponDiscount: {\n        type: Number,\n        default: 0\n    },\n    flashSaleDiscount: {\n        type: Number,\n        default: 0\n    },\n    estimatedDelivery: String,\n    trackingNumber: String,\n    addressId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Address\"\n    }\n}, {\n    timestamps: true,\n    collection: \"order\"\n});\nconst HomepageSettingSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    productOfTheMonthId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\"\n    },\n    bannerText: String,\n    bannerCtaText: String,\n    bannerCtaLink: String,\n    isActive: {\n        type: Boolean,\n        default: true\n    }\n}, {\n    timestamps: true,\n    collection: \"homepageSetting\"\n});\nconst TestimonialSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: true\n    },\n    content: {\n        type: String,\n        required: true\n    },\n    rating: {\n        type: Number,\n        default: 5,\n        min: 1,\n        max: 5\n    },\n    image: String,\n    position: String,\n    company: String,\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    order: {\n        type: Number,\n        default: 0\n    }\n}, {\n    timestamps: true,\n    collection: \"testimonials\"\n});\nconst ProductImageSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    productId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\",\n        required: true\n    },\n    url: {\n        type: String,\n        required: true\n    },\n    alt: String,\n    position: {\n        type: Number,\n        default: 0\n    }\n}, {\n    timestamps: true,\n    collection: \"images\"\n});\nconst ProductVariantSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    productId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\",\n        required: true\n    },\n    name: {\n        type: String,\n        required: true\n    },\n    value: {\n        type: String,\n        required: true\n    },\n    price: Number,\n    pricingMode: {\n        type: String,\n        enum: [\n            \"REPLACE\",\n            \"INCREMENT\",\n            \"FIXED\"\n        ],\n        default: \"REPLACE\"\n    }\n}, {\n    timestamps: true,\n    collection: \"variants\"\n});\nconst ReviewSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    productId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\",\n        required: true\n    },\n    userId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    rating: {\n        type: Number,\n        required: true,\n        min: 1,\n        max: 5\n    },\n    title: String,\n    comment: String,\n    isApproved: {\n        type: Boolean,\n        default: false\n    }\n}, {\n    timestamps: true,\n    collection: \"reviews\"\n});\nconst AddressSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    userId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    firstName: {\n        type: String,\n        required: true\n    },\n    lastName: {\n        type: String,\n        required: true\n    },\n    address1: {\n        type: String,\n        required: true\n    },\n    address2: String,\n    city: {\n        type: String,\n        required: true\n    },\n    state: {\n        type: String,\n        required: true\n    },\n    postalCode: {\n        type: String,\n        required: true\n    },\n    country: {\n        type: String,\n        required: true\n    },\n    phone: {\n        type: String,\n        required: true\n    },\n    isDefault: {\n        type: Boolean,\n        default: false\n    }\n}, {\n    timestamps: true,\n    collection: \"address\"\n});\nconst OrderItemSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    orderId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Order\",\n        required: true\n    },\n    productId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\",\n        required: true\n    },\n    quantity: {\n        type: Number,\n        required: true\n    },\n    price: {\n        type: Number,\n        required: true\n    },\n    total: {\n        type: Number,\n        required: true\n    }\n}, {\n    timestamps: true,\n    collection: \"items\"\n});\nconst NotificationSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    userId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    title: {\n        type: String,\n        required: true\n    },\n    message: {\n        type: String,\n        required: true\n    },\n    type: {\n        type: String,\n        enum: [\n            \"ORDER\",\n            \"PRODUCT\",\n            \"SYSTEM\",\n            \"MARKETING\"\n        ],\n        required: true\n    },\n    isRead: {\n        type: Boolean,\n        default: false\n    },\n    data: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.Mixed\n}, {\n    timestamps: true,\n    collection: \"notifications\"\n});\nconst CouponSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    code: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    name: {\n        type: String,\n        required: true\n    },\n    description: String,\n    type: {\n        type: String,\n        enum: [\n            \"PERCENTAGE\",\n            \"FIXED\"\n        ],\n        required: true\n    },\n    value: {\n        type: Number,\n        required: true\n    },\n    minimumAmount: Number,\n    maximumDiscount: Number,\n    usageLimit: Number,\n    usedCount: {\n        type: Number,\n        default: 0\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    validFrom: {\n        type: Date,\n        required: true\n    },\n    validUntil: Date,\n    userUsageLimit: Number,\n    isStackable: Boolean,\n    showInModule: Boolean,\n    applicableProducts: [\n        {\n            type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n            ref: \"Product\"\n        }\n    ],\n    applicableCategories: [\n        {\n            type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n            ref: \"Category\"\n        }\n    ],\n    excludedProducts: [\n        {\n            type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n            ref: \"Product\"\n        }\n    ],\n    excludedCategories: [\n        {\n            type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n            ref: \"Category\"\n        }\n    ],\n    customerSegments: [\n        String\n    ]\n}, {\n    timestamps: true,\n    collection: \"coupons\"\n});\nconst WishlistSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    userId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    productId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\",\n        required: true\n    }\n}, {\n    timestamps: true,\n    collection: \"wishlist\"\n});\nconst NewsletterSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    email: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    }\n}, {\n    timestamps: true,\n    collection: \"newsletter\"\n});\nconst ProductCategorySchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    productId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\",\n        required: true\n    },\n    categoryId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Category\",\n        required: true\n    }\n}, {\n    timestamps: true,\n    collection: \"productCategories\"\n});\nconst WishlistItemSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    userId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    productId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\",\n        required: true\n    }\n}, {\n    timestamps: true,\n    collection: \"wishlistItems\"\n});\n// Create and export models\nconst User = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).User || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"User\", UserSchema);\nconst Product = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Product || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Product\", ProductSchema);\nconst Category = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Category || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Category\", CategorySchema);\nconst Order = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Order || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Order\", OrderSchema);\nconst HomepageSetting = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).HomepageSetting || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"HomepageSetting\", HomepageSettingSchema);\nconst Testimonial = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Testimonial || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Testimonial\", TestimonialSchema);\nconst ProductImage = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).ProductImage || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"ProductImage\", ProductImageSchema);\nconst ProductVariant = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).ProductVariant || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"ProductVariant\", ProductVariantSchema);\nconst Review = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Review || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Review\", ReviewSchema);\nconst Address = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Address || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Address\", AddressSchema);\nconst OrderItem = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).OrderItem || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"OrderItem\", OrderItemSchema);\nconst Notification = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Notification || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Notification\", NotificationSchema);\nconst Coupon = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Coupon || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Coupon\", CouponSchema);\nconst Wishlist = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Wishlist || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Wishlist\", WishlistSchema);\nconst Newsletter = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Newsletter || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Newsletter\", NewsletterSchema);\nconst ProductCategory = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).ProductCategory || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"ProductCategory\", ProductCategorySchema);\nconst WishlistItem = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).WishlistItem || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"WishlistItem\", WishlistItemSchema);\nconst NotificationTemplateSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    type: {\n        type: String,\n        required: true\n    },\n    title: {\n        type: String,\n        required: true\n    },\n    message: {\n        type: String,\n        required: true\n    },\n    emailSubject: String,\n    emailTemplate: String,\n    isActive: {\n        type: Boolean,\n        default: true\n    }\n}, {\n    timestamps: true,\n    collection: \"notification_templates\"\n});\nconst NotificationTemplate = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).NotificationTemplate || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"NotificationTemplate\", NotificationTemplateSchema);\nconst EnquirySchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: true\n    },\n    email: {\n        type: String,\n        required: true\n    },\n    subject: {\n        type: String,\n        required: true\n    },\n    message: {\n        type: String,\n        required: true\n    },\n    status: {\n        type: String,\n        default: \"NEW\"\n    },\n    notes: String\n}, {\n    timestamps: true,\n    collection: \"enquiry\"\n});\nconst Enquiry = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Enquiry || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Enquiry\", EnquirySchema);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/models.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/mongoose.ts":
/*!*****************************!*\
  !*** ./app/lib/mongoose.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error(\"Please define the MONGODB_URI environment variable inside .env\");\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/mongoose.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fhomepage-settings%2Froute&page=%2Fapi%2Fhomepage-settings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhomepage-settings%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();