import React from "react";

const ShippingPage = () => {
  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-2xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <h1 className="text-3xl font-extrabold text-gray-900">
          Shipping and Returns
        </h1>
        <div className="mt-6 prose prose-indigo text-gray-500">
          <p>Last updated: July 31, 2024</p>

          <h2>Shipping</h2>
          <p>
            We ship to all locations within the country. Orders are typically
            processed within 1-2 business days. Shipping times may vary based on
            your location.
          </p>

          <h2>Returns</h2>
          <p>
            We have a 30-day return policy, which means you have 30 days after
            receiving your item to request a return.
          </p>
          <p>
            To be eligible for a return, your item must be in the same condition
            that you received it, unworn or unused, with tags, and in its
            original packaging. You’ll also need the receipt or proof of
            purchase.
          </p>

          {/* Add more sections as needed */}
        </div>
      </div>
    </div>
  );
};

export default ShippingPage;
