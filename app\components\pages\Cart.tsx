"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { Minus, Plus, Trash2, ShoppingBag, ArrowRight } from "lucide-react";
import { useCart } from "../../context/CartContext";
import { useSession } from "next-auth/react";
import CouponModule from "../CouponModule";
import { AppliedCoupon } from "../../types";

const Cart: React.FC = () => {
  const { state, dispatch } = useCart();
  const { data: session } = useSession();

  const updateQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      dispatch({ type: "REMOVE_ITEM", payload: itemId });
    } else {
      dispatch({ type: "UPDATE_QUANTITY", payload: { id: itemId, quantity } });
    }
  };

  const removeItem = (itemId: string) => {
    dispatch({ type: "REMOVE_ITEM", payload: itemId });
  };

  const handleCouponApply = (appliedCoupon: AppliedCoupon) => {
    dispatch({ type: "APPLY_COUPON", payload: appliedCoupon });
  };

  const handleCouponRemove = (couponId: string) => {
    dispatch({ type: "REMOVE_COUPON", payload: couponId });
  };

  if (!state.isHydrated) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="loader"></div>
        <style jsx>{`
          .loader {
            border: 8px solid #f3f3f3;
            border-top: 8px solid #3498db;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            animation: spin 2s linear infinite;
          }
          @keyframes spin {
            0% {
              transform: rotate(0deg);
            }
            100% {
              transform: rotate(360deg);
            }
          }
        `}</style>
      </div>
    );
  }

  if (state.items.length === 0) {
    return (
      <div className="lg:grid lg:grid-cols-12 lg:gap-8">
        {/* Mobile Layout */}
        <div className="lg:hidden">
          <div className="px-4 py-8 text-center">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <ShoppingBag className="w-12 h-12 text-gray-400" />
            </div>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              Your cart is empty
            </h2>
            <p className="text-gray-600 mb-6">
              Add some products to get started
            </p>
            <Link
              href="/shop"
              className="inline-flex items-center bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors"
            >
              Start Shopping
              <ArrowRight className="ml-2 w-4 h-4" />
            </Link>
          </div>
        </div>

        {/* Desktop Layout */}
        <div className="hidden lg:block lg:col-span-12">
          <div className="py-16 text-center">
            <div className="w-32 h-32 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-8">
              <ShoppingBag className="w-16 h-16 text-gray-400" />
            </div>
            <h2 className="text-4xl font-bold text-gray-800 mb-4">
              Your cart is empty
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Discover our amazing products and start shopping
            </p>
            <Link
              href="/shop"
              className="inline-flex items-center bg-green-600 text-white px-8 py-4 rounded-full font-semibold hover:bg-green-700 transition-colors text-lg"
            >
              Start Shopping
              <ArrowRight className="ml-3 w-5 h-5" />
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="lg:grid lg:grid-cols-12 lg:gap-8">
      {/* Mobile Layout */}
      <div className="lg:hidden">
        <div className="px-4 py-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-6">
            Shopping Cart
          </h1>

          <div className="space-y-4 mb-6">
            {state.items.map((item) => (
              <div
                key={item.variantKey || item.product.id}
                className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100"
              >
                <div className="flex space-x-4">
                  <div className="w-20 h-20 relative rounded-xl overflow-hidden flex-shrink-0">
                    <Image
                      src={item.product.image || "/placeholder-product.jpg"}
                      alt={item.product.name}
                      fill
                      className="object-cover"
                      sizes="80px"
                    />
                  </div>

                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-gray-800 mb-1 line-clamp-1">
                      {item.product.name}
                    </h3>
                    {item.selectedVariants &&
                      item.selectedVariants.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-2">
                          {item.selectedVariants.map((variant, index) => (
                            <span
                              key={index}
                              className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
                            >
                              {variant.name}: {variant.value}
                            </span>
                          ))}
                        </div>
                      )}
                    <p className="text-sm text-gray-600 mb-2 line-clamp-1">
                      {item.product.shortDescription}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-bold text-gray-900">
                        ₹{item.product.price}
                      </span>
                      <button
                        onClick={() =>
                          removeItem(item.variantKey || item.product.id)
                        }
                        className="p-2 text-red-500 hover:bg-red-50 rounded-full transition-colors"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-100">
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() =>
                        updateQuantity(
                          item.variantKey || item.product.id,
                          item.quantity - 1,
                        )
                      }
                      className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors"
                    >
                      <Minus className="w-4 h-4" />
                    </button>
                    <span className="font-medium text-gray-800 w-8 text-center">
                      {item.quantity}
                    </span>
                    <button
                      onClick={() =>
                        updateQuantity(
                          item.variantKey || item.product.id,
                          item.quantity + 1,
                        )
                      }
                      className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                  <span className="font-bold text-gray-900">
                    ₹{(item.product.price * item.quantity).toFixed(2)}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {/* Coupon Module - Moved below products */}
          <div className="mb-6">
            <CouponModule
              cartItems={state.items}
              subtotal={state.subtotal}
              userId={session?.user?.id}
              onCouponApply={handleCouponApply}
              onCouponRemove={handleCouponRemove}
              appliedCoupons={state.coupons.appliedCoupons}
            />
          </div>

          {/* Order Summary */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6">
            <h3 className="font-semibold text-gray-800 mb-4">Order Summary</h3>
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-gray-600">
                <span>Subtotal ({state.itemCount} items)</span>
                <span>₹{state.subtotal.toFixed(2)}</span>
              </div>
              {state.flashSaleDiscount > 0 && (
                <div className="flex justify-between text-red-600">
                  <span>
                    Flash Sale Discount ({state.flashSalePercentage}% OFF)
                  </span>
                  <span>-₹{state.flashSaleDiscount.toFixed(2)}</span>
                </div>
              )}
              {state.coupons.totalDiscount > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>Coupon Discount</span>
                  <span>-₹{state.coupons.totalDiscount.toFixed(2)}</span>
                </div>
              )}
              <div className="flex justify-between text-gray-600">
                <span>Shipping</span>
                <span>Free</span>
              </div>
              <div className="border-t border-gray-200 pt-2">
                <div className="flex justify-between font-bold text-gray-900 text-lg">
                  <span>Total</span>
                  <span>₹{state.finalTotal.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>

          <Link
            href="/checkout"
            className="w-full bg-green-600 text-white py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors flex items-center justify-center space-x-2"
          >
            <span>Proceed to Checkout</span>
            <ArrowRight className="w-5 h-5" />
          </Link>
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:block lg:col-span-12">
        <div className="py-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-8">
            Shopping Cart
          </h1>

          <div className="grid grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="col-span-2">
              <div className="space-y-6">
                {state.items.map((item) => (
                  <div
                    key={item.variantKey || item.product.id}
                    className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
                  >
                    <div className="flex space-x-6">
                      <div className="w-32 h-32 relative rounded-xl overflow-hidden flex-shrink-0">
                        <Image
                          src={item.product.image || "/placeholder-product.jpg"}
                          alt={item.product.name}
                          fill
                          className="object-cover"
                          sizes="128px"
                        />
                      </div>

                      <div className="flex-1">
                        <div className="flex justify-between items-start mb-4">
                          <div>
                            <h3 className="text-xl font-semibold text-gray-800 mb-2">
                              {item.product.name}
                            </h3>
                            {item.selectedVariants &&
                              item.selectedVariants.length > 0 && (
                                <div className="flex flex-wrap gap-2 mb-2">
                                  {item.selectedVariants.map(
                                    (variant, index) => (
                                      <span
                                        key={index}
                                        className="text-sm bg-gray-100 text-gray-600 px-3 py-1 rounded-full"
                                      >
                                        {variant.name}: {variant.value}
                                      </span>
                                    ),
                                  )}
                                </div>
                              )}
                            <p className="text-gray-600">
                              {item.product.shortDescription}
                            </p>
                          </div>
                          <button
                            onClick={() =>
                              removeItem(item.variantKey || item.product.id)
                            }
                            className="p-2 text-red-500 hover:bg-red-50 rounded-full transition-colors"
                          >
                            <Trash2 className="w-5 h-5" />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <button
                              onClick={() =>
                                updateQuantity(
                                  item.variantKey || item.product.id,
                                  item.quantity - 1,
                                )
                              }
                              className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors"
                            >
                              <Minus className="w-5 h-5" />
                            </button>
                            <span className="font-medium text-gray-800 w-12 text-center text-lg">
                              {item.quantity}
                            </span>
                            <button
                              onClick={() =>
                                updateQuantity(
                                  item.variantKey || item.product.id,
                                  item.quantity + 1,
                                )
                              }
                              className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors"
                            >
                              <Plus className="w-5 h-5" />
                            </button>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold text-gray-900">
                              ₹{(item.product.price * item.quantity).toFixed(2)}
                            </div>
                            <div className="text-sm text-gray-500">
                              ₹{item.product.price} each
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Coupon Module - Moved to left column below products */}
              <div className="mt-6">
                <CouponModule
                  cartItems={state.items}
                  subtotal={state.subtotal}
                  userId={session?.user?.id}
                  onCouponApply={handleCouponApply}
                  onCouponRemove={handleCouponRemove}
                  appliedCoupons={state.coupons.appliedCoupons}
                />
              </div>
            </div>

            {/* Order Summary */}
            <div className="col-span-1">
              <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 sticky top-24">
                <h3 className="text-xl font-semibold text-gray-800 mb-6">
                  Order Summary
                </h3>
                <div className="space-y-4 mb-6">
                  <div className="flex justify-between text-gray-600">
                    <span>Subtotal ({state.itemCount} items)</span>
                    <span>₹{state.subtotal.toFixed(2)}</span>
                  </div>
                  {state.flashSaleDiscount > 0 && (
                    <div className="flex justify-between text-red-600">
                      <span>
                        Flash Sale Discount ({state.flashSalePercentage}% OFF)
                      </span>
                      <span>-₹{state.flashSaleDiscount.toFixed(2)}</span>
                    </div>
                  )}
                  {state.coupons.totalDiscount > 0 && (
                    <div className="flex justify-between text-green-600">
                      <span>Coupon Discount</span>
                      <span>-₹{state.coupons.totalDiscount.toFixed(2)}</span>
                    </div>
                  )}
                  <div className="flex justify-between text-gray-600">
                    <span>Shipping</span>
                    <span className="text-green-600 font-medium">Free</span>
                  </div>
                  <div className="border-t border-gray-200 pt-4">
                    <div className="flex justify-between font-bold text-gray-900 text-xl">
                      <span>Total</span>
                      <span>₹{state.finalTotal.toFixed(2)}</span>
                    </div>
                  </div>
                </div>

                <Link
                  href="/checkout"
                  className="w-full bg-green-600 text-white py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors flex items-center justify-center space-x-2 text-lg"
                >
                  <span>Proceed to Checkout</span>
                  <ArrowRight className="w-5 h-5" />
                </Link>

                <Link
                  href="/shop"
                  className="w-full mt-3 border border-gray-300 text-gray-700 py-4 rounded-2xl font-medium hover:bg-gray-50 transition-colors flex items-center justify-center"
                >
                  Continue Shopping
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Cart;
