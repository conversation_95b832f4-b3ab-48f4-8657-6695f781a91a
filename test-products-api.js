// Quick test to check if products API is working
async function testProductsAPI() {
  try {
    console.log('Testing products API...');
    
    const response = await fetch('http://localhost:3000/api/products?limit=1000');
    const data = await response.json();
    
    console.log('API Response Status:', response.status);
    console.log('API Response Data:', JSON.stringify(data, null, 2));
    
    if (data.success) {
      console.log(`Found ${data.data.length} products`);
      if (data.data.length > 0) {
        console.log('First product:', data.data[0]);
      }
    } else {
      console.log('API Error:', data.error);
    }
    
  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

// Test categories API too
async function testCategoriesAPI() {
  try {
    console.log('\nTesting categories API...');
    
    const response = await fetch('http://localhost:3000/api/categories');
    const data = await response.json();
    
    console.log('Categories API Response Status:', response.status);
    console.log('Categories API Response Data:', JSON.stringify(data, null, 2));
    
    if (data.success) {
      console.log(`Found ${data.data.length} categories`);
    } else {
      console.log('Categories API Error:', data.error);
    }
    
  } catch (error) {
    console.error('Categories test failed:', error.message);
  }
}

// Run tests
testProductsAPI();
testCategoriesAPI();