import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/app/lib/mongoose";
import { User, Order } from "@/app/lib/models";

// GET /api/users - List all users
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const role = searchParams.get("role");
    const search = searchParams.get("search");

    const skip = (page - 1) * limit;

    const where: any = {};

    if (role) {
      where.role = role;
    }

    if (search) {
      where.$or = [
        { name: { $regex: search, $options: "i" } },
        { email: { $regex: search, $options: "i" } },
      ];
    }

    const [users, total] = await Promise.all([
      User.find(where).skip(skip).limit(limit).sort({ createdAt: -1 }).lean(),
      User.countDocuments(where),
    ]);

    // Get order counts for each user
    const usersWithCounts = await Promise.all(
      users.map(async (user) => {
        const orderCount = await Order.countDocuments({
          userId: String((user as any)._id),
        });
        return {
          ...user,
          _count: {
            orders: orderCount,
          },
        };
      }),
    );

    return NextResponse.json({
      success: true,
      data: usersWithCounts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch users" },
      { status: 500 },
    );
  }
}

// POST /api/users - Create a new user
export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const { name, email, phone, role = "CUSTOMER" } = body;

    const user = await User.create({
      name,
      email,
      phone,
      role,
    });

    // Get order count for the new user (should be 0)
    const orderCount = await Order.countDocuments({
      userId: user._id,
    });

    const userWithCount = {
      ...user.toObject(),
      _count: {
        orders: orderCount,
      },
    };

    return NextResponse.json({
      success: true,
      data: userWithCount,
      message: "User created successfully",
    });
  } catch (error) {
    console.error("Error creating user:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create user" },
      { status: 500 },
    );
  }
}
