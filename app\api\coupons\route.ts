import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../lib/auth";
import connectDB from "../../lib/mongoose";
import { Coupon } from "../../lib/models";
import { logger } from "../../lib/logger";

type CouponCreateInput = {
  code: string;
  name: string;
  description?: string | null;
  type: string;
  discountType: "PERCENTAGE" | "AMOUNT";
  discountValue: number;
  minimumAmount?: number | null;
  maximumDiscount?: number | null;
  usageLimit?: number | null;
  userUsageLimit?: number | null;
  isActive?: boolean;
  isStackable?: boolean;
  showInModule?: boolean;
  validFrom?: string | Date | null;
  validUntil?: string | Date | null;
  applicableProducts?: string[];
  applicableCategories?: string[];
  excludedProducts?: string[];
  excludedCategories?: string[];
  customerSegments?: string[];
};

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const { searchParams } = new URL(request.url);

    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "10", 10);
    const isActiveParam = searchParams.get("active");
    const showInModuleParam = searchParams.get("showInModule");
    const type = searchParams.get("type");
    const userId = searchParams.get("userId");

    const skip = (page - 1) * limit;

    await connectDB();

    const filter: any = {};

    if (isActiveParam === "true") {
      filter.isActive = true;
    } else if (isActiveParam === "false") {
      filter.isActive = false;
    }

    if (showInModuleParam === "true") {
      filter.showInModule = true;
    } else if (showInModuleParam === "false") {
      filter.showInModule = false;
    }

    if (type) {
      filter.type = type;
    }

    // For regular users, only show active and currently valid coupons
    if (!session?.user || (session.user as any).role !== "ADMIN") {
      const now = new Date();
      filter.isActive = true;
      filter.validFrom = { $lte: now };
      // validUntil is either null or in the future
      filter.$or = [{ validUntil: null }, { validUntil: { $gte: now } }];
    }

    const [coupons, total] = await Promise.all([
      Coupon.find(filter)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Coupon.countDocuments(filter),
    ]);

    // If a userId is provided, attach lightweight usage info for that user
    // Derive usage from Orders using couponCodes array instead of a CouponUsage model
    let usageByCouponId: Record<string, { id: string; usedAt: Date }[]> = {};
    if (userId) {
      const { Order } = await import("../../lib/models");
      // Build simple entries array (avoid MapIterator) of [couponId, CODE]
      const codeByIdEntries: [string, string][] = coupons.map(
        (c: any) => [String(c._id), String(c.code).toUpperCase()],
      );
      const codes = Array.from(new Set(codeByIdEntries.map((e) => e[1])));
      const userOrders = await Order.find({
        userId,
        couponCodes: { $in: codes },
      })
        .select("_id couponCodes createdAt")
        .lean();
      const ordersArr: any[] = Array.isArray(userOrders) ? userOrders : [];
      usageByCouponId = {};
      for (let i = 0; i < codeByIdEntries.length; i++) {
        const [couponId, code] = codeByIdEntries[i];
        const hits = ordersArr.filter((o: any) =>
          Array.isArray(o.couponCodes) &&
          o.couponCodes.some((cc: string) => String(cc).toUpperCase() === code),
        );
        usageByCouponId[couponId] = hits.map((o: any) => ({
          id: String(o._id),
          usedAt: o.createdAt as Date,
        }));
      }
    }
 
    const result = coupons.map((c: any) => ({
      ...c,
      ...(userId ? { couponUsages: usageByCouponId[String(c._id)] || [] } : {}),
    }));

    return NextResponse.json({
      coupons: result,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    logger.error("Error fetching coupons", error as Error);
    return NextResponse.json(
      { error: "Failed to fetch coupons" },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || (session.user as any).role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const data: CouponCreateInput = await request.json();

    // Validate required fields
    if (
      !data.code ||
      !data.name ||
      !data.type ||
      !data.discountType ||
      data.discountValue === undefined
    ) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 },
      );
    }

    await connectDB();

    const normalizedCode = data.code.toUpperCase();

    // Check if coupon code already exists
    const existing = await Coupon.findOne({ code: normalizedCode }).lean();
    if (existing) {
      return NextResponse.json(
        { error: "Coupon code already exists" },
        { status: 400 },
      );
    }

    const coupon = await Coupon.create({
      code: normalizedCode,
      name: data.name,
      description: data.description ?? null,
      type: data.type,
      discountType: data.discountType,
      discountValue: data.discountValue,
      minimumAmount: data.minimumAmount ?? null,
      maximumDiscount: data.maximumDiscount ?? null,
      usageLimit: data.usageLimit ?? null,
      userUsageLimit: data.userUsageLimit ?? null,
      isActive: data.isActive ?? true,
      isStackable: data.isStackable ?? false,
      showInModule: data.showInModule ?? false,
      validFrom: data.validFrom ? new Date(data.validFrom) : new Date(),
      validUntil: data.validUntil ? new Date(data.validUntil) : null,
      applicableProducts: data.applicableProducts || [],
      applicableCategories: data.applicableCategories || [],
      excludedProducts: data.excludedProducts || [],
      excludedCategories: data.excludedCategories || [],
      customerSegments: data.customerSegments || [],
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Return plain object
    const created = coupon.toObject ? coupon.toObject() : coupon;

    return NextResponse.json(created, { status: 201 });
  } catch (error: any) {
    // Handle duplicate key error if unique index exists on code
    if (error?.code === 11000) {
      return NextResponse.json(
        { error: "Coupon code already exists" },
        { status: 400 },
      );
    }
    logger.error("Error creating coupon", error as Error);
    return NextResponse.json(
      { error: "Failed to create coupon" },
      { status: 500 },
    );
  }
}
