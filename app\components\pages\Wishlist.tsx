"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import Link from "next/link";
import Image from "next/image";
import { ArrowLeft, Heart, ShoppingCart, X, Star, Loader2 } from "lucide-react";
import { useCart } from "../../context/CartContext";
import { Product } from "../../types";
import { WishlistSkeleton } from "../loaders/SkeletonLoaders";
import { useToastContext } from "../../context/ToastContext";
import { useNotifications } from "../../context/NotificationContext";

interface WishlistItem {
  id: string;
  name: string;
  slug: string;
  price: number;
  shortDescription: string;
  image: string;
  rating: number;
  reviews: number;
  wishlistItemId: string;
}

const Wishlist: React.FC = () => {
  const router = useRouter();
  const { data: session, status } = useSession();
  const { dispatch } = useCart();
  const { showToast } = useToastContext();
  const { refreshUnreadCount, refreshNotifications } = useNotifications();

  const [wishlistItems, setWishlistItems] = useState<WishlistItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [recommendations, setRecommendations] = useState<WishlistItem[]>([]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === "unauthenticated" && !session) {
      router.push("/login");
    }
  }, [status, router]);

  // Fetch wishlist items
  useEffect(() => {
    const fetchWishlist = async () => {
      if (status !== "authenticated" || !session?.user?.id) {
        if (status !== "loading") {
          setLoading(false);
        }
        return;
      }

      try {
        const response = await fetch("/api/wishlist");
        if (!response.ok) {
          throw new Error("Failed to fetch wishlist");
        }

        const data = await response.json();
        setWishlistItems(data.items || []);
      } catch (err) {
        console.error("Error fetching wishlist:", err);
        setError("Failed to load wishlist");
      } finally {
        setLoading(false);
      }
    };

    fetchWishlist();
  }, [session, status]);

  // Fetch recommendations
  useEffect(() => {
    const fetchRecommendations = async () => {
      try {
        const response = await fetch("/api/products?limit=4");
        if (response.ok) {
          const data = await response.json();
          const transformed = data.data.map((p: any) => ({
            id: p.id,
            name: p.name,
            slug: p.slug,
            price: p.price,
            shortDescription: p.shortDescription,
            image: p.images?.[0]?.url || "/placeholder-product.jpg",
            rating: p.rating || 0,
            reviews: p.reviews || 0,
            wishlistItemId: "", // Not a real wishlist item
          }));
          setRecommendations(transformed || []);
        }
      } catch (err) {
        console.error("Error fetching recommendations:", err);
      }
    };

    fetchRecommendations();
  }, []);

  const removeFromWishlist = async (productId: string) => {
    try {
      const response = await fetch(`/api/wishlist?productId=${productId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Failed to remove from wishlist");
      }

      setWishlistItems((prev) => prev.filter((item) => item.id !== productId));
      showToast("Item removed from wishlist", "success");
      refreshUnreadCount();
      refreshNotifications();
    } catch (err: any) {
      console.error("Error removing from wishlist:", err);
      showToast(err.message || "Could not remove item from wishlist.", "error");
    }
  };

  const addToCart = (wishlistItem: WishlistItem) => {
    const { wishlistItemId, ...product } = wishlistItem;
    dispatch({ type: "ADD_ITEM", payload: product as Product });
    showToast(`${product.name} added to cart`, "success");
  };

  const addAllToCart = () => {
    wishlistItems.forEach((wishlistItem) => {
      const { wishlistItemId, ...product } = wishlistItem;
      dispatch({ type: "ADD_ITEM", payload: product as Product });
    });
    showToast("All wishlist items added to cart", "success");
  };

  // Show loading state while checking authentication
  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="w-8 h-8 animate-spin text-green-600" />
      </div>
    );
  }

  // Don't render content if not authenticated
  if (status === "unauthenticated") {
    return null;
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-gray-900">My Wishlist</h1>
          <span className="text-gray-600">{wishlistItems.length} items</span>
        </div>

        {loading ? (
          <WishlistSkeleton />
        ) : error ? (
          <div className="text-center py-16">
            <div className="w-32 h-32 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-8">
              <X className="w-16 h-16 text-red-500" />
            </div>
            <h2 className="text-3xl font-bold text-gray-800 mb-4">
              Error loading wishlist
            </h2>
            <p className="text-xl text-gray-600 mb-8">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="inline-flex items-center bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors text-lg"
            >
              Try Again
            </button>
          </div>
        ) : wishlistItems.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Wishlist Items */}
            <div className="lg:col-span-2">
              <div className="space-y-4">
                {wishlistItems.map((product) => (
                  <div
                    key={product.id}
                    className="bg-white rounded-lg shadow-sm p-4 flex items-center space-x-4"
                  >
                    <Link href={`/product/${product.slug || product.id}`}>
                      <div className="w-24 h-24 relative rounded-md overflow-hidden flex-shrink-0">
                        <Image
                          src={product.image || "/placeholder-product.jpg"}
                          alt={product.name}
                          fill
                          className="object-cover"
                          sizes="96px"
                        />
                      </div>
                    </Link>
                    <div className="flex-1">
                      <Link href={`/product/${product.slug || product.id}`}>
                        <h3 className="font-semibold text-gray-800 hover:text-green-600">
                          {product.name}
                        </h3>
                      </Link>
                      <p className="text-sm text-gray-500 mt-1">
                        ₹{product.price}
                      </p>
                      <div className="flex items-center mt-2">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="text-sm text-gray-600 ml-1">
                          {product.rating} ({product.reviews} reviews)
                        </span>
                      </div>
                    </div>
                    <div className="flex flex-col items-end space-y-2">
                      <button
                        onClick={() => removeFromWishlist(product.id)}
                        className="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-full transition-colors"
                      >
                        <X className="w-5 h-5" />
                      </button>
                      <button
                        onClick={() => addToCart(product)}
                        className="bg-green-100 text-green-700 px-4 py-2 rounded-full text-sm font-medium hover:bg-green-200 transition-colors"
                      >
                        Add to Cart
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Summary & Recommendations */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm p-6 sticky top-24">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Wishlist Summary
                </h2>
                <div className="flex justify-between items-center mb-4">
                  <span className="text-gray-600">Total Items:</span>
                  <span className="font-semibold text-gray-900">
                    {wishlistItems.length}
                  </span>
                </div>
                <button
                  onClick={addAllToCart}
                  className="w-full bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors flex items-center justify-center space-x-2"
                >
                  <ShoppingCart className="w-5 h-5" />
                  <span>Add All to Cart</span>
                </button>
                <div className="mt-8">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    You might also like
                  </h3>
                  <div className="space-y-4">
                    {recommendations.slice(0, 2).map((product) => (
                      <Link
                        key={product.id}
                        href={`/product/${product.slug || product.id}`}
                        className="flex items-center space-x-3 group"
                      >
                        <div className="w-16 h-16 relative rounded-md overflow-hidden flex-shrink-0">
                          <Image
                            src={product.image || "/placeholder-product.jpg"}
                            alt={product.name}
                            fill
                            className="object-cover"
                            sizes="64px"
                          />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-800 group-hover:text-green-600">
                            {product.name}
                          </p>
                          <p className="text-sm text-gray-500">
                            ₹{product.price}
                          </p>
                        </div>
                      </Link>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="w-32 h-32 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-8">
              <Heart className="w-16 h-16 text-green-500" />
            </div>
            <h2 className="text-3xl font-bold text-gray-800 mb-4">
              Your wishlist is empty
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Looks like you haven't added anything yet. Let's change that!
            </p>
            <Link
              href="/shop"
              className="inline-flex items-center bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors text-lg"
            >
              Explore Products
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default Wishlist;
