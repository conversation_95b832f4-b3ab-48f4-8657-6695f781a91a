import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../../lib/auth";
import connectDB from "../../../../lib/mongoose";
import { Types } from "mongoose";
import { Product, ProductVariant } from "../../../../lib/models";
import {
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  ValidationError,
  asyncHandler,
} from "../../../../lib/errors";
import { logger } from "../../../../lib/logger";

export const GET = asyncHandler(
  async (request: NextRequest, { params }: { params: { id: string } }) => {
    const productIdParam = params.id;
    logger.apiRequest("GET", `/api/products/${productIdParam}/variations`);

    await connectDB();

    const productId = Types.ObjectId.isValid(productIdParam)
      ? new Types.ObjectId(productIdParam)
      : productIdParam;

    // Fetch all variations for the product
    const variations = await ProductVariant.find({ productId })
      .sort({ name: 1, value: 1 })
      .lean();

    logger.info("Product variations fetched", {
      productId: productIdParam,
      count: variations.length,
    });

    return NextResponse.json({
      success: true,
      data: variations,
    });
  },
);

export const POST = asyncHandler(
  async (request: NextRequest, { params }: { params: { id: string } }) => {
    const productIdParam = params.id;
    logger.apiRequest("POST", `/api/products/${productIdParam}/variations`);

    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      throw new AuthenticationError();
    }

    if (session.user.role !== "ADMIN") {
      throw new AuthorizationError();
    }

    const body = await request.json();
    const { name, value, price, pricingMode } = body;

    // Validate required fields
    if (!name || !value) {
      throw new ValidationError("Name and value are required");
    }

    // Validate pricing mode
    const validPricingModes: Array<"REPLACE" | "INCREMENT" | "FIXED"> = [
      "REPLACE",
      "INCREMENT",
      "FIXED",
    ];
    const validatedPricingMode =
      pricingMode && validPricingModes.includes(pricingMode)
        ? (pricingMode as "REPLACE" | "INCREMENT" | "FIXED")
        : "REPLACE";

    await connectDB();

    const productId = Types.ObjectId.isValid(productIdParam)
      ? new Types.ObjectId(productIdParam)
      : productIdParam;

    // Check if product exists
    const product = await Product.findById(productId).lean();

    if (!product) {
      throw new NotFoundError("Product");
    }

    // Check if variation with same name and value already exists
    const existingVariation = await ProductVariant.findOne({
      productId,
      name,
      value,
    }).lean();

    if (existingVariation) {
      throw new ConflictError(
        "Variation with this name and value already exists",
      );
    }

    // Parse and validate price
    let parsedPrice: number | null = null;
    if (price !== undefined && price !== null && price !== "") {
      const numPrice =
        typeof price === "string" ? parseFloat(price) : Number(price);
      if (!isNaN(numPrice) && isFinite(numPrice)) {
        parsedPrice = numPrice;
      }
    }

    // Create the variation
    const variation = await ProductVariant.create({
      name: name.trim(),
      value: value.trim(),
      price: parsedPrice,
      pricingMode: validatedPricingMode,
      productId,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    logger.info("Product variation created", {
      productId: productIdParam,
      variationId: String(variation._id),
      name,
      value,
      userId: (session.user as any)._id ?? session.user.id,
    });

    return NextResponse.json({
      success: true,
      data: variation,
      message: "Variation created successfully",
    });
  },
);
