[{"_id": "68913c7053288734bd4a8673", "id": "cmdqmdh360013ulsohzcit7th", "type": "WISHLIST_REMOVED", "title": "<PERSON><PERSON> Removed from Wishlist", "message": "Beard Oil has been removed from your wishlist.", "data": {"productId": "cmdiy0y5j0052ults6dkgyua4", "productName": "Beard Oil"}, "isRead": true, "emailSent": false, "emailSentAt": null, "emailError": null, "expiresAt": null, "createdAt": "2025-07-30T23:51:21.282Z", "updatedAt": "2025-07-30T23:51:21.265Z", "userId": "cmdhti89l000fulhwdl0057fe", "templateId": null}, {"_id": "68913c7053288734bd4a8674", "id": "cmdqmdm9u0015ulsodhp3akwr", "type": "WISHLIST_REMOVED", "title": "<PERSON><PERSON> Removed from Wishlist", "message": "Night Care Combo has been removed from your wishlist.", "data": {"productId": "cmdiy1lqz007uults4ehp6ut0", "productName": "Night Care Combo"}, "isRead": true, "emailSent": false, "emailSentAt": null, "emailError": null, "expiresAt": null, "createdAt": "2025-07-30T23:51:28.002Z", "updatedAt": "2025-07-30T23:51:28.000Z", "userId": "cmdhti89l000fulhwdl0057fe", "templateId": null}, {"_id": "68913c7053288734bd4a8675", "id": "cmdqme6wb0019ulsoelisd54d", "type": "WISHLIST_ADDED", "title": "<PERSON><PERSON> Added to Wishlist", "message": "Saffron Night Repair Cream has been added to your wishlist. We'll notify you of any price changes!", "data": {"price": 800, "currency": "INR", "productId": "cmdiy1kj5007oultssfl9wyh4", "productName": "Saffron Night Repair Cream"}, "isRead": true, "emailSent": false, "emailSentAt": null, "emailError": null, "expiresAt": null, "createdAt": "2025-07-30T23:51:54.731Z", "updatedAt": "2025-07-30T23:51:54.730Z", "userId": "cmdhti89l000fulhwdl0057fe", "templateId": null}, {"_id": "68913c7053288734bd4a8676", "id": "cmdqkkn6r000rulso6pnbafby", "type": "ORDER_PLACED", "title": "Order Placed Successfully", "message": "Your order #F1F70BE6 has been placed successfully. We'll send you updates as your order progresses.", "data": {"amount": 2400, "orderId": "cmdqkkkrr000mulso3zk6fpf5", "currency": "INR", "itemCount": 1, "orderNumber": "F1F70BE6"}, "isRead": true, "emailSent": true, "emailSentAt": "2025-07-30T23:00:58.426Z", "emailError": null, "expiresAt": null, "createdAt": "2025-07-30T23:00:56.547Z", "updatedAt": "2025-07-30T23:00:56.544Z", "userId": "cmdhti89l000fulhwdl0057fe", "templateId": null}, {"_id": "68913c7053288734bd4a8677", "id": "cmdqkldka000tulsopepojf9c", "type": "ORDER_CONFIRMED", "title": "Order Confirmed", "message": "Your order #F1F70BE6 has been confirmed and is being prepared for shipment.", "data": {"orderId": "cmdqkkkrr000mulso3zk6fpf5", "orderNumber": "F1F70BE6"}, "isRead": true, "emailSent": true, "emailSentAt": "2025-07-30T23:01:31.808Z", "emailError": null, "expiresAt": null, "createdAt": "2025-07-30T23:01:30.730Z", "updatedAt": "2025-07-30T23:01:30.728Z", "userId": "cmdhti89l000fulhwdl0057fe", "templateId": null}, {"_id": "68913c7053288734bd4a8678", "id": "cmdqku5b8000xulsovnac4wrs", "type": "REVIEW_SUBMITTED", "title": "Review Submitted", "message": "Thank you for your 5-star review of Anti-Pimple Pack! Your feedback is valuable to us and other customers.", "data": {"rating": 5, "productId": "cmdiy0tk6004mults1f4ecrxz", "productName": "Anti-Pimple Pack"}, "isRead": true, "emailSent": false, "emailSentAt": null, "emailError": null, "expiresAt": null, "createdAt": "2025-07-30T23:08:19.941Z", "updatedAt": "2025-07-30T23:08:19.938Z", "userId": "cmdhti89l000fulhwdl0057fe", "templateId": null}, {"_id": "68913c7053288734bd4a8679", "id": "cmdqmbnb70011ulso89asrxjj", "type": "WISHLIST_ADDED", "title": "<PERSON><PERSON> Added to Wishlist", "message": "Beard Oil has been added to your wishlist. We'll notify you of any price changes!", "data": {"price": 600, "currency": "INR", "productId": "cmdiy0y5j0052ults6dkgyua4", "productName": "Beard Oil"}, "isRead": true, "emailSent": false, "emailSentAt": null, "emailError": null, "expiresAt": null, "createdAt": "2025-07-30T23:49:56.035Z", "updatedAt": "2025-07-30T23:49:56.034Z", "userId": "cmdhti89l000fulhwdl0057fe", "templateId": null}]