/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/profile/page";
exports.ids = ["app/profile/page"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'profile',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/profile/page.tsx */ \"(rsc)/./app/profile/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\profile\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/profile/layout.tsx */ \"(rsc)/./app/profile/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\profile\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\profile\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/profile/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/profile/page\",\n        pathname: \"/profile\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccomponents%5C%5CLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccomponents%5C%5Cpages%5C%5CProfile.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccomponents%5C%5CLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccomponents%5C%5Cpages%5C%5CProfile.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/Layout.tsx */ \"(ssr)/./app/components/Layout.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/pages/Profile.tsx */ \"(ssr)/./app/components/pages/Profile.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNEZXNrdG9wJTVDJTVDcHJvamVjdCU1QyU1Q2FwcCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNMYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNVc2VyJTVDJTVDRGVza3RvcCU1QyU1Q3Byb2plY3QlNUMlNUNhcHAlNUMlNUNjb21wb25lbnRzJTVDJTVDcGFnZXMlNUMlNUNQcm9maWxlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUFnSTtBQUNoSTtBQUNBLGdMQUF3SSIsInNvdXJjZXMiOlsid2VicGFjazovL2hlcmJhbGljaW91cy1uZXh0anMvP2IzNmIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcVXNlclxcXFxEZXNrdG9wXFxcXHByb2plY3RcXFxcYXBwXFxcXGNvbXBvbmVudHNcXFxcTGF5b3V0LnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXFVzZXJcXFxcRGVza3RvcFxcXFxwcm9qZWN0XFxcXGFwcFxcXFxjb21wb25lbnRzXFxcXHBhZ2VzXFxcXFByb2ZpbGUudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccomponents%5C%5CLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccomponents%5C%5Cpages%5C%5CProfile.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CFlashSaleContext.tsx%22%2C%22ids%22%3A%5B%22FlashSaleProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CToastContext.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CFlashSaleContext.tsx%22%2C%22ids%22%3A%5B%22FlashSaleProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CToastContext.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/CartContext.tsx */ \"(ssr)/./app/context/CartContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/FlashSaleContext.tsx */ \"(ssr)/./app/context/FlashSaleContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/NotificationContext.tsx */ \"(ssr)/./app/context/NotificationContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/SessionProvider.tsx */ \"(ssr)/./app/context/SessionProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/ToastContext.tsx */ \"(ssr)/./app/context/ToastContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CFlashSaleContext.tsx%22%2C%22ids%22%3A%5B%22FlashSaleProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CToastContext.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/components/Layout.tsx":
/*!***********************************!*\
  !*** ./app/components/Layout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Menu,ShoppingBag,ShoppingCart,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Menu,ShoppingBag,ShoppingCart,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Menu,ShoppingBag,ShoppingCart,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Menu,ShoppingBag,ShoppingCart,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Menu,ShoppingBag,ShoppingCart,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Menu,ShoppingBag,ShoppingCart,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _context_CartContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../context/CartContext */ \"(ssr)/./app/context/CartContext.tsx\");\n/* harmony import */ var _context_NotificationContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../context/NotificationContext */ \"(ssr)/./app/context/NotificationContext.tsx\");\n/* harmony import */ var _MobileMenu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./MobileMenu */ \"(ssr)/./app/components/MobileMenu.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nconst Layout = ({ children })=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_5__.useSession)();\n    const { state } = (0,_context_CartContext__WEBPACK_IMPORTED_MODULE_6__.useCart)();\n    const { unreadCount } = (0,_context_NotificationContext__WEBPACK_IMPORTED_MODULE_7__.useNotifications)();\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    const isActive = (path)=>pathname === path;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileMenu__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: isMenuOpen,\n                onClose: ()=>setIsMenuOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm sticky top-0 z-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-md mx-auto lg:hidden px-4 py-3 flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsMenuOpen(true),\n                                    className: \"p-2 rounded-full hover:bg-gray-100 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/logo.svg\",\n                                        alt: \"Herbalicious Logo\",\n                                        width: 60,\n                                        height: 60,\n                                        className: \"h-[40px] w-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/notifications\",\n                                    className: \"p-2 rounded-full hover:bg-gray-100 transition-colors relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-5 h-5 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isClient && session?.user && unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\",\n                                            children: unreadCount > 99 ? \"99+\" : unreadCount\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex px-4 py-3 items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/logo.svg\",\n                                        alt: \"Herbalicious Logo\",\n                                        width: 60,\n                                        height: 60,\n                                        className: \"h-[60px] w-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex items-center space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/\",\n                                            className: \"text-gray-600 hover:text-green-600 font-medium transition-colors\",\n                                            children: \"Home\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/shop\",\n                                            className: \"text-gray-600 hover:text-green-600 font-medium transition-colors\",\n                                            children: \"Shop\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/about\",\n                                            className: \"text-gray-600 hover:text-green-600 font-medium transition-colors\",\n                                            children: \"About\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/contact\",\n                                            className: \"text-gray-600 hover:text-green-600 font-medium transition-colors\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/faq\",\n                                            className: \"text-gray-600 hover:text-green-600 font-medium transition-colors\",\n                                            children: \"FAQs\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/notifications\",\n                                            className: \"p-2 rounded-full hover:bg-gray-100 transition-colors relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-5 h-5 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                isClient && session?.user && unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\",\n                                                    children: unreadCount > 99 ? \"99+\" : unreadCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/cart\",\n                                            className: \"relative p-2 rounded-full hover:bg-gray-100 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-5 h-5 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                isClient && state.itemCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\",\n                                                    children: state.itemCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/profile\",\n                                            className: \"p-2 rounded-full hover:bg-gray-100 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-5 h-5 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 pb-20 lg:pb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-md mx-auto lg:max-w-none\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 lg:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md mx-auto px-4 py-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-around\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: `flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${isActive(\"/\") ? \"text-green-600 bg-green-50\" : \"text-gray-500 hover:text-gray-700\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-6 h-6 mb-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-medium\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/shop\",\n                                className: `flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${isActive(\"/shop\") ? \"text-green-600 bg-green-50\" : \"text-gray-500 hover:text-gray-700\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-6 h-6 mb-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-medium\",\n                                        children: \"Shop\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/cart\",\n                                className: `flex flex-col items-center py-2 px-3 rounded-lg transition-colors relative ${isActive(\"/cart\") ? \"text-green-600 bg-green-50\" : \"text-gray-500 hover:text-gray-700\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-6 h-6 mb-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isClient && state.itemCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\",\n                                        children: state.itemCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-medium\",\n                                        children: \"Cart\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/profile\",\n                                className: `flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${isActive(\"/profile\") ? \"text-green-600 bg-green-50\" : \"text-gray-500 hover:text-gray-700\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Menu_ShoppingBag_ShoppingCart_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-6 h-6 mb-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-medium\",\n                                        children: \"Profile\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"hidden lg:block bg-white border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    \"\\xa9 \",\n                                    new Date().getFullYear(),\n                                    \" Herbalicious. All rights reserved.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/privacy\",\n                                        className: \"text-sm text-gray-500 hover:text-green-600\",\n                                        children: \"Privacy Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/terms\",\n                                        className: \"text-sm text-gray-500 hover:text-green-600\",\n                                        children: \"Terms and Conditions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/shipping\",\n                                        className: \"text-sm text-gray-500 hover:text-green-600\",\n                                        children: \"Shippings and Returns\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/faq\",\n                                        className: \"text-sm text-gray-500 hover:text-green-600\",\n                                        children: \"FAQs\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Layout.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/Layout.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/MobileMenu.tsx":
/*!***************************************!*\
  !*** ./app/components/MobileMenu.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,HelpCircle,Home,Info,Instagram,Mail,Search,Shield,ShoppingBag,Truck,Twitter,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,HelpCircle,Home,Info,Instagram,Mail,Search,Shield,ShoppingBag,Truck,Twitter,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,HelpCircle,Home,Info,Instagram,Mail,Search,Shield,ShoppingBag,Truck,Twitter,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,HelpCircle,Home,Info,Instagram,Mail,Search,Shield,ShoppingBag,Truck,Twitter,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,HelpCircle,Home,Info,Instagram,Mail,Search,Shield,ShoppingBag,Truck,Twitter,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,HelpCircle,Home,Info,Instagram,Mail,Search,Shield,ShoppingBag,Truck,Twitter,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,HelpCircle,Home,Info,Instagram,Mail,Search,Shield,ShoppingBag,Truck,Twitter,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,HelpCircle,Home,Info,Instagram,Mail,Search,Shield,ShoppingBag,Truck,Twitter,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/help-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,HelpCircle,Home,Info,Instagram,Mail,Search,Shield,ShoppingBag,Truck,Twitter,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,HelpCircle,Home,Info,Instagram,Mail,Search,Shield,ShoppingBag,Truck,Twitter,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,HelpCircle,Home,Info,Instagram,Mail,Search,Shield,ShoppingBag,Truck,Twitter,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,HelpCircle,Home,Info,Instagram,Mail,Search,Shield,ShoppingBag,Truck,Twitter,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,HelpCircle,Home,Info,Instagram,Mail,Search,Shield,ShoppingBag,Truck,Twitter,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst MobileMenu = ({ isOpen, onClose })=>{\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    if (!isOpen) return null;\n    const mainLinks = [\n        {\n            href: \"/\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-5 h-5 text-white\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                lineNumber: 32,\n                columnNumber: 24\n            }, undefined),\n            label: \"Home\"\n        },\n        {\n            href: \"/shop\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-5 h-5 text-white\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                lineNumber: 35,\n                columnNumber: 13\n            }, undefined),\n            label: \"Shop\"\n        },\n        {\n            href: \"/about\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"w-5 h-5 text-white\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                lineNumber: 40,\n                columnNumber: 13\n            }, undefined),\n            label: \"About\"\n        },\n        {\n            href: \"/contact\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"w-5 h-5 text-white\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                lineNumber: 45,\n                columnNumber: 13\n            }, undefined),\n            label: \"Contact\"\n        }\n    ];\n    const secondaryLinks = [\n        {\n            href: \"/privacy\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"w-5 h-5 text-white\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                lineNumber: 53,\n                columnNumber: 13\n            }, undefined),\n            label: \"Privacy Policy\"\n        },\n        {\n            href: \"/terms\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"w-5 h-5 text-white\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                lineNumber: 58,\n                columnNumber: 13\n            }, undefined),\n            label: \"Terms and Conditions\"\n        },\n        {\n            href: \"/shipping\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"w-5 h-5 text-white\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                lineNumber: 63,\n                columnNumber: 13\n            }, undefined),\n            label: \"Shippings and Returns\"\n        },\n        {\n            href: \"/faq\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"w-5 h-5 text-white\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                lineNumber: 68,\n                columnNumber: 13\n            }, undefined),\n            label: \"FAQs\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 z-50 lg:hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed left-0 top-0 h-full w-80 bg-white shadow-xl z-60\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 flex items-center justify-between border-b\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-bold text-lg\",\n                                children: [\n                                    \"Welcome, \",\n                                    session?.user?.name?.split(\" \")[0] || \"User\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"p-2 rounded-full hover:bg-gray-100\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-5 h-5 text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search products...\",\n                                    className: \"w-full bg-gray-100 border border-gray-200 rounded-full py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex-1 overflow-y-auto p-4 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-semibold text-gray-500 uppercase tracking-wider mb-2\",\n                                        children: \"Main\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-1\",\n                                        children: mainLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: link.href,\n                                                    onClick: onClose,\n                                                    className: \"flex items-center p-2 rounded-lg hover:bg-gray-100 text-gray-700 font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mr-3 bg-green-600 p-2 rounded-full\",\n                                                            children: link.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                                            lineNumber: 116,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        link.label\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, link.href, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-semibold text-gray-500 uppercase tracking-wider mb-2\",\n                                        children: \"Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-1\",\n                                        children: secondaryLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: link.href,\n                                                    onClick: onClose,\n                                                    className: \"flex items-center p-2 rounded-lg hover:bg-gray-100 text-gray-700 font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mr-3 bg-green-600 p-2 rounded-full\",\n                                                            children: link.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        link.label\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, link.href, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"p-2 text-gray-500 hover:text-green-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"p-2 text-gray-500 hover:text-green-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"p-2 text-gray-500 hover:text-green-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_FileText_HelpCircle_Home_Info_Instagram_Mail_Search_Shield_ShoppingBag_Truck_Twitter_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\MobileMenu.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MobileMenu);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/MobileMenu.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/Toast.tsx":
/*!**********************************!*\
  !*** ./app/components/Toast.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Info,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Info,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Info,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Info,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst toastIcons = {\n    success: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        className: \"w-5 h-5 text-white\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Toast.tsx\",\n        lineNumber: 15,\n        columnNumber: 12\n    }, undefined),\n    error: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        className: \"w-5 h-5 text-white\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Toast.tsx\",\n        lineNumber: 16,\n        columnNumber: 10\n    }, undefined),\n    info: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        className: \"w-5 h-5 text-white\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Toast.tsx\",\n        lineNumber: 17,\n        columnNumber: 9\n    }, undefined)\n};\nconst toastColors = {\n    success: \"bg-green-500\",\n    error: \"bg-red-500\",\n    info: \"bg-blue-500\"\n};\nconst Toast = ({ message, type, onClose })=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            onClose();\n        }, 5000);\n        return ()=>{\n            clearTimeout(timer);\n        };\n    }, [\n        onClose\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `fixed bottom-5 right-5 flex items-center p-4 rounded-lg shadow-lg text-white ${toastColors[type]} z-50 transition-transform transform animate-slide-in`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mr-3\",\n                children: toastIcons[type]\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Toast.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: message\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Toast.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onClose,\n                className: \"ml-4 p-1 rounded-full hover:bg-white/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Info_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Toast.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Toast.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\Toast.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Toast);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/Toast.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ToastContainer.tsx":
/*!*******************************************!*\
  !*** ./app/components/ToastContainer.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Toast */ \"(ssr)/./app/components/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst ToastContainer = ({ toasts, hideToast })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-5 right-5 z-50\",\n        children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                message: toast.message,\n                type: toast.type,\n                onClose: ()=>hideToast(toast.id)\n            }, toast.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\ToastContainer.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\ToastContainer.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ToastContainer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9Ub2FzdENvbnRhaW5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUUwQjtBQUNFO0FBUTVCLE1BQU1FLGlCQUFnRCxDQUFDLEVBQ3JEQyxNQUFNLEVBQ05DLFNBQVMsRUFDVjtJQUNDLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNaSCxPQUFPSSxHQUFHLENBQUMsQ0FBQ0Msc0JBQ1gsOERBQUNQLDhDQUFLQTtnQkFFSlEsU0FBU0QsTUFBTUMsT0FBTztnQkFDdEJDLE1BQU1GLE1BQU1FLElBQUk7Z0JBQ2hCQyxTQUFTLElBQU1QLFVBQVVJLE1BQU1JLEVBQUU7ZUFINUJKLE1BQU1JLEVBQUU7Ozs7Ozs7Ozs7QUFRdkI7QUFFQSxpRUFBZVYsY0FBY0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2hlcmJhbGljaW91cy1uZXh0anMvLi9hcHAvY29tcG9uZW50cy9Ub2FzdENvbnRhaW5lci50c3g/NTRhMiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IFRvYXN0IGZyb20gXCIuL1RvYXN0XCI7XG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gXCIuLi9ob29rcy91c2VUb2FzdFwiO1xuXG5pbnRlcmZhY2UgVG9hc3RDb250YWluZXJQcm9wcyB7XG4gIHRvYXN0czogeyBpZDogbnVtYmVyOyBtZXNzYWdlOiBzdHJpbmc7IHR5cGU6IFwic3VjY2Vzc1wiIHwgXCJlcnJvclwiIHwgXCJpbmZvXCIgfVtdO1xuICBoaWRlVG9hc3Q6IChpZDogbnVtYmVyKSA9PiB2b2lkO1xufVxuXG5jb25zdCBUb2FzdENvbnRhaW5lcjogUmVhY3QuRkM8VG9hc3RDb250YWluZXJQcm9wcz4gPSAoe1xuICB0b2FzdHMsXG4gIGhpZGVUb2FzdCxcbn0pID0+IHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGJvdHRvbS01IHJpZ2h0LTUgei01MFwiPlxuICAgICAge3RvYXN0cy5tYXAoKHRvYXN0KSA9PiAoXG4gICAgICAgIDxUb2FzdFxuICAgICAgICAgIGtleT17dG9hc3QuaWR9XG4gICAgICAgICAgbWVzc2FnZT17dG9hc3QubWVzc2FnZX1cbiAgICAgICAgICB0eXBlPXt0b2FzdC50eXBlfVxuICAgICAgICAgIG9uQ2xvc2U9eygpID0+IGhpZGVUb2FzdCh0b2FzdC5pZCl9XG4gICAgICAgIC8+XG4gICAgICApKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFRvYXN0Q29udGFpbmVyO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiVG9hc3QiLCJUb2FzdENvbnRhaW5lciIsInRvYXN0cyIsImhpZGVUb2FzdCIsImRpdiIsImNsYXNzTmFtZSIsIm1hcCIsInRvYXN0IiwibWVzc2FnZSIsInR5cGUiLCJvbkNsb3NlIiwiaWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ToastContainer.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/pages/Profile.tsx":
/*!******************************************!*\
  !*** ./app/components/pages/Profile.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_ChevronRight_Heart_LogOut_Package_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,Heart,LogOut,Package,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_Heart_LogOut_Package_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,Heart,LogOut,Package,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_Heart_LogOut_Package_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,Heart,LogOut,Package,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_Heart_LogOut_Package_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,Heart,LogOut,Package,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_Heart_LogOut_Package_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,Heart,LogOut,Package,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_Heart_LogOut_Package_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,Heart,LogOut,Package,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst Profile = ()=>{\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [userStats, setUserStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Authentication is now handled by the server-side layout\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchUserStats = async ()=>{\n            if (!session?.user?.id) return;\n            try {\n                const response = await fetch(`/api/users/${session.user.id}/stats`);\n                if (response.ok) {\n                    const data = await response.json();\n                    setUserStats(data.data);\n                }\n            } catch (error) {\n                console.error(\"Error fetching user stats:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchUserStats();\n    }, [\n        session?.user?.id\n    ]);\n    const user = {\n        name: session?.user?.name || \"User\",\n        email: session?.user?.email || \"\",\n        joinDate: userStats?.user?.joinDate ? new Date(userStats.user.joinDate).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"long\"\n        }) : \"Loading...\",\n        totalOrders: userStats?.orders?.total || 0,\n        isAdmin: session?.user?.role === \"ADMIN\",\n        accountStatus: userStats?.accountStatus || \"Loading...\"\n    };\n    const handleSignOut = async ()=>{\n        try {\n            await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signOut)({\n                redirect: false,\n                callbackUrl: \"/\"\n            });\n            // Use replace to avoid back button issues\n            window.location.replace(\"/\");\n        } catch (error) {\n            console.error(\"Error signing out:\", error);\n            // Fallback redirect\n            window.location.replace(\"/\");\n        }\n    };\n    const menuItems = [\n        {\n            icon: _barrel_optimize_names_ChevronRight_Heart_LogOut_Package_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: \"Order History\",\n            description: \"View your past orders\",\n            href: \"/order-history\",\n            color: \"bg-green-100 text-green-600\"\n        },\n        {\n            icon: _barrel_optimize_names_ChevronRight_Heart_LogOut_Package_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: \"Wishlist\",\n            description: \"Your saved items\",\n            href: \"/wishlist\",\n            color: \"bg-green-100 text-green-600\"\n        },\n        {\n            icon: _barrel_optimize_names_ChevronRight_Heart_LogOut_Package_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: \"Edit Profile\",\n            description: \"Update your profile details\",\n            href: \"/edit-profile\",\n            color: \"bg-green-100 text-green-600\"\n        }\n    ];\n    const adminItems = [\n        {\n            icon: _barrel_optimize_names_ChevronRight_Heart_LogOut_Package_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            title: \"Admin Panel\",\n            description: \"Manage store (Admin only)\",\n            href: \"/admin\",\n            color: \"bg-green-100 text-green-600\"\n        }\n    ];\n    // Show loading state while checking authentication\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 border-4 border-green-600 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                lineNumber: 135,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n            lineNumber: 134,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Don't render content if not authenticated\n    if (status === \"unauthenticated\") {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 grid grid-cols-1 lg:grid-cols-12 gap-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden col-span-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-green-600 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Heart_LogOut_Package_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-8 h-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-gray-800\",\n                                                children: user.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: user.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: [\n                                                    \"Member since \",\n                                                    user.joinDate\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl p-4 shadow-sm border border-gray-100 text-center mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-gray-800\",\n                                    children: user.totalOrders\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Total Orders\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                menuItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: item.href,\n                                        className: \"flex items-center space-x-4 p-4 bg-white rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `w-12 h-12 rounded-full flex items-center justify-center ${item.color}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"w-6 h-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-gray-800\",\n                                                        children: item.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: item.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Heart_LogOut_Package_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined)),\n                                user.isAdmin && adminItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: item.href,\n                                        className: \"flex items-center space-x-4 p-4 bg-white rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `w-12 h-12 rounded-full flex items-center justify-center ${item.color}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"w-6 h-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-gray-800\",\n                                                        children: item.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: item.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Heart_LogOut_Package_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, `admin-${index}`, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, undefined))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSignOut,\n                                className: \"w-full flex items-center justify-center space-x-2 p-4 bg-red-50 text-red-600 rounded-2xl font-medium hover:bg-red-100 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Heart_LogOut_Package_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Sign Out\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:block lg:col-span-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-800 mb-12\",\n                            children: \"Profile\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-2xl p-8 shadow-sm border border-gray-100 mb-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-24 h-24 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Heart_LogOut_Package_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-12 h-12 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-800 mb-1\",\n                                                        children: user.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-1\",\n                                                        children: user.email\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            \"Member since \",\n                                                            user.joinDate\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-2xl p-8 shadow-sm border border-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                                    children: \"Account Stats\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Total Orders\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                                    lineNumber: 265,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold text-gray-800\",\n                                                                    children: user.totalOrders\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Account Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold text-green-600\",\n                                                                    children: user.accountStatus\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2 space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-2xl p-8 shadow-sm border border-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-semibold text-gray-800 mb-6\",\n                                                    children: \"Quick Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: menuItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            href: item.href,\n                                                            className: \"flex items-center space-x-3 p-4 rounded-xl hover:bg-gray-50 transition-colors border border-gray-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `w-10 h-10 rounded-full flex items-center justify-center ${item.color}`,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                        className: \"w-5 h-5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                                        lineNumber: 297,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                                    lineNumber: 294,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-medium text-gray-800\",\n                                                                            children: item.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                                            lineNumber: 300,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: item.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                                            lineNumber: 303,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-2xl p-8 shadow-sm border border-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-semibold text-gray-800 mb-6\",\n                                                    children: \"Account Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            href: \"/edit-profile\",\n                                                            className: \"flex items-center space-x-2 px-6 py-3 bg-green-600 text-white rounded-xl font-medium hover:bg-green-700 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Heart_LogOut_Package_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Edit Profile\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        user.isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            href: \"/admin\",\n                                                            className: \"flex items-center space-x-2 px-6 py-3 bg-green-700 text-white rounded-xl font-medium hover:bg-green-800 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Heart_LogOut_Package_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Admin Panel\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleSignOut,\n                                                            className: \"flex items-center space-x-2 px-6 py-3 bg-red-50 text-red-600 rounded-xl font-medium hover:bg-red-100 transition-colors ml-auto\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Heart_LogOut_Package_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Sign Out\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\components\\\\pages\\\\Profile.tsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Profile);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/pages/Profile.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/CartContext.tsx":
/*!*************************************!*\
  !*** ./app/context/CartContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartProvider: () => (/* binding */ CartProvider),\n/* harmony export */   useCart: () => (/* binding */ useCart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ CartProvider,useCart auto */ \n\nconst CartContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\n// localStorage utilities\nconst CART_STORAGE_KEY = \"herbalicious_cart\";\nconst saveCartToStorage = (state)=>{\n    try {\n        const { isHydrated, ...stateToSave } = state;\n        if (false) {}\n    } catch (error) {\n        console.error(\"Error saving cart to localStorage:\", error);\n    }\n};\nconst loadCartFromStorage = ()=>{\n    try {\n        if (false) {}\n    } catch (error) {\n        console.error(\"Error loading cart from localStorage:\", error);\n    }\n    return null;\n};\n// Helper function to generate unique variant key\nconst generateVariantKey = (productId, selectedVariants)=>{\n    if (!selectedVariants || selectedVariants.length === 0) {\n        return productId;\n    }\n    const sortedVariants = [\n        ...selectedVariants\n    ].sort((a, b)=>a.name.localeCompare(b.name));\n    const variantString = sortedVariants.map((v)=>`${v.name}:${v.value}`).join(\"|\");\n    return `${productId}__${variantString}`;\n};\n// Helper function to get item identifier\nconst getItemIdentifier = (item)=>{\n    return item.variantKey || item.product?.id || item.id;\n};\nconst getInitialCartState = ()=>{\n    return {\n        items: [],\n        total: 0,\n        subtotal: 0,\n        itemCount: 0,\n        finalTotal: 0,\n        coupons: {\n            appliedCoupons: [],\n            totalDiscount: 0,\n            availableCoupons: []\n        },\n        flashSaleDiscount: 0,\n        flashSaleActive: false,\n        flashSalePercentage: 0,\n        isHydrated: false\n    };\n};\n// Default flash sale settings\nconst DEFAULT_FLASH_SALE_PERCENTAGE = 25;\n// Helper function to get flash sale settings (client-side only)\nconst getFlashSaleSettings = ()=>{\n    try {\n        if (false) {}\n    } catch (error) {\n        console.error(\"Error loading flash sale settings:\", error);\n    }\n    return {\n        flashSaleActive: false,\n        flashSalePercentage: 0\n    };\n};\n// Helper function to calculate flash sale prices\nconst calculateFlashSalePrices = (items, appliedCoupons, flashSaleActive, flashSalePercentage)=>{\n    let subtotal = 0;\n    let flashSaleDiscount = 0;\n    items.forEach((item)=>{\n        const originalPrice = item.product.price;\n        if (flashSaleActive && flashSalePercentage > 0) {\n            const discountAmount = originalPrice * flashSalePercentage / 100;\n            const salePrice = originalPrice - discountAmount;\n            subtotal += salePrice * item.quantity;\n            flashSaleDiscount += discountAmount * item.quantity;\n        } else {\n            subtotal += originalPrice * item.quantity;\n        }\n    });\n    const itemCount = items.reduce((sum, item)=>sum + item.quantity, 0);\n    const couponDiscount = appliedCoupons.reduce((sum, coupon)=>sum + coupon.discountAmount, 0);\n    const finalTotal = subtotal - couponDiscount;\n    return {\n        subtotal,\n        itemCount,\n        total: subtotal,\n        finalTotal,\n        flashSaleDiscount,\n        couponDiscount\n    };\n};\nconst cartReducer = (state, action)=>{\n    // Get flash sale settings - use client-side only to prevent hydration issues\n    const { flashSaleActive, flashSalePercentage } =  false ? 0 : {\n        flashSaleActive: false,\n        flashSalePercentage: 0\n    };\n    switch(action.type){\n        case \"SET_CART_STATE\":\n            return {\n                ...action.payload,\n                isHydrated: true\n            };\n        case \"ADD_ITEM\":\n            {\n                const variantKey = generateVariantKey(action.payload.id, action.selectedVariants);\n                const existingItem = state.items.find((item)=>getItemIdentifier(item) === variantKey);\n                let updatedItems;\n                if (existingItem) {\n                    updatedItems = state.items.map((item)=>getItemIdentifier(item) === variantKey ? {\n                            ...item,\n                            quantity: item.quantity + 1,\n                            variantKey\n                        } : item);\n                } else {\n                    const newCartItem = {\n                        product: action.payload,\n                        quantity: 1,\n                        selectedVariants: action.selectedVariants || [],\n                        variantKey\n                    };\n                    updatedItems = [\n                        ...state.items,\n                        newCartItem\n                    ];\n                }\n                const prices = calculateFlashSalePrices(updatedItems, state.coupons.appliedCoupons, flashSaleActive, flashSalePercentage);\n                const newState = {\n                    ...state,\n                    items: updatedItems,\n                    subtotal: prices.subtotal,\n                    itemCount: prices.itemCount,\n                    total: prices.total,\n                    finalTotal: prices.finalTotal,\n                    coupons: {\n                        ...state.coupons,\n                        totalDiscount: prices.couponDiscount\n                    },\n                    flashSaleDiscount: prices.flashSaleDiscount,\n                    flashSaleActive,\n                    flashSalePercentage\n                };\n                saveCartToStorage(newState);\n                return newState;\n            }\n        case \"REMOVE_ITEM\":\n            {\n                const filteredItems = state.items.filter((item)=>getItemIdentifier(item) !== action.payload);\n                const prices = calculateFlashSalePrices(filteredItems, state.coupons.appliedCoupons, flashSaleActive, flashSalePercentage);\n                const newState = {\n                    ...state,\n                    items: filteredItems,\n                    subtotal: prices.subtotal,\n                    itemCount: prices.itemCount,\n                    total: prices.total,\n                    finalTotal: prices.finalTotal,\n                    coupons: {\n                        ...state.coupons,\n                        totalDiscount: prices.couponDiscount\n                    },\n                    flashSaleDiscount: prices.flashSaleDiscount,\n                    flashSaleActive,\n                    flashSalePercentage\n                };\n                saveCartToStorage(newState);\n                return newState;\n            }\n        case \"UPDATE_QUANTITY\":\n            {\n                const updatedItems = state.items.map((item)=>getItemIdentifier(item) === action.payload.id ? {\n                        ...item,\n                        quantity: action.payload.quantity\n                    } : item).filter((item)=>item.quantity > 0);\n                const prices = calculateFlashSalePrices(updatedItems, state.coupons.appliedCoupons, flashSaleActive, flashSalePercentage);\n                const newState = {\n                    ...state,\n                    items: updatedItems,\n                    subtotal: prices.subtotal,\n                    itemCount: prices.itemCount,\n                    total: prices.total,\n                    finalTotal: prices.finalTotal,\n                    coupons: {\n                        ...state.coupons,\n                        totalDiscount: prices.couponDiscount\n                    },\n                    flashSaleDiscount: prices.flashSaleDiscount,\n                    flashSaleActive,\n                    flashSalePercentage\n                };\n                saveCartToStorage(newState);\n                return newState;\n            }\n        case \"APPLY_COUPON\":\n            {\n                const isAlreadyApplied = state.coupons.appliedCoupons.some((coupon)=>coupon.coupon.id === action.payload.coupon.id);\n                if (isAlreadyApplied) {\n                    return state;\n                }\n                const hasNonStackableCoupon = state.coupons.appliedCoupons.some((coupon)=>!coupon.coupon.isStackable);\n                if (hasNonStackableCoupon && !action.payload.coupon.isStackable) {\n                    return state;\n                }\n                const updatedAppliedCoupons = [\n                    ...state.coupons.appliedCoupons,\n                    action.payload\n                ];\n                const prices = calculateFlashSalePrices(state.items, updatedAppliedCoupons, flashSaleActive, flashSalePercentage);\n                const newState = {\n                    ...state,\n                    subtotal: prices.subtotal,\n                    itemCount: prices.itemCount,\n                    total: prices.total,\n                    finalTotal: prices.finalTotal,\n                    coupons: {\n                        ...state.coupons,\n                        appliedCoupons: updatedAppliedCoupons,\n                        totalDiscount: prices.couponDiscount\n                    },\n                    flashSaleDiscount: prices.flashSaleDiscount,\n                    flashSaleActive,\n                    flashSalePercentage\n                };\n                saveCartToStorage(newState);\n                return newState;\n            }\n        case \"REMOVE_COUPON\":\n            {\n                const updatedAppliedCoupons = state.coupons.appliedCoupons.filter((coupon)=>coupon.coupon.id !== action.payload);\n                const prices = calculateFlashSalePrices(state.items, updatedAppliedCoupons, flashSaleActive, flashSalePercentage);\n                const newState = {\n                    ...state,\n                    subtotal: prices.subtotal,\n                    itemCount: prices.itemCount,\n                    total: prices.total,\n                    finalTotal: prices.finalTotal,\n                    coupons: {\n                        ...state.coupons,\n                        appliedCoupons: updatedAppliedCoupons,\n                        totalDiscount: prices.couponDiscount\n                    },\n                    flashSaleDiscount: prices.flashSaleDiscount,\n                    flashSaleActive,\n                    flashSalePercentage\n                };\n                saveCartToStorage(newState);\n                return newState;\n            }\n        case \"CLEAR_COUPONS\":\n            {\n                const prices = calculateFlashSalePrices(state.items, [], flashSaleActive, flashSalePercentage);\n                const newState = {\n                    ...state,\n                    subtotal: prices.subtotal,\n                    itemCount: prices.itemCount,\n                    total: prices.total,\n                    finalTotal: prices.finalTotal,\n                    coupons: {\n                        appliedCoupons: [],\n                        totalDiscount: 0,\n                        availableCoupons: []\n                    },\n                    flashSaleDiscount: prices.flashSaleDiscount,\n                    flashSaleActive,\n                    flashSalePercentage\n                };\n                saveCartToStorage(newState);\n                return newState;\n            }\n        case \"CLEAR_CART\":\n            {\n                const newState = {\n                    ...getInitialCartState(),\n                    isHydrated: true,\n                    flashSaleActive,\n                    flashSalePercentage\n                };\n                saveCartToStorage(newState);\n                return newState;\n            }\n        default:\n            return state;\n    }\n};\nconst CartProvider = ({ children })=>{\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(cartReducer, getInitialCartState());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const storedCart = loadCartFromStorage();\n        if (storedCart) {\n            dispatch({\n                type: \"SET_CART_STATE\",\n                payload: storedCart\n            });\n        } else {\n            dispatch({\n                type: \"SET_CART_STATE\",\n                payload: getInitialCartState()\n            });\n        }\n    }, []);\n    // Re-calculate cart when flash sale settings change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (state.isHydrated && state.items.length > 0) {\n            dispatch({\n                type: \"UPDATE_QUANTITY\",\n                payload: {\n                    id: state.items[0].variantKey || state.items[0].product.id,\n                    quantity: state.items[0].quantity\n                }\n            });\n        }\n    }, [\n        state.isHydrated\n    ]);\n    // Listen for flash sale settings changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!state.isHydrated) return;\n        const handleStorageChange = (e)=>{\n            if (e.key === \"flashSaleSettings\" && state.items.length > 0) {\n                dispatch({\n                    type: \"UPDATE_QUANTITY\",\n                    payload: {\n                        id: state.items[0].variantKey || state.items[0].product.id,\n                        quantity: state.items[0].quantity\n                    }\n                });\n            }\n        };\n        const handleFlashSaleUpdate = ()=>{\n            if (state.items.length > 0) {\n                dispatch({\n                    type: \"UPDATE_QUANTITY\",\n                    payload: {\n                        id: state.items[0].variantKey || state.items[0].product.id,\n                        quantity: state.items[0].quantity\n                    }\n                });\n            }\n        };\n        window.addEventListener(\"storage\", handleStorageChange);\n        window.addEventListener(\"flashSaleSettingsUpdated\", handleFlashSaleUpdate);\n        return ()=>{\n            window.removeEventListener(\"storage\", handleStorageChange);\n            window.removeEventListener(\"flashSaleSettingsUpdated\", handleFlashSaleUpdate);\n        };\n    }, [\n        state.isHydrated,\n        state.items\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CartContext.Provider, {\n        value: {\n            state,\n            dispatch\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\context\\\\CartContext.tsx\",\n        lineNumber: 523,\n        columnNumber: 5\n    }, undefined);\n};\nconst useCart = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CartContext);\n    if (!context) {\n        throw new Error(\"useCart must be used within a CartProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/context/CartContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/FlashSaleContext.tsx":
/*!******************************************!*\
  !*** ./app/context/FlashSaleContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FlashSaleProvider: () => (/* binding */ FlashSaleProvider),\n/* harmony export */   useFlashSale: () => (/* binding */ useFlashSale)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ FlashSaleProvider,useFlashSale auto */ \n\nconst FlashSaleContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction FlashSaleProvider({ children }) {\n    const [flashSaleSettings, setFlashSaleSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isHydrated, setIsHydrated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fetchFlashSaleSettings = async ()=>{\n        try {\n            const response = await fetch(\"/api/homepage-settings\");\n            const data = await response.json();\n            if (data.success && data.data.settings) {\n                const settings = data.data.settings;\n                const flashSaleData = {\n                    showFlashSale: settings.showFlashSale,\n                    flashSaleEndDate: settings.flashSaleEndDate,\n                    flashSalePercentage: settings.flashSalePercentage,\n                    flashSaleTitle: settings.flashSaleTitle,\n                    flashSaleSubtitle: settings.flashSaleSubtitle,\n                    flashSaleBackgroundColor: settings.flashSaleBackgroundColor\n                };\n                setFlashSaleSettings(flashSaleData);\n                // Store in localStorage for cart to access\n                if (false) {}\n            }\n        } catch (error) {\n            console.error(\"Error fetching flash sale settings:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchFlashSaleSettings().then(()=>{\n            setIsHydrated(true);\n        });\n    }, []);\n    const refreshSettings = async ()=>{\n        setLoading(true);\n        await fetchFlashSaleSettings();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FlashSaleContext.Provider, {\n        value: {\n            flashSaleSettings,\n            loading,\n            isHydrated,\n            refreshSettings\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\context\\\\FlashSaleContext.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\nfunction useFlashSale() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(FlashSaleContext);\n    if (context === undefined) {\n        throw new Error(\"useFlashSale must be used within a FlashSaleProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/context/FlashSaleContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/NotificationContext.tsx":
/*!*********************************************!*\
  !*** ./app/context/NotificationContext.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationProvider: () => (/* binding */ NotificationProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useNotifications: () => (/* binding */ useNotifications)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ useNotifications,NotificationProvider,default auto */ \n\n\nconst NotificationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useNotifications = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NotificationContext);\n    if (context === undefined) {\n        throw new Error(\"useNotifications must be used within a NotificationProvider\");\n    }\n    return context;\n};\nconst NotificationProvider = ({ children })=>{\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [prevUnreadCount, setPrevUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const fetchNotifications = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (options = {})=>{\n        if (!session?.user?.id) return;\n        try {\n            setLoading(true);\n            setError(null);\n            const params = new URLSearchParams({\n                page: (options.page || 1).toString(),\n                limit: (options.limit || 10).toString(),\n                ...options.unreadOnly && {\n                    unreadOnly: \"true\"\n                }\n            });\n            const response = await fetch(`/api/notifications?${params}`);\n            const data = await response.json();\n            if (data.success) {\n                setNotifications(data.data.notifications);\n                setUnreadCount(data.data.unreadCount);\n            } else {\n                setError(data.error || \"Failed to fetch notifications\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching notifications:\", error);\n            setError(\"Failed to fetch notifications\");\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        session?.user?.id\n    ]);\n    const refreshNotifications = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        await fetchNotifications({\n            limit: 10\n        });\n    }, [\n        fetchNotifications\n    ]);\n    const refreshUnreadCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!session?.user?.id) return;\n        try {\n            const response = await fetch(\"/api/notifications/unread-count\");\n            const data = await response.json();\n            if (data.success) {\n                if (data.unreadCount > prevUnreadCount) {\n                    fetchNotifications({\n                        limit: 10\n                    });\n                }\n                setPrevUnreadCount(data.unreadCount);\n                setUnreadCount(data.unreadCount);\n            }\n        } catch (error) {\n            console.error(\"Error fetching unread count:\", error);\n        }\n    }, [\n        session?.user?.id\n    ]);\n    const markAsRead = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (notificationId)=>{\n        if (!session?.user?.id) return;\n        try {\n            const response = await fetch(`/api/notifications/${notificationId}/read`, {\n                method: \"POST\"\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Update local state\n                setNotifications((prev)=>prev.map((notification)=>notification.id === notificationId ? {\n                            ...notification,\n                            isRead: true\n                        } : notification));\n                // Update unread count\n                setUnreadCount((prev)=>Math.max(0, prev - 1));\n            } else {\n                setError(data.error || \"Failed to mark notification as read\");\n            }\n        } catch (error) {\n            console.error(\"Error marking notification as read:\", error);\n            setError(\"Failed to mark notification as read\");\n        }\n    }, [\n        session?.user?.id\n    ]);\n    const markAllAsRead = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!session?.user?.id) return;\n        try {\n            const response = await fetch(\"/api/notifications/mark-all-read\", {\n                method: \"POST\"\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Update local state\n                setNotifications((prev)=>prev.map((notification)=>({\n                            ...notification,\n                            isRead: true\n                        })));\n                setUnreadCount(0);\n            } else {\n                setError(data.error || \"Failed to mark all notifications as read\");\n            }\n        } catch (error) {\n            console.error(\"Error marking all notifications as read:\", error);\n            setError(\"Failed to mark all notifications as read\");\n        }\n    }, [\n        session?.user?.id\n    ]);\n    // Fetch notifications when user logs in\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (status === \"authenticated\" && session?.user?.id) {\n            fetchNotifications({\n                limit: 5\n            }); // Fetch recent notifications for dropdown\n            refreshUnreadCount();\n        }\n    }, [\n        status,\n        session?.user?.id,\n        fetchNotifications,\n        refreshUnreadCount\n    ]);\n    // Refresh unread count periodically (every 30 seconds)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!session?.user?.id) return;\n        const interval = setInterval(()=>{\n            refreshUnreadCount();\n        }, 30000); // 30 seconds\n        return ()=>clearInterval(interval);\n    }, [\n        session?.user?.id,\n        refreshUnreadCount\n    ]);\n    const value = {\n        notifications,\n        unreadCount,\n        loading,\n        error,\n        fetchNotifications,\n        markAsRead,\n        markAllAsRead,\n        refreshUnreadCount,\n        refreshNotifications\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\context\\\\NotificationContext.tsx\",\n        lineNumber: 218,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NotificationProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29udGV4dC9Ob3RpZmljYXRpb25Db250ZXh0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBUWU7QUFDOEI7QUE0QjdDLE1BQU1PLG9DQUFzQk4sb0RBQWFBLENBQ3ZDTztBQUdLLE1BQU1DLG1CQUFtQjtJQUM5QixNQUFNQyxVQUFVUixpREFBVUEsQ0FBQ0s7SUFDM0IsSUFBSUcsWUFBWUYsV0FBVztRQUN6QixNQUFNLElBQUlHLE1BQ1I7SUFFSjtJQUNBLE9BQU9EO0FBQ1QsRUFBRTtBQU1LLE1BQU1FLHVCQUE0RCxDQUFDLEVBQ3hFQyxRQUFRLEVBQ1Q7SUFDQyxNQUFNLEVBQUVDLE1BQU1DLE9BQU8sRUFBRUMsTUFBTSxFQUFFLEdBQUdWLDJEQUFVQTtJQUM1QyxNQUFNLENBQUNXLGVBQWVDLGlCQUFpQixHQUFHZiwrQ0FBUUEsQ0FBaUIsRUFBRTtJQUNyRSxNQUFNLENBQUNnQixhQUFhQyxlQUFlLEdBQUdqQiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNrQixTQUFTQyxXQUFXLEdBQUduQiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNvQixPQUFPQyxTQUFTLEdBQUdyQiwrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDc0IsaUJBQWlCQyxtQkFBbUIsR0FBR3ZCLCtDQUFRQSxDQUFDO0lBRXZELE1BQU13QixxQkFBcUJ0QixrREFBV0EsQ0FDcEMsT0FDRXVCLFVBQW1FLENBQUMsQ0FBQztRQUVyRSxJQUFJLENBQUNiLFNBQVNjLE1BQU1DLElBQUk7UUFFeEIsSUFBSTtZQUNGUixXQUFXO1lBQ1hFLFNBQVM7WUFFVCxNQUFNTyxTQUFTLElBQUlDLGdCQUFnQjtnQkFDakNDLE1BQU0sQ0FBQ0wsUUFBUUssSUFBSSxJQUFJLEdBQUdDLFFBQVE7Z0JBQ2xDQyxPQUFPLENBQUNQLFFBQVFPLEtBQUssSUFBSSxFQUFDLEVBQUdELFFBQVE7Z0JBQ3JDLEdBQUlOLFFBQVFRLFVBQVUsSUFBSTtvQkFBRUEsWUFBWTtnQkFBTyxDQUFDO1lBQ2xEO1lBRUEsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLENBQUMsbUJBQW1CLEVBQUVQLE9BQU8sQ0FBQztZQUMzRCxNQUFNakIsT0FBTyxNQUFNdUIsU0FBU0UsSUFBSTtZQUVoQyxJQUFJekIsS0FBSzBCLE9BQU8sRUFBRTtnQkFDaEJ0QixpQkFBaUJKLEtBQUtBLElBQUksQ0FBQ0csYUFBYTtnQkFDeENHLGVBQWVOLEtBQUtBLElBQUksQ0FBQ0ssV0FBVztZQUN0QyxPQUFPO2dCQUNMSyxTQUFTVixLQUFLUyxLQUFLLElBQUk7WUFDekI7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZGtCLFFBQVFsQixLQUFLLENBQUMsaUNBQWlDQTtZQUMvQ0MsU0FBUztRQUNYLFNBQVU7WUFDUkYsV0FBVztRQUNiO0lBQ0YsR0FDQTtRQUFDUCxTQUFTYyxNQUFNQztLQUFHO0lBR3JCLE1BQU1ZLHVCQUF1QnJDLGtEQUFXQSxDQUFDO1FBQ3ZDLE1BQU1zQixtQkFBbUI7WUFBRVEsT0FBTztRQUFHO0lBQ3ZDLEdBQUc7UUFBQ1I7S0FBbUI7SUFFdkIsTUFBTWdCLHFCQUFxQnRDLGtEQUFXQSxDQUFDO1FBQ3JDLElBQUksQ0FBQ1UsU0FBU2MsTUFBTUMsSUFBSTtRQUV4QixJQUFJO1lBQ0YsTUFBTU8sV0FBVyxNQUFNQyxNQUFNO1lBQzdCLE1BQU14QixPQUFPLE1BQU11QixTQUFTRSxJQUFJO1lBRWhDLElBQUl6QixLQUFLMEIsT0FBTyxFQUFFO2dCQUNoQixJQUFJMUIsS0FBS0ssV0FBVyxHQUFHTSxpQkFBaUI7b0JBQ3RDRSxtQkFBbUI7d0JBQUVRLE9BQU87b0JBQUc7Z0JBQ2pDO2dCQUNBVCxtQkFBbUJaLEtBQUtLLFdBQVc7Z0JBQ25DQyxlQUFlTixLQUFLSyxXQUFXO1lBQ2pDO1FBQ0YsRUFBRSxPQUFPSSxPQUFPO1lBQ2RrQixRQUFRbEIsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDaEQ7SUFDRixHQUFHO1FBQUNSLFNBQVNjLE1BQU1DO0tBQUc7SUFFdEIsTUFBTWMsYUFBYXZDLGtEQUFXQSxDQUM1QixPQUFPd0M7UUFDTCxJQUFJLENBQUM5QixTQUFTYyxNQUFNQyxJQUFJO1FBRXhCLElBQUk7WUFDRixNQUFNTyxXQUFXLE1BQU1DLE1BQ3JCLENBQUMsbUJBQW1CLEVBQUVPLGVBQWUsS0FBSyxDQUFDLEVBQzNDO2dCQUNFQyxRQUFRO1lBQ1Y7WUFHRixNQUFNaEMsT0FBTyxNQUFNdUIsU0FBU0UsSUFBSTtZQUVoQyxJQUFJekIsS0FBSzBCLE9BQU8sRUFBRTtnQkFDaEIscUJBQXFCO2dCQUNyQnRCLGlCQUFpQixDQUFDNkIsT0FDaEJBLEtBQUtDLEdBQUcsQ0FBQyxDQUFDQyxlQUNSQSxhQUFhbkIsRUFBRSxLQUFLZSxpQkFDaEI7NEJBQUUsR0FBR0ksWUFBWTs0QkFBRUMsUUFBUTt3QkFBSyxJQUNoQ0Q7Z0JBSVIsc0JBQXNCO2dCQUN0QjdCLGVBQWUsQ0FBQzJCLE9BQVNJLEtBQUtDLEdBQUcsQ0FBQyxHQUFHTCxPQUFPO1lBQzlDLE9BQU87Z0JBQ0x2QixTQUFTVixLQUFLUyxLQUFLLElBQUk7WUFDekI7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZGtCLFFBQVFsQixLQUFLLENBQUMsdUNBQXVDQTtZQUNyREMsU0FBUztRQUNYO0lBQ0YsR0FDQTtRQUFDVCxTQUFTYyxNQUFNQztLQUFHO0lBR3JCLE1BQU11QixnQkFBZ0JoRCxrREFBV0EsQ0FBQztRQUNoQyxJQUFJLENBQUNVLFNBQVNjLE1BQU1DLElBQUk7UUFFeEIsSUFBSTtZQUNGLE1BQU1PLFdBQVcsTUFBTUMsTUFBTSxvQ0FBb0M7Z0JBQy9EUSxRQUFRO1lBQ1Y7WUFFQSxNQUFNaEMsT0FBTyxNQUFNdUIsU0FBU0UsSUFBSTtZQUVoQyxJQUFJekIsS0FBSzBCLE9BQU8sRUFBRTtnQkFDaEIscUJBQXFCO2dCQUNyQnRCLGlCQUFpQixDQUFDNkIsT0FDaEJBLEtBQUtDLEdBQUcsQ0FBQyxDQUFDQyxlQUFrQjs0QkFBRSxHQUFHQSxZQUFZOzRCQUFFQyxRQUFRO3dCQUFLO2dCQUU5RDlCLGVBQWU7WUFDakIsT0FBTztnQkFDTEksU0FBU1YsS0FBS1MsS0FBSyxJQUFJO1lBQ3pCO1FBQ0YsRUFBRSxPQUFPQSxPQUFPO1lBQ2RrQixRQUFRbEIsS0FBSyxDQUFDLDRDQUE0Q0E7WUFDMURDLFNBQVM7UUFDWDtJQUNGLEdBQUc7UUFBQ1QsU0FBU2MsTUFBTUM7S0FBRztJQUV0Qix3Q0FBd0M7SUFDeEMxQixnREFBU0EsQ0FBQztRQUNSLElBQUlZLFdBQVcsbUJBQW1CRCxTQUFTYyxNQUFNQyxJQUFJO1lBQ25ESCxtQkFBbUI7Z0JBQUVRLE9BQU87WUFBRSxJQUFJLDBDQUEwQztZQUM1RVE7UUFDRjtJQUNGLEdBQUc7UUFBQzNCO1FBQVFELFNBQVNjLE1BQU1DO1FBQUlIO1FBQW9CZ0I7S0FBbUI7SUFFdEUsdURBQXVEO0lBQ3ZEdkMsZ0RBQVNBLENBQUM7UUFDUixJQUFJLENBQUNXLFNBQVNjLE1BQU1DLElBQUk7UUFFeEIsTUFBTXdCLFdBQVdDLFlBQVk7WUFDM0JaO1FBQ0YsR0FBRyxRQUFRLGFBQWE7UUFFeEIsT0FBTyxJQUFNYSxjQUFjRjtJQUM3QixHQUFHO1FBQUN2QyxTQUFTYyxNQUFNQztRQUFJYTtLQUFtQjtJQUUxQyxNQUFNYyxRQUFpQztRQUNyQ3hDO1FBQ0FFO1FBQ0FFO1FBQ0FFO1FBQ0FJO1FBQ0FpQjtRQUNBUztRQUNBVjtRQUNBRDtJQUNGO0lBRUEscUJBQ0UsOERBQUNuQyxvQkFBb0JtRCxRQUFRO1FBQUNELE9BQU9BO2tCQUNsQzVDOzs7Ozs7QUFHUCxFQUFFO0FBRUYsaUVBQWVELG9CQUFvQkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2hlcmJhbGljaW91cy1uZXh0anMvLi9hcHAvY29udGV4dC9Ob3RpZmljYXRpb25Db250ZXh0LnRzeD8yMmQwIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgUmVhY3QsIHtcbiAgY3JlYXRlQ29udGV4dCxcbiAgdXNlQ29udGV4dCxcbiAgdXNlU3RhdGUsXG4gIHVzZUVmZmVjdCxcbiAgdXNlQ2FsbGJhY2ssXG59IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgdXNlU2Vzc2lvbiB9IGZyb20gXCJuZXh0LWF1dGgvcmVhY3RcIjtcblxuaW50ZXJmYWNlIE5vdGlmaWNhdGlvbiB7XG4gIGlkOiBzdHJpbmc7XG4gIHRpdGxlOiBzdHJpbmc7XG4gIG1lc3NhZ2U6IHN0cmluZztcbiAgdHlwZTogc3RyaW5nO1xuICBpc1JlYWQ6IGJvb2xlYW47XG4gIGRhdGE6IGFueTtcbiAgY3JlYXRlZEF0OiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBOb3RpZmljYXRpb25Db250ZXh0VHlwZSB7XG4gIG5vdGlmaWNhdGlvbnM6IE5vdGlmaWNhdGlvbltdO1xuICB1bnJlYWRDb3VudDogbnVtYmVyO1xuICBsb2FkaW5nOiBib29sZWFuO1xuICBlcnJvcjogc3RyaW5nIHwgbnVsbDtcbiAgZmV0Y2hOb3RpZmljYXRpb25zOiAob3B0aW9ucz86IHtcbiAgICBwYWdlPzogbnVtYmVyO1xuICAgIGxpbWl0PzogbnVtYmVyO1xuICAgIHVucmVhZE9ubHk/OiBib29sZWFuO1xuICB9KSA9PiBQcm9taXNlPHZvaWQ+O1xuICBtYXJrQXNSZWFkOiAobm90aWZpY2F0aW9uSWQ6IHN0cmluZykgPT4gUHJvbWlzZTx2b2lkPjtcbiAgbWFya0FsbEFzUmVhZDogKCkgPT4gUHJvbWlzZTx2b2lkPjtcbiAgcmVmcmVzaFVucmVhZENvdW50OiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xuICByZWZyZXNoTm90aWZpY2F0aW9uczogKCkgPT4gUHJvbWlzZTx2b2lkPjtcbn1cblxuY29uc3QgTm90aWZpY2F0aW9uQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8Tm90aWZpY2F0aW9uQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KFxuICB1bmRlZmluZWQsXG4pO1xuXG5leHBvcnQgY29uc3QgdXNlTm90aWZpY2F0aW9ucyA9ICgpID0+IHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoTm90aWZpY2F0aW9uQ29udGV4dCk7XG4gIGlmIChjb250ZXh0ID09PSB1bmRlZmluZWQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICBcInVzZU5vdGlmaWNhdGlvbnMgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIE5vdGlmaWNhdGlvblByb3ZpZGVyXCIsXG4gICAgKTtcbiAgfVxuICByZXR1cm4gY29udGV4dDtcbn07XG5cbmludGVyZmFjZSBOb3RpZmljYXRpb25Qcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGNvbnN0IE5vdGlmaWNhdGlvblByb3ZpZGVyOiBSZWFjdC5GQzxOb3RpZmljYXRpb25Qcm92aWRlclByb3BzPiA9ICh7XG4gIGNoaWxkcmVuLFxufSkgPT4ge1xuICBjb25zdCB7IGRhdGE6IHNlc3Npb24sIHN0YXR1cyB9ID0gdXNlU2Vzc2lvbigpO1xuICBjb25zdCBbbm90aWZpY2F0aW9ucywgc2V0Tm90aWZpY2F0aW9uc10gPSB1c2VTdGF0ZTxOb3RpZmljYXRpb25bXT4oW10pO1xuICBjb25zdCBbdW5yZWFkQ291bnQsIHNldFVucmVhZENvdW50XSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtwcmV2VW5yZWFkQ291bnQsIHNldFByZXZVbnJlYWRDb3VudF0gPSB1c2VTdGF0ZSgwKTtcblxuICBjb25zdCBmZXRjaE5vdGlmaWNhdGlvbnMgPSB1c2VDYWxsYmFjayhcbiAgICBhc3luYyAoXG4gICAgICBvcHRpb25zOiB7IHBhZ2U/OiBudW1iZXI7IGxpbWl0PzogbnVtYmVyOyB1bnJlYWRPbmx5PzogYm9vbGVhbiB9ID0ge30sXG4gICAgKSA9PiB7XG4gICAgICBpZiAoIXNlc3Npb24/LnVzZXI/LmlkKSByZXR1cm47XG5cbiAgICAgIHRyeSB7XG4gICAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgICAgIGNvbnN0IHBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoe1xuICAgICAgICAgIHBhZ2U6IChvcHRpb25zLnBhZ2UgfHwgMSkudG9TdHJpbmcoKSxcbiAgICAgICAgICBsaW1pdDogKG9wdGlvbnMubGltaXQgfHwgMTApLnRvU3RyaW5nKCksXG4gICAgICAgICAgLi4uKG9wdGlvbnMudW5yZWFkT25seSAmJiB7IHVucmVhZE9ubHk6IFwidHJ1ZVwiIH0pLFxuICAgICAgICB9KTtcblxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL25vdGlmaWNhdGlvbnM/JHtwYXJhbXN9YCk7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xuICAgICAgICAgIHNldE5vdGlmaWNhdGlvbnMoZGF0YS5kYXRhLm5vdGlmaWNhdGlvbnMpO1xuICAgICAgICAgIHNldFVucmVhZENvdW50KGRhdGEuZGF0YS51bnJlYWRDb3VudCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgc2V0RXJyb3IoZGF0YS5lcnJvciB8fCBcIkZhaWxlZCB0byBmZXRjaCBub3RpZmljYXRpb25zXCIpO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZmV0Y2hpbmcgbm90aWZpY2F0aW9uczpcIiwgZXJyb3IpO1xuICAgICAgICBzZXRFcnJvcihcIkZhaWxlZCB0byBmZXRjaCBub3RpZmljYXRpb25zXCIpO1xuICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgICB9XG4gICAgfSxcbiAgICBbc2Vzc2lvbj8udXNlcj8uaWRdLFxuICApO1xuXG4gIGNvbnN0IHJlZnJlc2hOb3RpZmljYXRpb25zID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIGF3YWl0IGZldGNoTm90aWZpY2F0aW9ucyh7IGxpbWl0OiAxMCB9KTtcbiAgfSwgW2ZldGNoTm90aWZpY2F0aW9uc10pO1xuXG4gIGNvbnN0IHJlZnJlc2hVbnJlYWRDb3VudCA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXNlc3Npb24/LnVzZXI/LmlkKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChcIi9hcGkvbm90aWZpY2F0aW9ucy91bnJlYWQtY291bnRcIik7XG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICBpZiAoZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIGlmIChkYXRhLnVucmVhZENvdW50ID4gcHJldlVucmVhZENvdW50KSB7XG4gICAgICAgICAgZmV0Y2hOb3RpZmljYXRpb25zKHsgbGltaXQ6IDEwIH0pO1xuICAgICAgICB9XG4gICAgICAgIHNldFByZXZVbnJlYWRDb3VudChkYXRhLnVucmVhZENvdW50KTtcbiAgICAgICAgc2V0VW5yZWFkQ291bnQoZGF0YS51bnJlYWRDb3VudCk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBmZXRjaGluZyB1bnJlYWQgY291bnQ6XCIsIGVycm9yKTtcbiAgICB9XG4gIH0sIFtzZXNzaW9uPy51c2VyPy5pZF0pO1xuXG4gIGNvbnN0IG1hcmtBc1JlYWQgPSB1c2VDYWxsYmFjayhcbiAgICBhc3luYyAobm90aWZpY2F0aW9uSWQ6IHN0cmluZykgPT4ge1xuICAgICAgaWYgKCFzZXNzaW9uPy51c2VyPy5pZCkgcmV0dXJuO1xuXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKFxuICAgICAgICAgIGAvYXBpL25vdGlmaWNhdGlvbnMvJHtub3RpZmljYXRpb25JZH0vcmVhZGAsXG4gICAgICAgICAge1xuICAgICAgICAgICAgbWV0aG9kOiBcIlBPU1RcIixcbiAgICAgICAgICB9LFxuICAgICAgICApO1xuXG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xuICAgICAgICAgIC8vIFVwZGF0ZSBsb2NhbCBzdGF0ZVxuICAgICAgICAgIHNldE5vdGlmaWNhdGlvbnMoKHByZXYpID0+XG4gICAgICAgICAgICBwcmV2Lm1hcCgobm90aWZpY2F0aW9uKSA9PlxuICAgICAgICAgICAgICBub3RpZmljYXRpb24uaWQgPT09IG5vdGlmaWNhdGlvbklkXG4gICAgICAgICAgICAgICAgPyB7IC4uLm5vdGlmaWNhdGlvbiwgaXNSZWFkOiB0cnVlIH1cbiAgICAgICAgICAgICAgICA6IG5vdGlmaWNhdGlvbixcbiAgICAgICAgICAgICksXG4gICAgICAgICAgKTtcblxuICAgICAgICAgIC8vIFVwZGF0ZSB1bnJlYWQgY291bnRcbiAgICAgICAgICBzZXRVbnJlYWRDb3VudCgocHJldikgPT4gTWF0aC5tYXgoMCwgcHJldiAtIDEpKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBzZXRFcnJvcihkYXRhLmVycm9yIHx8IFwiRmFpbGVkIHRvIG1hcmsgbm90aWZpY2F0aW9uIGFzIHJlYWRcIik7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBtYXJraW5nIG5vdGlmaWNhdGlvbiBhcyByZWFkOlwiLCBlcnJvcik7XG4gICAgICAgIHNldEVycm9yKFwiRmFpbGVkIHRvIG1hcmsgbm90aWZpY2F0aW9uIGFzIHJlYWRcIik7XG4gICAgICB9XG4gICAgfSxcbiAgICBbc2Vzc2lvbj8udXNlcj8uaWRdLFxuICApO1xuXG4gIGNvbnN0IG1hcmtBbGxBc1JlYWQgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFzZXNzaW9uPy51c2VyPy5pZCkgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goXCIvYXBpL25vdGlmaWNhdGlvbnMvbWFyay1hbGwtcmVhZFwiLCB7XG4gICAgICAgIG1ldGhvZDogXCJQT1NUXCIsXG4gICAgICB9KTtcblxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcblxuICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xuICAgICAgICAvLyBVcGRhdGUgbG9jYWwgc3RhdGVcbiAgICAgICAgc2V0Tm90aWZpY2F0aW9ucygocHJldikgPT5cbiAgICAgICAgICBwcmV2Lm1hcCgobm90aWZpY2F0aW9uKSA9PiAoeyAuLi5ub3RpZmljYXRpb24sIGlzUmVhZDogdHJ1ZSB9KSksXG4gICAgICAgICk7XG4gICAgICAgIHNldFVucmVhZENvdW50KDApO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0RXJyb3IoZGF0YS5lcnJvciB8fCBcIkZhaWxlZCB0byBtYXJrIGFsbCBub3RpZmljYXRpb25zIGFzIHJlYWRcIik7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBtYXJraW5nIGFsbCBub3RpZmljYXRpb25zIGFzIHJlYWQ6XCIsIGVycm9yKTtcbiAgICAgIHNldEVycm9yKFwiRmFpbGVkIHRvIG1hcmsgYWxsIG5vdGlmaWNhdGlvbnMgYXMgcmVhZFwiKTtcbiAgICB9XG4gIH0sIFtzZXNzaW9uPy51c2VyPy5pZF0pO1xuXG4gIC8vIEZldGNoIG5vdGlmaWNhdGlvbnMgd2hlbiB1c2VyIGxvZ3MgaW5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoc3RhdHVzID09PSBcImF1dGhlbnRpY2F0ZWRcIiAmJiBzZXNzaW9uPy51c2VyPy5pZCkge1xuICAgICAgZmV0Y2hOb3RpZmljYXRpb25zKHsgbGltaXQ6IDUgfSk7IC8vIEZldGNoIHJlY2VudCBub3RpZmljYXRpb25zIGZvciBkcm9wZG93blxuICAgICAgcmVmcmVzaFVucmVhZENvdW50KCk7XG4gICAgfVxuICB9LCBbc3RhdHVzLCBzZXNzaW9uPy51c2VyPy5pZCwgZmV0Y2hOb3RpZmljYXRpb25zLCByZWZyZXNoVW5yZWFkQ291bnRdKTtcblxuICAvLyBSZWZyZXNoIHVucmVhZCBjb3VudCBwZXJpb2RpY2FsbHkgKGV2ZXJ5IDMwIHNlY29uZHMpXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFzZXNzaW9uPy51c2VyPy5pZCkgcmV0dXJuO1xuXG4gICAgY29uc3QgaW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XG4gICAgICByZWZyZXNoVW5yZWFkQ291bnQoKTtcbiAgICB9LCAzMDAwMCk7IC8vIDMwIHNlY29uZHNcblxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGludGVydmFsKTtcbiAgfSwgW3Nlc3Npb24/LnVzZXI/LmlkLCByZWZyZXNoVW5yZWFkQ291bnRdKTtcblxuICBjb25zdCB2YWx1ZTogTm90aWZpY2F0aW9uQ29udGV4dFR5cGUgPSB7XG4gICAgbm90aWZpY2F0aW9ucyxcbiAgICB1bnJlYWRDb3VudCxcbiAgICBsb2FkaW5nLFxuICAgIGVycm9yLFxuICAgIGZldGNoTm90aWZpY2F0aW9ucyxcbiAgICBtYXJrQXNSZWFkLFxuICAgIG1hcmtBbGxBc1JlYWQsXG4gICAgcmVmcmVzaFVucmVhZENvdW50LFxuICAgIHJlZnJlc2hOb3RpZmljYXRpb25zLFxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPE5vdGlmaWNhdGlvbkNvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L05vdGlmaWNhdGlvbkNvbnRleHQuUHJvdmlkZXI+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBOb3RpZmljYXRpb25Qcm92aWRlcjtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsInVzZVNlc3Npb24iLCJOb3RpZmljYXRpb25Db250ZXh0IiwidW5kZWZpbmVkIiwidXNlTm90aWZpY2F0aW9ucyIsImNvbnRleHQiLCJFcnJvciIsIk5vdGlmaWNhdGlvblByb3ZpZGVyIiwiY2hpbGRyZW4iLCJkYXRhIiwic2Vzc2lvbiIsInN0YXR1cyIsIm5vdGlmaWNhdGlvbnMiLCJzZXROb3RpZmljYXRpb25zIiwidW5yZWFkQ291bnQiLCJzZXRVbnJlYWRDb3VudCIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsInByZXZVbnJlYWRDb3VudCIsInNldFByZXZVbnJlYWRDb3VudCIsImZldGNoTm90aWZpY2F0aW9ucyIsIm9wdGlvbnMiLCJ1c2VyIiwiaWQiLCJwYXJhbXMiLCJVUkxTZWFyY2hQYXJhbXMiLCJwYWdlIiwidG9TdHJpbmciLCJsaW1pdCIsInVucmVhZE9ubHkiLCJyZXNwb25zZSIsImZldGNoIiwianNvbiIsInN1Y2Nlc3MiLCJjb25zb2xlIiwicmVmcmVzaE5vdGlmaWNhdGlvbnMiLCJyZWZyZXNoVW5yZWFkQ291bnQiLCJtYXJrQXNSZWFkIiwibm90aWZpY2F0aW9uSWQiLCJtZXRob2QiLCJwcmV2IiwibWFwIiwibm90aWZpY2F0aW9uIiwiaXNSZWFkIiwiTWF0aCIsIm1heCIsIm1hcmtBbGxBc1JlYWQiLCJpbnRlcnZhbCIsInNldEludGVydmFsIiwiY2xlYXJJbnRlcnZhbCIsInZhbHVlIiwiUHJvdmlkZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/context/NotificationContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/SessionProvider.tsx":
/*!*****************************************!*\
  !*** ./app/context/SessionProvider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthSessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AuthSessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\context\\\\SessionProvider.tsx\",\n        lineNumber: 11,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29udGV4dC9TZXNzaW9uUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVrRDtBQU9uQyxTQUFTQyxvQkFBb0IsRUFBRUMsUUFBUSxFQUFTO0lBQzdELHFCQUFPLDhEQUFDRiw0REFBZUE7a0JBQUVFOzs7Ozs7QUFDM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9oZXJiYWxpY2lvdXMtbmV4dGpzLy4vYXBwL2NvbnRleHQvU2Vzc2lvblByb3ZpZGVyLnRzeD82MTQxIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyBTZXNzaW9uUHJvdmlkZXIgfSBmcm9tIFwibmV4dC1hdXRoL3JlYWN0XCI7XG5pbXBvcnQgeyBSZWFjdE5vZGUgfSBmcm9tIFwicmVhY3RcIjtcblxuaW50ZXJmYWNlIFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXV0aFNlc3Npb25Qcm92aWRlcih7IGNoaWxkcmVuIH06IFByb3BzKSB7XG4gIHJldHVybiA8U2Vzc2lvblByb3ZpZGVyPntjaGlsZHJlbn08L1Nlc3Npb25Qcm92aWRlcj47XG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiQXV0aFNlc3Npb25Qcm92aWRlciIsImNoaWxkcmVuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/context/SessionProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/ToastContext.tsx":
/*!**************************************!*\
  !*** ./app/context/ToastContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   useToastContext: () => (/* binding */ useToastContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useToast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../hooks/useToast */ \"(ssr)/./app/hooks/useToast.ts\");\n/* harmony import */ var _components_ToastContainer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ToastContainer */ \"(ssr)/./app/components/ToastContainer.tsx\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,useToastContext auto */ \n\n\n\nconst ToastContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst ToastProvider = ({ children })=>{\n    const { toasts, showToast, hideToast } = (0,_hooks_useToast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: {\n            showToast\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ToastContainer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                toasts: toasts,\n                hideToast: hideToast\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\context\\\\ToastContext.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\context\\\\ToastContext.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined);\n};\nconst useToastContext = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ToastContext);\n    if (context === undefined) {\n        throw new Error(\"useToastContext must be used within a ToastProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/context/ToastContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/hooks/useToast.ts":
/*!*******************************!*\
  !*** ./app/hooks/useToast.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useToast auto */ \nconst useToast = ()=>{\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const showToast = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((message, type = \"info\")=>{\n        const id = Date.now();\n        setToasts((prevToasts)=>[\n                ...prevToasts,\n                {\n                    id,\n                    message,\n                    type\n                }\n            ]);\n    }, []);\n    const hideToast = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((id)=>{\n        setToasts((prevToasts)=>prevToasts.filter((toast)=>toast.id !== id));\n    }, []);\n    return {\n        toasts,\n        showToast,\n        hideToast\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvaG9va3MvdXNlVG9hc3QudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OzhEQUU4QztBQVV2QyxNQUFNRSxXQUFXO0lBQ3RCLE1BQU0sQ0FBQ0MsUUFBUUMsVUFBVSxHQUFHSiwrQ0FBUUEsQ0FBaUIsRUFBRTtJQUV2RCxNQUFNSyxZQUFZSixrREFBV0EsQ0FBQyxDQUFDSyxTQUFpQkMsT0FBa0IsTUFBTTtRQUN0RSxNQUFNQyxLQUFLQyxLQUFLQyxHQUFHO1FBQ25CTixVQUFVLENBQUNPLGFBQWU7bUJBQUlBO2dCQUFZO29CQUFFSDtvQkFBSUY7b0JBQVNDO2dCQUFLO2FBQUU7SUFDbEUsR0FBRyxFQUFFO0lBRUwsTUFBTUssWUFBWVgsa0RBQVdBLENBQUMsQ0FBQ087UUFDN0JKLFVBQVUsQ0FBQ08sYUFBZUEsV0FBV0UsTUFBTSxDQUFDLENBQUNDLFFBQVVBLE1BQU1OLEVBQUUsS0FBS0E7SUFDdEUsR0FBRyxFQUFFO0lBRUwsT0FBTztRQUFFTDtRQUFRRTtRQUFXTztJQUFVO0FBQ3hDLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9oZXJiYWxpY2lvdXMtbmV4dGpzLy4vYXBwL2hvb2tzL3VzZVRvYXN0LnRzPzUwMzMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VDYWxsYmFjayB9IGZyb20gXCJyZWFjdFwiO1xuXG50eXBlIFRvYXN0VHlwZSA9IFwic3VjY2Vzc1wiIHwgXCJlcnJvclwiIHwgXCJpbmZvXCI7XG5cbmludGVyZmFjZSBUb2FzdE1lc3NhZ2Uge1xuICBpZDogbnVtYmVyO1xuICBtZXNzYWdlOiBzdHJpbmc7XG4gIHR5cGU6IFRvYXN0VHlwZTtcbn1cblxuZXhwb3J0IGNvbnN0IHVzZVRvYXN0ID0gKCkgPT4ge1xuICBjb25zdCBbdG9hc3RzLCBzZXRUb2FzdHNdID0gdXNlU3RhdGU8VG9hc3RNZXNzYWdlW10+KFtdKTtcblxuICBjb25zdCBzaG93VG9hc3QgPSB1c2VDYWxsYmFjaygobWVzc2FnZTogc3RyaW5nLCB0eXBlOiBUb2FzdFR5cGUgPSBcImluZm9cIikgPT4ge1xuICAgIGNvbnN0IGlkID0gRGF0ZS5ub3coKTtcbiAgICBzZXRUb2FzdHMoKHByZXZUb2FzdHMpID0+IFsuLi5wcmV2VG9hc3RzLCB7IGlkLCBtZXNzYWdlLCB0eXBlIH1dKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGhpZGVUb2FzdCA9IHVzZUNhbGxiYWNrKChpZDogbnVtYmVyKSA9PiB7XG4gICAgc2V0VG9hc3RzKChwcmV2VG9hc3RzKSA9PiBwcmV2VG9hc3RzLmZpbHRlcigodG9hc3QpID0+IHRvYXN0LmlkICE9PSBpZCkpO1xuICB9LCBbXSk7XG5cbiAgcmV0dXJuIHsgdG9hc3RzLCBzaG93VG9hc3QsIGhpZGVUb2FzdCB9O1xufTtcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUNhbGxiYWNrIiwidXNlVG9hc3QiLCJ0b2FzdHMiLCJzZXRUb2FzdHMiLCJzaG93VG9hc3QiLCJtZXNzYWdlIiwidHlwZSIsImlkIiwiRGF0ZSIsIm5vdyIsInByZXZUb2FzdHMiLCJoaWRlVG9hc3QiLCJmaWx0ZXIiLCJ0b2FzdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/hooks/useToast.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6493b11fe5cc\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9oZXJiYWxpY2lvdXMtbmV4dGpzLy4vYXBwL2dsb2JhbHMuY3NzPzIzMDQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2NDkzYjExZmU1Y2NcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/components/Layout.tsx":
/*!***********************************!*\
  !*** ./app/components/Layout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\Layout.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/components/pages/Profile.tsx":
/*!******************************************!*\
  !*** ./app/components/pages/Profile.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\components\pages\Profile.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/context/CartContext.tsx":
/*!*************************************!*\
  !*** ./app/context/CartContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CartProvider: () => (/* binding */ e0),
/* harmony export */   useCart: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\CartContext.tsx#CartProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\CartContext.tsx#useCart`);


/***/ }),

/***/ "(rsc)/./app/context/FlashSaleContext.tsx":
/*!******************************************!*\
  !*** ./app/context/FlashSaleContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FlashSaleProvider: () => (/* binding */ e0),
/* harmony export */   useFlashSale: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\FlashSaleContext.tsx#FlashSaleProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\FlashSaleContext.tsx#useFlashSale`);


/***/ }),

/***/ "(rsc)/./app/context/NotificationContext.tsx":
/*!*********************************************!*\
  !*** ./app/context/NotificationContext.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationProvider: () => (/* binding */ e1),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useNotifications: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#useNotifications`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#NotificationProvider`);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/context/SessionProvider.tsx":
/*!*****************************************!*\
  !*** ./app/context/SessionProvider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\SessionProvider.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/context/ToastContext.tsx":
/*!**************************************!*\
  !*** ./app/context/ToastContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ e0),
/* harmony export */   useToastContext: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\ToastContext.tsx#ToastProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\ToastContext.tsx#useToastContext`);


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _context_CartContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context/CartContext */ \"(rsc)/./app/context/CartContext.tsx\");\n/* harmony import */ var _context_SessionProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./context/SessionProvider */ \"(rsc)/./app/context/SessionProvider.tsx\");\n/* harmony import */ var _context_NotificationContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./context/NotificationContext */ \"(rsc)/./app/context/NotificationContext.tsx\");\n/* harmony import */ var _context_FlashSaleContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./context/FlashSaleContext */ \"(rsc)/./app/context/FlashSaleContext.tsx\");\n/* harmony import */ var _context_ToastContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./context/ToastContext */ \"(rsc)/./app/context/ToastContext.tsx\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Herbalicious - Natural Skincare\",\n    description: \"Natural skincare products for radiant, healthy skin. Discover our botanical collection crafted with nature's finest ingredients.\"\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1.0,\n    themeColor: \"#16a34a\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_SessionProvider__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_NotificationContext__WEBPACK_IMPORTED_MODULE_4__.NotificationProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_FlashSaleContext__WEBPACK_IMPORTED_MODULE_5__.FlashSaleProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_CartContext__WEBPACK_IMPORTED_MODULE_2__.CartProvider, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_ToastContext__WEBPACK_IMPORTED_MODULE_6__.ToastProvider, {\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/lib/auth.ts":
/*!*************************!*\
  !*** ./app/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _mongoose__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./mongoose */ \"(rsc)/./app/lib/mongoose.ts\");\n/* harmony import */ var _models__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./models */ \"(rsc)/./app/lib/models.ts\");\n\n\n\n\n\nconst authOptions = {\n    // Remove PrismaAdapter when using JWT strategy\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                await (0,_mongoose__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n                const user = await _models__WEBPACK_IMPORTED_MODULE_4__.User.findOne({\n                    email: credentials.email\n                });\n                if (!user || !user.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2__.compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                return {\n                    id: user._id.toString(),\n                    email: user.email,\n                    name: user.name,\n                    role: user.role\n                };\n            }\n        })\n    ],\n    // Use JWT sessions, ensure cookie config aligns with NextAuth defaults\n    session: {\n        strategy: \"jwt\",\n        maxAge: 7 * 24 * 60 * 60,\n        updateAge: 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 7 * 24 * 60 * 60\n    },\n    cookies: {\n        sessionToken: {\n            name:  false ? 0 : `next-auth.session-token`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: \"development\" === \"production\",\n                domain:  false ? 0 : undefined\n            }\n        }\n    },\n    callbacks: {\n        async jwt ({ token, user, account, profile }) {\n            console.log(\"JWT callback called with:\", {\n                hasToken: !!token,\n                hasUser: !!user,\n                hasAccount: !!account,\n                accountProvider: account?.provider,\n                userId: user?.id,\n                userEmail: user?.email\n            });\n            // Handle initial sign in\n            if (account && user) {\n                // For OAuth providers, create/update user in database\n                if (account.provider === \"google\") {\n                    try {\n                        const email = user.email || profile?.email;\n                        if (!email) {\n                            throw new Error(\"No email found for Google account\");\n                        }\n                        await (0,_mongoose__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n                        // Check if user exists\n                        let dbUser = await _models__WEBPACK_IMPORTED_MODULE_4__.User.findOne({\n                            email\n                        });\n                        // Create user if doesn't exist\n                        if (!dbUser) {\n                            dbUser = await _models__WEBPACK_IMPORTED_MODULE_4__.User.create({\n                                email,\n                                name: user.name || profile?.name || email.split(\"@\")[0],\n                                // Don't save Google profile picture\n                                role: \"CUSTOMER\",\n                                emailVerified: new Date()\n                            });\n                        }\n                        if (dbUser) {\n                            // Set token properties\n                            token.sub = dbUser._id.toString();\n                            token.role = dbUser.role;\n                            token.email = dbUser.email;\n                            token.name = dbUser.name;\n                        }\n                    } catch (error) {\n                        console.error(\"Error handling Google sign in:\", error);\n                        throw error; // This will prevent sign in\n                    }\n                } else if (user) {\n                    // For credentials provider\n                    console.log(\"Processing credentials user:\", user);\n                    token.sub = user.id;\n                    token.role = user.role;\n                    token.email = user.email;\n                    token.name = user.name;\n                }\n            }\n            console.log(\"JWT token being returned:\", {\n                sub: token.sub,\n                email: token.email,\n                role: token.role,\n                name: token.name\n            });\n            return token;\n        },\n        async session ({ session, token }) {\n            // Populate session with user data from token\n            console.log(\"Session callback called with:\", {\n                hasSession: !!session,\n                hasToken: !!token,\n                tokenSub: token?.sub,\n                tokenRole: token?.role,\n                tokenEmail: token?.email,\n                tokenName: token?.name\n            });\n            if (token) {\n                session.user = {\n                    id: token.sub ?? \"\",\n                    role: token.role ?? \"CUSTOMER\",\n                    email: token.email ?? \"\",\n                    name: token.name ?? null,\n                    image: null\n                };\n                console.log(\"Session populated with:\", session.user);\n            }\n            console.log(\"Session being returned:\", session);\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Prefer explicit callbackUrl if provided, else dashboard/home depending on role via query\n            try {\n                const u = new URL(url, baseUrl);\n                // If NextAuth default callback cookie/param is present, respect relative paths\n                if (u.origin === \"null\") return baseUrl;\n                if (u.pathname.startsWith(\"/\")) return `${baseUrl}${u.pathname}${u.search}${u.hash}`;\n                if (u.origin === baseUrl) return u.toString();\n            } catch  {\n            // ignore parsing issues and fallback\n            }\n            return baseUrl;\n        }\n    },\n    events: {\n        async signIn ({ user, account, profile, isNewUser }) {\n            // Log sign in events in development only\n            if (true) {\n                console.log(\"Sign in event:\", {\n                    userId: user.id,\n                    email: user.email,\n                    provider: account?.provider,\n                    isNewUser\n                });\n            }\n        }\n    },\n    pages: {\n        signIn: \"/login\",\n        signOut: \"/\",\n        error: \"/login\"\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    // Ensure correct base URL to generate/validate callback & cookie domains\n    // NEXTAUTH_URL must match the browser origin, e.g. http://localhost:3000\n    // Enable debug in development to see what's happening\n    debug: \"development\" === \"development\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/models.ts":
/*!***************************!*\
  !*** ./app/lib/models.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Address: () => (/* binding */ Address),\n/* harmony export */   Category: () => (/* binding */ Category),\n/* harmony export */   Coupon: () => (/* binding */ Coupon),\n/* harmony export */   Enquiry: () => (/* binding */ Enquiry),\n/* harmony export */   HomepageSetting: () => (/* binding */ HomepageSetting),\n/* harmony export */   Newsletter: () => (/* binding */ Newsletter),\n/* harmony export */   Notification: () => (/* binding */ Notification),\n/* harmony export */   NotificationTemplate: () => (/* binding */ NotificationTemplate),\n/* harmony export */   Order: () => (/* binding */ Order),\n/* harmony export */   OrderItem: () => (/* binding */ OrderItem),\n/* harmony export */   Product: () => (/* binding */ Product),\n/* harmony export */   ProductCategory: () => (/* binding */ ProductCategory),\n/* harmony export */   ProductImage: () => (/* binding */ ProductImage),\n/* harmony export */   ProductVariant: () => (/* binding */ ProductVariant),\n/* harmony export */   Review: () => (/* binding */ Review),\n/* harmony export */   Testimonial: () => (/* binding */ Testimonial),\n/* harmony export */   User: () => (/* binding */ User),\n/* harmony export */   Wishlist: () => (/* binding */ Wishlist),\n/* harmony export */   WishlistItem: () => (/* binding */ WishlistItem)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst UserSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    email: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    name: String,\n    phone: String,\n    avatar: String,\n    password: String,\n    role: {\n        type: String,\n        enum: [\n            \"ADMIN\",\n            \"CUSTOMER\"\n        ],\n        default: \"CUSTOMER\"\n    },\n    emailVerified: Date,\n    resetToken: String,\n    resetTokenExpiry: Date\n}, {\n    timestamps: true,\n    collection: \"user\"\n});\nconst ProductSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: true\n    },\n    slug: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    description: String,\n    shortDescription: String,\n    comparePrice: Number,\n    costPrice: Number,\n    weight: Number,\n    dimensions: String,\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    isFeatured: {\n        type: Boolean,\n        default: false\n    },\n    metaTitle: String,\n    metaDescription: String,\n    categoryId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Category\"\n    },\n    price: Number\n}, {\n    timestamps: true,\n    collection: \"product\"\n});\nconst CategorySchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    slug: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    description: String,\n    parentId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Category\"\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    }\n}, {\n    timestamps: true,\n    collection: \"category\"\n});\nconst OrderSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    orderNumber: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    status: {\n        type: String,\n        enum: [\n            \"PENDING\",\n            \"CONFIRMED\",\n            \"PROCESSING\",\n            \"SHIPPED\",\n            \"DELIVERED\",\n            \"CANCELLED\",\n            \"REFUNDED\"\n        ],\n        default: \"PENDING\"\n    },\n    paymentStatus: {\n        type: String,\n        enum: [\n            \"PENDING\",\n            \"PAID\",\n            \"FAILED\",\n            \"REFUNDED\"\n        ],\n        default: \"PENDING\"\n    },\n    paymentMethod: String,\n    paymentId: String,\n    subtotal: {\n        type: Number,\n        required: true\n    },\n    tax: {\n        type: Number,\n        default: 0\n    },\n    shipping: {\n        type: Number,\n        default: 0\n    },\n    discount: {\n        type: Number,\n        default: 0\n    },\n    total: {\n        type: Number,\n        required: true\n    },\n    currency: {\n        type: String,\n        default: \"INR\"\n    },\n    notes: String,\n    userId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    couponCodes: [\n        String\n    ],\n    couponDiscount: {\n        type: Number,\n        default: 0\n    },\n    flashSaleDiscount: {\n        type: Number,\n        default: 0\n    },\n    estimatedDelivery: String,\n    trackingNumber: String,\n    addressId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Address\"\n    }\n}, {\n    timestamps: true,\n    collection: \"order\"\n});\nconst HomepageSettingSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    productOfTheMonthId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\"\n    },\n    bannerText: String,\n    bannerCtaText: String,\n    bannerCtaLink: String,\n    isActive: {\n        type: Boolean,\n        default: true\n    }\n}, {\n    timestamps: true,\n    collection: \"homepageSetting\"\n});\nconst TestimonialSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: true\n    },\n    content: {\n        type: String,\n        required: true\n    },\n    rating: {\n        type: Number,\n        default: 5,\n        min: 1,\n        max: 5\n    },\n    image: String,\n    position: String,\n    company: String,\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    order: {\n        type: Number,\n        default: 0\n    }\n}, {\n    timestamps: true,\n    collection: \"testimonials\"\n});\nconst ProductImageSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    productId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\",\n        required: true\n    },\n    url: {\n        type: String,\n        required: true\n    },\n    alt: String,\n    position: {\n        type: Number,\n        default: 0\n    }\n}, {\n    timestamps: true,\n    collection: \"images\"\n});\nconst ProductVariantSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    productId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\",\n        required: true\n    },\n    name: {\n        type: String,\n        required: true\n    },\n    value: {\n        type: String,\n        required: true\n    },\n    price: Number,\n    pricingMode: {\n        type: String,\n        enum: [\n            \"REPLACE\",\n            \"INCREMENT\",\n            \"FIXED\"\n        ],\n        default: \"REPLACE\"\n    }\n}, {\n    timestamps: true,\n    collection: \"variants\"\n});\nconst ReviewSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    productId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\",\n        required: true\n    },\n    userId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    rating: {\n        type: Number,\n        required: true,\n        min: 1,\n        max: 5\n    },\n    title: String,\n    comment: String,\n    isApproved: {\n        type: Boolean,\n        default: false\n    }\n}, {\n    timestamps: true,\n    collection: \"reviews\"\n});\nconst AddressSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    userId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    firstName: {\n        type: String,\n        required: true\n    },\n    lastName: {\n        type: String,\n        required: true\n    },\n    address1: {\n        type: String,\n        required: true\n    },\n    address2: String,\n    city: {\n        type: String,\n        required: true\n    },\n    state: {\n        type: String,\n        required: true\n    },\n    postalCode: {\n        type: String,\n        required: true\n    },\n    country: {\n        type: String,\n        required: true\n    },\n    phone: {\n        type: String,\n        required: true\n    },\n    isDefault: {\n        type: Boolean,\n        default: false\n    }\n}, {\n    timestamps: true,\n    collection: \"address\"\n});\nconst OrderItemSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    orderId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Order\",\n        required: true\n    },\n    productId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\",\n        required: true\n    },\n    quantity: {\n        type: Number,\n        required: true\n    },\n    price: {\n        type: Number,\n        required: true\n    },\n    total: {\n        type: Number,\n        required: true\n    }\n}, {\n    timestamps: true,\n    collection: \"items\"\n});\nconst NotificationSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    userId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    title: {\n        type: String,\n        required: true\n    },\n    message: {\n        type: String,\n        required: true\n    },\n    type: {\n        type: String,\n        enum: [\n            \"ORDER\",\n            \"PRODUCT\",\n            \"SYSTEM\",\n            \"MARKETING\"\n        ],\n        required: true\n    },\n    isRead: {\n        type: Boolean,\n        default: false\n    },\n    data: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.Mixed\n}, {\n    timestamps: true,\n    collection: \"notifications\"\n});\nconst CouponSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    code: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    name: {\n        type: String,\n        required: true\n    },\n    description: String,\n    type: {\n        type: String,\n        enum: [\n            \"PERCENTAGE\",\n            \"FIXED\"\n        ],\n        required: true\n    },\n    value: {\n        type: Number,\n        required: true\n    },\n    minimumAmount: Number,\n    maximumDiscount: Number,\n    usageLimit: Number,\n    usedCount: {\n        type: Number,\n        default: 0\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    validFrom: {\n        type: Date,\n        required: true\n    },\n    validUntil: Date,\n    userUsageLimit: Number,\n    isStackable: Boolean,\n    showInModule: Boolean,\n    applicableProducts: [\n        {\n            type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n            ref: \"Product\"\n        }\n    ],\n    applicableCategories: [\n        {\n            type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n            ref: \"Category\"\n        }\n    ],\n    excludedProducts: [\n        {\n            type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n            ref: \"Product\"\n        }\n    ],\n    excludedCategories: [\n        {\n            type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n            ref: \"Category\"\n        }\n    ],\n    customerSegments: [\n        String\n    ]\n}, {\n    timestamps: true,\n    collection: \"coupons\"\n});\nconst WishlistSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    userId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    productId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\",\n        required: true\n    }\n}, {\n    timestamps: true,\n    collection: \"wishlist\"\n});\nconst NewsletterSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    email: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    }\n}, {\n    timestamps: true,\n    collection: \"newsletter\"\n});\nconst ProductCategorySchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    productId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\",\n        required: true\n    },\n    categoryId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Category\",\n        required: true\n    }\n}, {\n    timestamps: true,\n    collection: \"productCategories\"\n});\nconst WishlistItemSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    userId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    productId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\",\n        required: true\n    }\n}, {\n    timestamps: true,\n    collection: \"wishlistItems\"\n});\n// Create and export models\nconst User = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).User || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"User\", UserSchema);\nconst Product = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Product || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Product\", ProductSchema);\nconst Category = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Category || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Category\", CategorySchema);\nconst Order = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Order || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Order\", OrderSchema);\nconst HomepageSetting = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).HomepageSetting || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"HomepageSetting\", HomepageSettingSchema);\nconst Testimonial = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Testimonial || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Testimonial\", TestimonialSchema);\nconst ProductImage = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).ProductImage || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"ProductImage\", ProductImageSchema);\nconst ProductVariant = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).ProductVariant || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"ProductVariant\", ProductVariantSchema);\nconst Review = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Review || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Review\", ReviewSchema);\nconst Address = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Address || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Address\", AddressSchema);\nconst OrderItem = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).OrderItem || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"OrderItem\", OrderItemSchema);\nconst Notification = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Notification || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Notification\", NotificationSchema);\nconst Coupon = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Coupon || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Coupon\", CouponSchema);\nconst Wishlist = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Wishlist || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Wishlist\", WishlistSchema);\nconst Newsletter = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Newsletter || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Newsletter\", NewsletterSchema);\nconst ProductCategory = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).ProductCategory || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"ProductCategory\", ProductCategorySchema);\nconst WishlistItem = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).WishlistItem || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"WishlistItem\", WishlistItemSchema);\nconst NotificationTemplateSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    type: {\n        type: String,\n        required: true\n    },\n    title: {\n        type: String,\n        required: true\n    },\n    message: {\n        type: String,\n        required: true\n    },\n    emailSubject: String,\n    emailTemplate: String,\n    isActive: {\n        type: Boolean,\n        default: true\n    }\n}, {\n    timestamps: true,\n    collection: \"notification_templates\"\n});\nconst NotificationTemplate = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).NotificationTemplate || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"NotificationTemplate\", NotificationTemplateSchema);\nconst EnquirySchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: true\n    },\n    email: {\n        type: String,\n        required: true\n    },\n    subject: {\n        type: String,\n        required: true\n    },\n    message: {\n        type: String,\n        required: true\n    },\n    status: {\n        type: String,\n        default: \"NEW\"\n    },\n    notes: String\n}, {\n    timestamps: true,\n    collection: \"enquiry\"\n});\nconst Enquiry = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Enquiry || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Enquiry\", EnquirySchema);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/models.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/mongoose.ts":
/*!*****************************!*\
  !*** ./app/lib/mongoose.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error(\"Please define the MONGODB_URI environment variable inside .env\");\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/mongoose.ts\n");

/***/ }),

/***/ "(rsc)/./app/profile/layout.tsx":
/*!********************************!*\
  !*** ./app/profile/layout.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProfileLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/auth */ \"(rsc)/./app/lib/auth.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n\n\n\n\nasync function ProfileLayout({ children }) {\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_app_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n    // If no session, redirect to login\n    if (!session) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/login\");\n    }\n    // If user is admin, redirect to admin panel\n    if (session.user?.role === \"ADMIN\") {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/admin\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcHJvZmlsZS9sYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTZDO0FBQ0E7QUFDRjtBQUU1QixlQUFlRyxjQUFjLEVBQzFDQyxRQUFRLEVBR1Q7SUFDQyxNQUFNQyxVQUFVLE1BQU1MLDJEQUFnQkEsQ0FBQ0Msc0RBQVdBO0lBRWxELG1DQUFtQztJQUNuQyxJQUFJLENBQUNJLFNBQVM7UUFDWkgseURBQVFBLENBQUM7SUFDWDtJQUVBLDRDQUE0QztJQUM1QyxJQUFJRyxRQUFRQyxJQUFJLEVBQUVDLFNBQVMsU0FBUztRQUNsQ0wseURBQVFBLENBQUM7SUFDWDtJQUVBLHFCQUFPO2tCQUFHRTs7QUFDWiIsInNvdXJjZXMiOlsid2VicGFjazovL2hlcmJhbGljaW91cy1uZXh0anMvLi9hcHAvcHJvZmlsZS9sYXlvdXQudHN4P2YyMmYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0U2VydmVyU2Vzc2lvbiB9IGZyb20gXCJuZXh0LWF1dGhcIjtcclxuaW1wb3J0IHsgYXV0aE9wdGlvbnMgfSBmcm9tIFwiQC9hcHAvbGliL2F1dGhcIjtcclxuaW1wb3J0IHsgcmVkaXJlY3QgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCI7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBhc3luYyBmdW5jdGlvbiBQcm9maWxlTGF5b3V0KHtcclxuICBjaGlsZHJlbixcclxufToge1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XHJcbn0pIHtcclxuICBjb25zdCBzZXNzaW9uID0gYXdhaXQgZ2V0U2VydmVyU2Vzc2lvbihhdXRoT3B0aW9ucyk7XHJcblxyXG4gIC8vIElmIG5vIHNlc3Npb24sIHJlZGlyZWN0IHRvIGxvZ2luXHJcbiAgaWYgKCFzZXNzaW9uKSB7XHJcbiAgICByZWRpcmVjdChcIi9sb2dpblwiKTtcclxuICB9XHJcblxyXG4gIC8vIElmIHVzZXIgaXMgYWRtaW4sIHJlZGlyZWN0IHRvIGFkbWluIHBhbmVsXHJcbiAgaWYgKHNlc3Npb24udXNlcj8ucm9sZSA9PT0gXCJBRE1JTlwiKSB7XHJcbiAgICByZWRpcmVjdChcIi9hZG1pblwiKTtcclxuICB9XHJcblxyXG4gIHJldHVybiA8PntjaGlsZHJlbn08Lz47XHJcbn1cclxuIl0sIm5hbWVzIjpbImdldFNlcnZlclNlc3Npb24iLCJhdXRoT3B0aW9ucyIsInJlZGlyZWN0IiwiUHJvZmlsZUxheW91dCIsImNoaWxkcmVuIiwic2Vzc2lvbiIsInVzZXIiLCJyb2xlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/profile/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/profile/page.tsx":
/*!******************************!*\
  !*** ./app/profile/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProfilePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/Layout */ \"(rsc)/./app/components/Layout.tsx\");\n/* harmony import */ var _components_pages_Profile__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/pages/Profile */ \"(rsc)/./app/components/pages/Profile.tsx\");\n\n\n\nfunction ProfilePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pages_Profile__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\profile\\\\page.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\profile\\\\page.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcHJvZmlsZS9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEM7QUFDUTtBQUVuQyxTQUFTRTtJQUN0QixxQkFDRSw4REFBQ0YsMERBQU1BO2tCQUNMLDRFQUFDQyxpRUFBT0E7Ozs7Ozs7Ozs7QUFHZCIsInNvdXJjZXMiOlsid2VicGFjazovL2hlcmJhbGljaW91cy1uZXh0anMvLi9hcHAvcHJvZmlsZS9wYWdlLnRzeD8xZjgyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBMYXlvdXQgZnJvbSBcIi4uL2NvbXBvbmVudHMvTGF5b3V0XCI7XG5pbXBvcnQgUHJvZmlsZSBmcm9tIFwiLi4vY29tcG9uZW50cy9wYWdlcy9Qcm9maWxlXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFByb2ZpbGVQYWdlKCkge1xuICByZXR1cm4gKFxuICAgIDxMYXlvdXQ+XG4gICAgICA8UHJvZmlsZSAvPlxuICAgIDwvTGF5b3V0PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkxheW91dCIsIlByb2ZpbGUiLCJQcm9maWxlUGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/profile/page.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/lucide-react","vendor-chunks/preact","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();