"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import Image from "next/image";
import {
  ArrowLeft,
  Star,
  ShoppingCart,
  Heart,
  Share2,
  Check,
  Loader2,
} from "lucide-react";
import { useCart } from "../../context/CartContext";
import ProductFAQs from "../ProductFAQs";
import ProductVariationSelector from "../ProductVariationSelector";
import ProductImageGallery from "../ProductImageGallery";
import ProductCategories from "../ProductCategories";
import ProductReviews from "../ProductReviews";
import { ProductDetailSkeleton } from "../loaders/SkeletonLoaders";
import { useFlashSale } from "../../context/FlashSaleContext";
import { getFlashSalePrice } from "../../lib/flash-sale";
import { useToastContext } from "../../context/ToastContext";
import { useNotifications } from "../../context/NotificationContext";

// Database product interface
interface DBProduct {
  id: string;
  name: string;
  slug: string;
  description: string;
  shortDescription: string;
  price: number;
  comparePrice?: number;
  isFeatured: boolean;
  isActive: boolean;
  category: {
    id: string;
    name: string;
    slug: string;
  };
  productCategories?: Array<{
    category: {
      id: string;
      name: string;
      slug: string;
    };
  }>;
  images: Array<{
    id: string;
    url: string;
    alt: string;
    position?: number;
  }>;
  variants?: Array<{
    id: string;
    name: string;
    value: string;
    price?: number;
  }>;
  reviews?: Array<{
    id: string;
    rating: number;
    title?: string;
    content?: string;
    isVerified: boolean;
    createdAt: string;
    user: {
      id: string;
      name?: string;
      avatar?: string;
    };
  }>;
}

// Frontend product interface (compatible with existing components)
interface ProductVariant {
  id: string;
  name: string;
  value: string;
  price?: number;
}

interface Product {
  id: string;
  name: string;
  description: string;
  shortDescription: string;
  price: number;
  image?: string;
  images?: Array<{
    id: string;
    url: string;
    alt: string;
    position: number;
  }>;
  category: string;
  featured: boolean;
  benefits: string[];
  rating: number;
  reviews: number;
  variants?: ProductVariant[];
  _raw?: DBProduct; // Raw database product data for components that need it
}

interface ProductDetailProps {
  id: string;
}

// Convert database product to frontend format
const convertDBProductToFrontend = (
  dbProduct: DBProduct & { reviews?: any[] },
): Product => {
  // Calculate actual rating from reviews
  const actualReviews = dbProduct.reviews || [];
  const actualRating =
    actualReviews.length > 0
      ? actualReviews.reduce((sum, review) => sum + review.rating, 0) /
        actualReviews.length
      : 0;

  // Parse benefits from description if available
  const benefits: string[] = [];

  // Try to extract benefits from description if it contains structured data
  if (dbProduct.description) {
    // Look for benefits section in description
    const benefitsMatch = dbProduct.description.match(/benefits?:\s*([^.]+)/i);
    if (benefitsMatch) {
      benefits.push(
        ...benefitsMatch[1]
          .split(",")
          .map((b) => b.trim())
          .filter((b) => b),
      );
    }
  }

  // If no benefits found in description, leave empty array
  // The UI will handle empty benefits gracefully

  return {
    id: dbProduct.id,
    name: dbProduct.name,
    description: dbProduct.description || "",
    shortDescription: dbProduct.shortDescription || "",
    price: dbProduct.price || 0,
    image: dbProduct.images[0]?.url || "/images/default-product.jpg",
    images: dbProduct.images.map((img) => ({
      id: img.id,
      url: img.url,
      alt: img.alt || dbProduct.name,
      position: img.position || 0,
    })),
    category: dbProduct.category?.slug || "skincare",
    featured: dbProduct.isFeatured,
    benefits,
    rating: Math.round(actualRating * 10) / 10, // Round to 1 decimal place
    reviews: actualReviews.length,
    // Include raw database product data for ProductCategories component
    _raw: dbProduct,
  };
};

const ProductDetail: React.FC<ProductDetailProps> = ({ id }) => {
  const router = useRouter();
  const { data: session } = useSession();
  const { dispatch } = useCart();
  const { flashSaleSettings } = useFlashSale();
  const { showToast } = useToastContext();
  const { refreshUnreadCount, refreshNotifications } = useNotifications();
  const [activeTab, setActiveTab] = useState<
    "description" | "reviews" | "faqs"
  >("description");
  const [isAddedToCart, setIsAddedToCart] = useState(false);
  const [selectedVariation, setSelectedVariation] = useState<any>(null);
  const [quantity, setQuantity] = useState(1);
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPrice, setCurrentPrice] = useState<number>(0);
  const [isInWishlist, setIsInWishlist] = useState(false);
  const [wishlistLoading, setWishlistLoading] = useState(false);
  const [hasVariants, setHasVariants] = useState(false);

  // Fetch product data
  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/products/${id}`);
        const data = await response.json();

        if (data.success) {
          const convertedProduct = convertDBProductToFrontend(data.data);
          setProduct(convertedProduct);

          // Check if product has variants
          const variants = data.data.variants || [];
          setHasVariants(variants.length > 0);

          // Don't set initial price here - let ProductVariationSelector handle it
          // For products without variants, use the base price
          if (variants.length === 0) {
            setCurrentPrice(convertedProduct.price);
          }
          // For products with variants, the price will be set by ProductVariationSelector
        } else {
          setError("Product not found");
        }
      } catch (error) {
        console.error("Error fetching product:", error);
        setError("Failed to load product");
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchProduct();
    }
  }, [id]);

  // Check if product is in wishlist
  useEffect(() => {
    const checkWishlistStatus = async () => {
      if (!session?.user?.id || !product?.id) return;

      try {
        const response = await fetch("/api/wishlist");
        if (response.ok) {
          const data = await response.json();
          const isInList = data.items.some(
            (item: any) => item.id === product.id,
          );
          setIsInWishlist(isInList);
        }
      } catch (error) {
        console.error("Error checking wishlist status:", error);
      }
    };

    checkWishlistStatus();
  }, [session, product]);

  const handleVariationChange = (variation: any, totalPrice: number) => {
    setSelectedVariation(variation);
    setCurrentPrice(totalPrice);
  };

  const handleAddToCart = () => {
    if (!product) return;

    // Get the sale price if flash sale is active
    const saleInfo = getFlashSalePrice(currentPrice, flashSaleSettings);

    const productToAdd: Product = {
      ...product,
      price: saleInfo.salePrice,
    };

    // Convert selectedVariation to the format expected by CartContext
    const selectedVariants = selectedVariation
      ? [
          {
            id: selectedVariation.id,
            name: selectedVariation.name,
            value: selectedVariation.value,
            price: selectedVariation.price,
          },
        ]
      : undefined;

    dispatch({
      type: "ADD_ITEM",
      payload: productToAdd,
      selectedVariants,
    });
    setIsAddedToCart(true);
    setTimeout(() => setIsAddedToCart(false), 2000);
  };

  const handleWishlistToggle = async () => {
    if (!session?.user?.id) {
      router.push("/login");
      return;
    }

    if (!product?.id) return;

    setWishlistLoading(true);
    try {
      if (isInWishlist) {
        // Remove from wishlist
        const response = await fetch(`/api/wishlist?productId=${product.id}`, {
          method: "DELETE",
        });

        if (response.ok) {
          setIsInWishlist(false);
          showToast("Removed from wishlist", "success");
          refreshUnreadCount();
          refreshNotifications();
        } else {
          const data = await response.json();
          showToast(data.error || "Failed to remove from wishlist", "error");
        }
      } else {
        // Add to wishlist
        const response = await fetch("/api/wishlist", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ productId: product.id }),
        });

        if (response.ok) {
          setIsInWishlist(true);
          showToast("Added to wishlist", "success");
          refreshUnreadCount();
          refreshNotifications();
        } else {
          const data = await response.json();
          showToast(data.error || "Failed to add to wishlist", "error");
        }
      }
    } catch (error) {
      console.error("Error updating wishlist:", error);
      showToast("An unexpected error occurred.", "error");
    } finally {
      setWishlistLoading(false);
    }
  };

  const handleShare = async () => {
    const shareData = {
      title: product?.name || "Check out this product",
      text: product?.shortDescription || "Amazing product from Herbalicious",
      url: window.location.href,
    };

    try {
      if (navigator.share && navigator.canShare(shareData)) {
        await navigator.share(shareData);
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(window.location.href);
        alert("Product link copied to clipboard!");
      }
    } catch (error) {
      console.error("Error sharing:", error);
      // Fallback: copy to clipboard
      try {
        await navigator.clipboard.writeText(window.location.href);
        alert("Product link copied to clipboard!");
      } catch (clipboardError) {
        console.error("Clipboard error:", clipboardError);
        alert("Unable to share. Please copy the URL manually.");
      }
    }
  };

  if (loading) {
    return <ProductDetailSkeleton />;
  }

  if (error || !product) {
    return (
      <div className="flex items-center justify-center py-20">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error || "Product not found"}</p>
          <button
            onClick={() => router.back()}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  // Calculate flash sale prices
  const saleInfo = getFlashSalePrice(currentPrice, flashSaleSettings);
  const displayPrice = saleInfo.salePrice;
  const showOriginalPrice = saleInfo.isOnSale && currentPrice > 0;

  return (
    <div className="lg:grid lg:grid-cols-12 lg:gap-8">
      {/* Mobile Layout */}
      <div className="lg:hidden bg-white">
        {/* Header */}
        <div className="sticky top-16 bg-white z-30 flex items-center justify-between px-4 py-3 border-b">
          <button
            onClick={() => router.back()}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleWishlistToggle}
              disabled={wishlistLoading}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors"
            >
              <Heart
                className={`w-5 h-5 ${isInWishlist ? "text-red-500 fill-current" : "text-gray-600"}`}
              />
            </button>
            <button
              onClick={handleShare}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors"
            >
              <Share2 className="w-5 h-5 text-gray-600" />
            </button>
          </div>
        </div>

        {/* Product Image Gallery */}
        <div className="relative p-4">
          <ProductImageGallery
            images={product.images || []}
            productName={product.name}
          />
          <div className="absolute top-8 right-8 bg-white rounded-full p-2 shadow-sm">
            <div className="flex items-center space-x-1">
              <Star className="w-4 h-4 text-yellow-400 fill-current" />
              <span className="text-sm font-medium text-gray-700">
                {product.rating}
              </span>
            </div>
          </div>
        </div>

        {/* Product Info */}
        <div className="p-6">
          <div className="mb-4">
            <h1 className="text-2xl font-bold text-gray-800 mb-2">
              {product.name}
            </h1>
            <p className="text-gray-600 mb-3">{product.shortDescription}</p>

            {/* Categories and Reviews on same line */}
            <div className="flex items-center justify-between mb-4">
              <div>
                <ProductCategories
                  product={product._raw || product}
                  showAsLinks={true}
                  size="base"
                />
              </div>
              <div className="flex items-center space-x-2">
                <div className="flex items-center space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-4 h-4 ${
                        i < Math.floor(product?.rating || 0)
                          ? "text-yellow-400 fill-current"
                          : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm text-gray-500">
                  ({product?.reviews || 0} reviews)
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex flex-col">
                <div className="flex items-center space-x-3">
                  <span className="text-3xl font-bold text-gray-900">
                    {displayPrice > 0
                      ? quantity > 1
                        ? `₹${(displayPrice * quantity).toFixed(0)}`
                        : `₹${displayPrice}`
                      : "Loading..."}
                  </span>
                  {showOriginalPrice && (
                    <span className="text-xl text-gray-500 line-through">
                      {quantity > 1
                        ? `₹${(currentPrice * quantity).toFixed(0)}`
                        : `₹${currentPrice}`}
                    </span>
                  )}
                </div>
                {quantity > 1 && displayPrice > 0 && (
                  <span className="text-sm text-gray-500">
                    ₹{displayPrice} × {quantity}
                  </span>
                )}
                {saleInfo.isOnSale && saleInfo.discountPercentage > 0 && (
                  <span className="text-sm text-red-600 font-semibold mt-1">
                    Save {saleInfo.discountPercentage}% - Flash Sale!
                  </span>
                )}
                <div className="flex items-center space-x-2 mt-1">
                  <div className="flex items-center space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`w-4 h-4 ${
                          i < Math.floor(product?.rating || 0)
                            ? "text-yellow-400 fill-current"
                            : "text-gray-300"
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-sm text-gray-500">
                    ({product?.reviews || 0} reviews)
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="mb-6">
            <div className="flex border-b border-gray-200">
              {[
                { id: "description", label: "Description" },
                { id: "reviews", label: "Reviews" },
                { id: "faqs", label: "FAQs" },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`px-4 py-2 text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? "text-green-600 border-b-2 border-green-600"
                      : "text-gray-500 hover:text-gray-700"
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>

            <div className="py-4">
              {activeTab === "description" && (
                <div>
                  <p className="text-gray-600 mb-4">{product.description}</p>
                  {product.benefits.length > 0 && (
                    <div>
                      <h3 className="font-semibold text-gray-800 mb-2">
                        Benefits:
                      </h3>
                      <ul className="space-y-1">
                        {product.benefits.map((benefit, index) => (
                          <li
                            key={index}
                            className="flex items-center space-x-2"
                          >
                            <Check className="w-4 h-4 text-green-600" />
                            <span className="text-sm text-gray-600">
                              {benefit}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}

              {activeTab === "reviews" && (
                <div>
                  <ProductReviews productId={product.id} />
                </div>
              )}

              {activeTab === "faqs" && (
                <div>
                  <ProductFAQs productId={product.id} />
                </div>
              )}
            </div>
          </div>

          {/* Product Variations */}
          <div className="mb-6">
            <ProductVariationSelector
              productId={product.id}
              basePrice={product.price}
              onVariationChange={handleVariationChange}
            />
          </div>

          {/* Quantity and Add to Cart */}
          <div className="space-y-3">
            {/* Quantity Selector */}
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                className="w-10 h-10 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50"
              >
                -
              </button>
              <span className="w-10 text-center font-medium">{quantity}</span>
              <button
                onClick={() => setQuantity(quantity + 1)}
                className="w-10 h-10 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50"
              >
                +
              </button>
            </div>

            <button
              onClick={handleAddToCart}
              disabled={isAddedToCart || currentPrice === 0}
              className={`w-full py-3 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-2 ${
                isAddedToCart
                  ? "bg-green-500 text-white"
                  : currentPrice === 0
                    ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                    : "bg-green-600 text-white hover:bg-green-700"
              }`}
            >
              {isAddedToCart ? (
                <>
                  <Check className="w-5 h-5" />
                  <span>Added!</span>
                </>
              ) : (
                <>
                  <ShoppingCart className="w-5 h-5" />
                  <span>Add to Cart</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:block lg:col-span-12">
        <div className="py-8">
          {/* Back Button */}
          <button
            onClick={() => router.back()}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors mb-8"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>Back to Products</span>
          </button>

          <div className="grid grid-cols-2 gap-12">
            {/* Product Image Gallery */}
            <div className="relative">
              <ProductImageGallery
                images={product.images || []}
                productName={product.name}
              />
              <div className="absolute top-6 right-6 bg-white rounded-full p-3 shadow-lg">
                <div className="flex items-center space-x-2">
                  <Star className="w-5 h-5 text-yellow-400 fill-current" />
                  <span className="font-medium text-gray-700">
                    {product.rating}
                  </span>
                </div>
              </div>
            </div>

            {/* Product Info */}
            <div>
              <div className="mb-8">
                <h1 className="text-4xl font-bold text-gray-800 mb-4">
                  {product.name}
                </h1>
                <p className="text-xl text-gray-600 mb-6">
                  {product.shortDescription}
                </p>

                {/* Categories and Reviews on same line */}
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <ProductCategories
                      product={product._raw || product}
                      showAsLinks={true}
                      size="lg"
                    />
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-1">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`w-5 h-5 ${
                            i < Math.floor(product?.rating || 0)
                              ? "text-yellow-400 fill-current"
                              : "text-gray-300"
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-gray-600">
                      ({product?.reviews || 0} reviews)
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between mb-6">
                  <div>
                    <div className="flex items-center space-x-4">
                      <span className="text-4xl font-bold text-gray-900">
                        {displayPrice > 0
                          ? quantity > 1
                            ? `₹${(displayPrice * quantity).toFixed(0)}`
                            : `₹${displayPrice}`
                          : "Loading..."}
                      </span>
                      {showOriginalPrice && (
                        <span className="text-2xl text-gray-500 line-through">
                          {quantity > 1
                            ? `₹${(currentPrice * quantity).toFixed(0)}`
                            : `₹${currentPrice}`}
                        </span>
                      )}
                    </div>
                    {quantity > 1 && displayPrice > 0 && (
                      <div className="text-sm text-gray-500 mt-1">
                        ₹{displayPrice} × {quantity}
                      </div>
                    )}
                    {saleInfo.isOnSale && saleInfo.discountPercentage > 0 && (
                      <div className="text-lg text-red-600 font-semibold mt-2">
                        Save {saleInfo.discountPercentage}% - Flash Sale!
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={handleWishlistToggle}
                      disabled={wishlistLoading}
                      className="p-3 rounded-full border border-gray-200 hover:bg-gray-50 transition-colors"
                    >
                      <Heart
                        className={`w-6 h-6 ${isInWishlist ? "text-red-500 fill-current" : "text-gray-600"}`}
                      />
                    </button>
                    <button
                      onClick={handleShare}
                      className="p-3 rounded-full border border-gray-200 hover:bg-gray-50 transition-colors"
                    >
                      <Share2 className="w-6 h-6 text-gray-600" />
                    </button>
                  </div>
                </div>

                {/* Product Variations */}
                <div className="mb-8">
                  <ProductVariationSelector
                    productId={product.id}
                    basePrice={product.price}
                    onVariationChange={handleVariationChange}
                  />
                </div>

                {/* Quantity and Add to Cart */}
                <div className="space-y-4">
                  {/* Quantity Selector */}
                  <div className="flex items-center space-x-4">
                    <span className="text-gray-700 font-medium">Quantity:</span>
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => setQuantity(Math.max(1, quantity - 1))}
                        className="w-12 h-12 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50 text-lg transition-colors"
                      >
                        -
                      </button>
                      <span className="w-12 text-center font-medium text-lg">
                        {quantity}
                      </span>
                      <button
                        onClick={() => setQuantity(quantity + 1)}
                        className="w-12 h-12 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50 text-lg transition-colors"
                      >
                        +
                      </button>
                    </div>
                  </div>

                  <button
                    onClick={handleAddToCart}
                    disabled={isAddedToCart || currentPrice === 0}
                    className={`w-full py-4 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-3 text-lg ${
                      isAddedToCart
                        ? "bg-green-500 text-white"
                        : currentPrice === 0
                          ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                          : "bg-green-600 text-white hover:bg-green-700"
                    }`}
                  >
                    {isAddedToCart ? (
                      <>
                        <Check className="w-6 h-6" />
                        <span>Added!</span>
                      </>
                    ) : (
                      <>
                        <ShoppingCart className="w-6 h-6" />
                        <span>Add to Cart</span>
                      </>
                    )}
                  </button>
                </div>
              </div>

              {/* Tabs */}
              <div>
                <div className="flex border-b border-gray-200 mb-6">
                  {[
                    { id: "description", label: "Description" },
                    { id: "reviews", label: "Reviews" },
                    { id: "faqs", label: "FAQs" },
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id as any)}
                      className={`px-6 py-3 font-medium transition-colors ${
                        activeTab === tab.id
                          ? "text-green-600 border-b-2 border-green-600"
                          : "text-gray-500 hover:text-gray-700"
                      }`}
                    >
                      {tab.label}
                    </button>
                  ))}
                </div>

                <div>
                  {activeTab === "description" && (
                    <div>
                      <p className="text-gray-600 mb-6 leading-relaxed">
                        {product.description}
                      </p>
                      {product.benefits.length > 0 && (
                        <div>
                          <h3 className="text-xl font-semibold text-gray-800 mb-4">
                            Benefits:
                          </h3>
                          <ul className="space-y-3">
                            {product.benefits.map((benefit, index) => (
                              <li
                                key={index}
                                className="flex items-center space-x-3"
                              >
                                <Check className="w-5 h-5 text-green-600" />
                                <span className="text-gray-600">{benefit}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  )}

                  {activeTab === "reviews" && (
                    <div>
                      <ProductReviews productId={product.id} />
                    </div>
                  )}

                  {activeTab === "faqs" && (
                    <div>
                      <ProductFAQs productId={product.id} />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;
