"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/embla-carousel";
exports.ids = ["vendor-chunks/embla-carousel"];
exports.modules = {

/***/ "(ssr)/./node_modules/embla-carousel/esm/embla-carousel.esm.js":
/*!***************************************************************!*\
  !*** ./node_modules/embla-carousel/esm/embla-carousel.esm.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EmblaCarousel)\n/* harmony export */ });\nfunction isNumber(subject) {\n  return typeof subject === 'number';\n}\nfunction isString(subject) {\n  return typeof subject === 'string';\n}\nfunction isBoolean(subject) {\n  return typeof subject === 'boolean';\n}\nfunction isObject(subject) {\n  return Object.prototype.toString.call(subject) === '[object Object]';\n}\nfunction mathAbs(n) {\n  return Math.abs(n);\n}\nfunction mathSign(n) {\n  return Math.sign(n);\n}\nfunction deltaAbs(valueB, valueA) {\n  return mathAbs(valueB - valueA);\n}\nfunction factorAbs(valueB, valueA) {\n  if (valueB === 0 || valueA === 0) return 0;\n  if (mathAbs(valueB) <= mathAbs(valueA)) return 0;\n  const diff = deltaAbs(mathAbs(valueB), mathAbs(valueA));\n  return mathAbs(diff / valueB);\n}\nfunction roundToTwoDecimals(num) {\n  return Math.round(num * 100) / 100;\n}\nfunction arrayKeys(array) {\n  return objectKeys(array).map(Number);\n}\nfunction arrayLast(array) {\n  return array[arrayLastIndex(array)];\n}\nfunction arrayLastIndex(array) {\n  return Math.max(0, array.length - 1);\n}\nfunction arrayIsLastIndex(array, index) {\n  return index === arrayLastIndex(array);\n}\nfunction arrayFromNumber(n, startAt = 0) {\n  return Array.from(Array(n), (_, i) => startAt + i);\n}\nfunction objectKeys(object) {\n  return Object.keys(object);\n}\nfunction objectsMergeDeep(objectA, objectB) {\n  return [objectA, objectB].reduce((mergedObjects, currentObject) => {\n    objectKeys(currentObject).forEach(key => {\n      const valueA = mergedObjects[key];\n      const valueB = currentObject[key];\n      const areObjects = isObject(valueA) && isObject(valueB);\n      mergedObjects[key] = areObjects ? objectsMergeDeep(valueA, valueB) : valueB;\n    });\n    return mergedObjects;\n  }, {});\n}\nfunction isMouseEvent(evt, ownerWindow) {\n  return typeof ownerWindow.MouseEvent !== 'undefined' && evt instanceof ownerWindow.MouseEvent;\n}\n\nfunction Alignment(align, viewSize) {\n  const predefined = {\n    start,\n    center,\n    end\n  };\n  function start() {\n    return 0;\n  }\n  function center(n) {\n    return end(n) / 2;\n  }\n  function end(n) {\n    return viewSize - n;\n  }\n  function measure(n, index) {\n    if (isString(align)) return predefined[align](n);\n    return align(viewSize, n, index);\n  }\n  const self = {\n    measure\n  };\n  return self;\n}\n\nfunction EventStore() {\n  let listeners = [];\n  function add(node, type, handler, options = {\n    passive: true\n  }) {\n    let removeListener;\n    if ('addEventListener' in node) {\n      node.addEventListener(type, handler, options);\n      removeListener = () => node.removeEventListener(type, handler, options);\n    } else {\n      const legacyMediaQueryList = node;\n      legacyMediaQueryList.addListener(handler);\n      removeListener = () => legacyMediaQueryList.removeListener(handler);\n    }\n    listeners.push(removeListener);\n    return self;\n  }\n  function clear() {\n    listeners = listeners.filter(remove => remove());\n  }\n  const self = {\n    add,\n    clear\n  };\n  return self;\n}\n\nfunction Animations(ownerDocument, ownerWindow, update, render) {\n  const documentVisibleHandler = EventStore();\n  const fixedTimeStep = 1000 / 60;\n  let lastTimeStamp = null;\n  let accumulatedTime = 0;\n  let animationId = 0;\n  function init() {\n    documentVisibleHandler.add(ownerDocument, 'visibilitychange', () => {\n      if (ownerDocument.hidden) reset();\n    });\n  }\n  function destroy() {\n    stop();\n    documentVisibleHandler.clear();\n  }\n  function animate(timeStamp) {\n    if (!animationId) return;\n    if (!lastTimeStamp) {\n      lastTimeStamp = timeStamp;\n      update();\n      update();\n    }\n    const timeElapsed = timeStamp - lastTimeStamp;\n    lastTimeStamp = timeStamp;\n    accumulatedTime += timeElapsed;\n    while (accumulatedTime >= fixedTimeStep) {\n      update();\n      accumulatedTime -= fixedTimeStep;\n    }\n    const alpha = accumulatedTime / fixedTimeStep;\n    render(alpha);\n    if (animationId) {\n      animationId = ownerWindow.requestAnimationFrame(animate);\n    }\n  }\n  function start() {\n    if (animationId) return;\n    animationId = ownerWindow.requestAnimationFrame(animate);\n  }\n  function stop() {\n    ownerWindow.cancelAnimationFrame(animationId);\n    lastTimeStamp = null;\n    accumulatedTime = 0;\n    animationId = 0;\n  }\n  function reset() {\n    lastTimeStamp = null;\n    accumulatedTime = 0;\n  }\n  const self = {\n    init,\n    destroy,\n    start,\n    stop,\n    update,\n    render\n  };\n  return self;\n}\n\nfunction Axis(axis, contentDirection) {\n  const isRightToLeft = contentDirection === 'rtl';\n  const isVertical = axis === 'y';\n  const scroll = isVertical ? 'y' : 'x';\n  const cross = isVertical ? 'x' : 'y';\n  const sign = !isVertical && isRightToLeft ? -1 : 1;\n  const startEdge = getStartEdge();\n  const endEdge = getEndEdge();\n  function measureSize(nodeRect) {\n    const {\n      height,\n      width\n    } = nodeRect;\n    return isVertical ? height : width;\n  }\n  function getStartEdge() {\n    if (isVertical) return 'top';\n    return isRightToLeft ? 'right' : 'left';\n  }\n  function getEndEdge() {\n    if (isVertical) return 'bottom';\n    return isRightToLeft ? 'left' : 'right';\n  }\n  function direction(n) {\n    return n * sign;\n  }\n  const self = {\n    scroll,\n    cross,\n    startEdge,\n    endEdge,\n    measureSize,\n    direction\n  };\n  return self;\n}\n\nfunction Limit(min = 0, max = 0) {\n  const length = mathAbs(min - max);\n  function reachedMin(n) {\n    return n < min;\n  }\n  function reachedMax(n) {\n    return n > max;\n  }\n  function reachedAny(n) {\n    return reachedMin(n) || reachedMax(n);\n  }\n  function constrain(n) {\n    if (!reachedAny(n)) return n;\n    return reachedMin(n) ? min : max;\n  }\n  function removeOffset(n) {\n    if (!length) return n;\n    return n - length * Math.ceil((n - max) / length);\n  }\n  const self = {\n    length,\n    max,\n    min,\n    constrain,\n    reachedAny,\n    reachedMax,\n    reachedMin,\n    removeOffset\n  };\n  return self;\n}\n\nfunction Counter(max, start, loop) {\n  const {\n    constrain\n  } = Limit(0, max);\n  const loopEnd = max + 1;\n  let counter = withinLimit(start);\n  function withinLimit(n) {\n    return !loop ? constrain(n) : mathAbs((loopEnd + n) % loopEnd);\n  }\n  function get() {\n    return counter;\n  }\n  function set(n) {\n    counter = withinLimit(n);\n    return self;\n  }\n  function add(n) {\n    return clone().set(get() + n);\n  }\n  function clone() {\n    return Counter(max, get(), loop);\n  }\n  const self = {\n    get,\n    set,\n    add,\n    clone\n  };\n  return self;\n}\n\nfunction DragHandler(axis, rootNode, ownerDocument, ownerWindow, target, dragTracker, location, animation, scrollTo, scrollBody, scrollTarget, index, eventHandler, percentOfView, dragFree, dragThreshold, skipSnaps, baseFriction, watchDrag) {\n  const {\n    cross: crossAxis,\n    direction\n  } = axis;\n  const focusNodes = ['INPUT', 'SELECT', 'TEXTAREA'];\n  const nonPassiveEvent = {\n    passive: false\n  };\n  const initEvents = EventStore();\n  const dragEvents = EventStore();\n  const goToNextThreshold = Limit(50, 225).constrain(percentOfView.measure(20));\n  const snapForceBoost = {\n    mouse: 300,\n    touch: 400\n  };\n  const freeForceBoost = {\n    mouse: 500,\n    touch: 600\n  };\n  const baseSpeed = dragFree ? 43 : 25;\n  let isMoving = false;\n  let startScroll = 0;\n  let startCross = 0;\n  let pointerIsDown = false;\n  let preventScroll = false;\n  let preventClick = false;\n  let isMouse = false;\n  function init(emblaApi) {\n    if (!watchDrag) return;\n    function downIfAllowed(evt) {\n      if (isBoolean(watchDrag) || watchDrag(emblaApi, evt)) down(evt);\n    }\n    const node = rootNode;\n    initEvents.add(node, 'dragstart', evt => evt.preventDefault(), nonPassiveEvent).add(node, 'touchmove', () => undefined, nonPassiveEvent).add(node, 'touchend', () => undefined).add(node, 'touchstart', downIfAllowed).add(node, 'mousedown', downIfAllowed).add(node, 'touchcancel', up).add(node, 'contextmenu', up).add(node, 'click', click, true);\n  }\n  function destroy() {\n    initEvents.clear();\n    dragEvents.clear();\n  }\n  function addDragEvents() {\n    const node = isMouse ? ownerDocument : rootNode;\n    dragEvents.add(node, 'touchmove', move, nonPassiveEvent).add(node, 'touchend', up).add(node, 'mousemove', move, nonPassiveEvent).add(node, 'mouseup', up);\n  }\n  function isFocusNode(node) {\n    const nodeName = node.nodeName || '';\n    return focusNodes.includes(nodeName);\n  }\n  function forceBoost() {\n    const boost = dragFree ? freeForceBoost : snapForceBoost;\n    const type = isMouse ? 'mouse' : 'touch';\n    return boost[type];\n  }\n  function allowedForce(force, targetChanged) {\n    const next = index.add(mathSign(force) * -1);\n    const baseForce = scrollTarget.byDistance(force, !dragFree).distance;\n    if (dragFree || mathAbs(force) < goToNextThreshold) return baseForce;\n    if (skipSnaps && targetChanged) return baseForce * 0.5;\n    return scrollTarget.byIndex(next.get(), 0).distance;\n  }\n  function down(evt) {\n    const isMouseEvt = isMouseEvent(evt, ownerWindow);\n    isMouse = isMouseEvt;\n    preventClick = dragFree && isMouseEvt && !evt.buttons && isMoving;\n    isMoving = deltaAbs(target.get(), location.get()) >= 2;\n    if (isMouseEvt && evt.button !== 0) return;\n    if (isFocusNode(evt.target)) return;\n    pointerIsDown = true;\n    dragTracker.pointerDown(evt);\n    scrollBody.useFriction(0).useDuration(0);\n    target.set(location);\n    addDragEvents();\n    startScroll = dragTracker.readPoint(evt);\n    startCross = dragTracker.readPoint(evt, crossAxis);\n    eventHandler.emit('pointerDown');\n  }\n  function move(evt) {\n    const isTouchEvt = !isMouseEvent(evt, ownerWindow);\n    if (isTouchEvt && evt.touches.length >= 2) return up(evt);\n    const lastScroll = dragTracker.readPoint(evt);\n    const lastCross = dragTracker.readPoint(evt, crossAxis);\n    const diffScroll = deltaAbs(lastScroll, startScroll);\n    const diffCross = deltaAbs(lastCross, startCross);\n    if (!preventScroll && !isMouse) {\n      if (!evt.cancelable) return up(evt);\n      preventScroll = diffScroll > diffCross;\n      if (!preventScroll) return up(evt);\n    }\n    const diff = dragTracker.pointerMove(evt);\n    if (diffScroll > dragThreshold) preventClick = true;\n    scrollBody.useFriction(0.3).useDuration(0.75);\n    animation.start();\n    target.add(direction(diff));\n    evt.preventDefault();\n  }\n  function up(evt) {\n    const currentLocation = scrollTarget.byDistance(0, false);\n    const targetChanged = currentLocation.index !== index.get();\n    const rawForce = dragTracker.pointerUp(evt) * forceBoost();\n    const force = allowedForce(direction(rawForce), targetChanged);\n    const forceFactor = factorAbs(rawForce, force);\n    const speed = baseSpeed - 10 * forceFactor;\n    const friction = baseFriction + forceFactor / 50;\n    preventScroll = false;\n    pointerIsDown = false;\n    dragEvents.clear();\n    scrollBody.useDuration(speed).useFriction(friction);\n    scrollTo.distance(force, !dragFree);\n    isMouse = false;\n    eventHandler.emit('pointerUp');\n  }\n  function click(evt) {\n    if (preventClick) {\n      evt.stopPropagation();\n      evt.preventDefault();\n      preventClick = false;\n    }\n  }\n  function pointerDown() {\n    return pointerIsDown;\n  }\n  const self = {\n    init,\n    destroy,\n    pointerDown\n  };\n  return self;\n}\n\nfunction DragTracker(axis, ownerWindow) {\n  const logInterval = 170;\n  let startEvent;\n  let lastEvent;\n  function readTime(evt) {\n    return evt.timeStamp;\n  }\n  function readPoint(evt, evtAxis) {\n    const property = evtAxis || axis.scroll;\n    const coord = `client${property === 'x' ? 'X' : 'Y'}`;\n    return (isMouseEvent(evt, ownerWindow) ? evt : evt.touches[0])[coord];\n  }\n  function pointerDown(evt) {\n    startEvent = evt;\n    lastEvent = evt;\n    return readPoint(evt);\n  }\n  function pointerMove(evt) {\n    const diff = readPoint(evt) - readPoint(lastEvent);\n    const expired = readTime(evt) - readTime(startEvent) > logInterval;\n    lastEvent = evt;\n    if (expired) startEvent = evt;\n    return diff;\n  }\n  function pointerUp(evt) {\n    if (!startEvent || !lastEvent) return 0;\n    const diffDrag = readPoint(lastEvent) - readPoint(startEvent);\n    const diffTime = readTime(evt) - readTime(startEvent);\n    const expired = readTime(evt) - readTime(lastEvent) > logInterval;\n    const force = diffDrag / diffTime;\n    const isFlick = diffTime && !expired && mathAbs(force) > 0.1;\n    return isFlick ? force : 0;\n  }\n  const self = {\n    pointerDown,\n    pointerMove,\n    pointerUp,\n    readPoint\n  };\n  return self;\n}\n\nfunction NodeRects() {\n  function measure(node) {\n    const {\n      offsetTop,\n      offsetLeft,\n      offsetWidth,\n      offsetHeight\n    } = node;\n    const offset = {\n      top: offsetTop,\n      right: offsetLeft + offsetWidth,\n      bottom: offsetTop + offsetHeight,\n      left: offsetLeft,\n      width: offsetWidth,\n      height: offsetHeight\n    };\n    return offset;\n  }\n  const self = {\n    measure\n  };\n  return self;\n}\n\nfunction PercentOfView(viewSize) {\n  function measure(n) {\n    return viewSize * (n / 100);\n  }\n  const self = {\n    measure\n  };\n  return self;\n}\n\nfunction ResizeHandler(container, eventHandler, ownerWindow, slides, axis, watchResize, nodeRects) {\n  const observeNodes = [container].concat(slides);\n  let resizeObserver;\n  let containerSize;\n  let slideSizes = [];\n  let destroyed = false;\n  function readSize(node) {\n    return axis.measureSize(nodeRects.measure(node));\n  }\n  function init(emblaApi) {\n    if (!watchResize) return;\n    containerSize = readSize(container);\n    slideSizes = slides.map(readSize);\n    function defaultCallback(entries) {\n      for (const entry of entries) {\n        if (destroyed) return;\n        const isContainer = entry.target === container;\n        const slideIndex = slides.indexOf(entry.target);\n        const lastSize = isContainer ? containerSize : slideSizes[slideIndex];\n        const newSize = readSize(isContainer ? container : slides[slideIndex]);\n        const diffSize = mathAbs(newSize - lastSize);\n        if (diffSize >= 0.5) {\n          emblaApi.reInit();\n          eventHandler.emit('resize');\n          break;\n        }\n      }\n    }\n    resizeObserver = new ResizeObserver(entries => {\n      if (isBoolean(watchResize) || watchResize(emblaApi, entries)) {\n        defaultCallback(entries);\n      }\n    });\n    ownerWindow.requestAnimationFrame(() => {\n      observeNodes.forEach(node => resizeObserver.observe(node));\n    });\n  }\n  function destroy() {\n    destroyed = true;\n    if (resizeObserver) resizeObserver.disconnect();\n  }\n  const self = {\n    init,\n    destroy\n  };\n  return self;\n}\n\nfunction ScrollBody(location, offsetLocation, previousLocation, target, baseDuration, baseFriction) {\n  let scrollVelocity = 0;\n  let scrollDirection = 0;\n  let scrollDuration = baseDuration;\n  let scrollFriction = baseFriction;\n  let rawLocation = location.get();\n  let rawLocationPrevious = 0;\n  function seek() {\n    const displacement = target.get() - location.get();\n    const isInstant = !scrollDuration;\n    let scrollDistance = 0;\n    if (isInstant) {\n      scrollVelocity = 0;\n      previousLocation.set(target);\n      location.set(target);\n      scrollDistance = displacement;\n    } else {\n      previousLocation.set(location);\n      scrollVelocity += displacement / scrollDuration;\n      scrollVelocity *= scrollFriction;\n      rawLocation += scrollVelocity;\n      location.add(scrollVelocity);\n      scrollDistance = rawLocation - rawLocationPrevious;\n    }\n    scrollDirection = mathSign(scrollDistance);\n    rawLocationPrevious = rawLocation;\n    return self;\n  }\n  function settled() {\n    const diff = target.get() - offsetLocation.get();\n    return mathAbs(diff) < 0.001;\n  }\n  function duration() {\n    return scrollDuration;\n  }\n  function direction() {\n    return scrollDirection;\n  }\n  function velocity() {\n    return scrollVelocity;\n  }\n  function useBaseDuration() {\n    return useDuration(baseDuration);\n  }\n  function useBaseFriction() {\n    return useFriction(baseFriction);\n  }\n  function useDuration(n) {\n    scrollDuration = n;\n    return self;\n  }\n  function useFriction(n) {\n    scrollFriction = n;\n    return self;\n  }\n  const self = {\n    direction,\n    duration,\n    velocity,\n    seek,\n    settled,\n    useBaseFriction,\n    useBaseDuration,\n    useFriction,\n    useDuration\n  };\n  return self;\n}\n\nfunction ScrollBounds(limit, location, target, scrollBody, percentOfView) {\n  const pullBackThreshold = percentOfView.measure(10);\n  const edgeOffsetTolerance = percentOfView.measure(50);\n  const frictionLimit = Limit(0.1, 0.99);\n  let disabled = false;\n  function shouldConstrain() {\n    if (disabled) return false;\n    if (!limit.reachedAny(target.get())) return false;\n    if (!limit.reachedAny(location.get())) return false;\n    return true;\n  }\n  function constrain(pointerDown) {\n    if (!shouldConstrain()) return;\n    const edge = limit.reachedMin(location.get()) ? 'min' : 'max';\n    const diffToEdge = mathAbs(limit[edge] - location.get());\n    const diffToTarget = target.get() - location.get();\n    const friction = frictionLimit.constrain(diffToEdge / edgeOffsetTolerance);\n    target.subtract(diffToTarget * friction);\n    if (!pointerDown && mathAbs(diffToTarget) < pullBackThreshold) {\n      target.set(limit.constrain(target.get()));\n      scrollBody.useDuration(25).useBaseFriction();\n    }\n  }\n  function toggleActive(active) {\n    disabled = !active;\n  }\n  const self = {\n    shouldConstrain,\n    constrain,\n    toggleActive\n  };\n  return self;\n}\n\nfunction ScrollContain(viewSize, contentSize, snapsAligned, containScroll, pixelTolerance) {\n  const scrollBounds = Limit(-contentSize + viewSize, 0);\n  const snapsBounded = measureBounded();\n  const scrollContainLimit = findScrollContainLimit();\n  const snapsContained = measureContained();\n  function usePixelTolerance(bound, snap) {\n    return deltaAbs(bound, snap) <= 1;\n  }\n  function findScrollContainLimit() {\n    const startSnap = snapsBounded[0];\n    const endSnap = arrayLast(snapsBounded);\n    const min = snapsBounded.lastIndexOf(startSnap);\n    const max = snapsBounded.indexOf(endSnap) + 1;\n    return Limit(min, max);\n  }\n  function measureBounded() {\n    return snapsAligned.map((snapAligned, index) => {\n      const {\n        min,\n        max\n      } = scrollBounds;\n      const snap = scrollBounds.constrain(snapAligned);\n      const isFirst = !index;\n      const isLast = arrayIsLastIndex(snapsAligned, index);\n      if (isFirst) return max;\n      if (isLast) return min;\n      if (usePixelTolerance(min, snap)) return min;\n      if (usePixelTolerance(max, snap)) return max;\n      return snap;\n    }).map(scrollBound => parseFloat(scrollBound.toFixed(3)));\n  }\n  function measureContained() {\n    if (contentSize <= viewSize + pixelTolerance) return [scrollBounds.max];\n    if (containScroll === 'keepSnaps') return snapsBounded;\n    const {\n      min,\n      max\n    } = scrollContainLimit;\n    return snapsBounded.slice(min, max);\n  }\n  const self = {\n    snapsContained,\n    scrollContainLimit\n  };\n  return self;\n}\n\nfunction ScrollLimit(contentSize, scrollSnaps, loop) {\n  const max = scrollSnaps[0];\n  const min = loop ? max - contentSize : arrayLast(scrollSnaps);\n  const limit = Limit(min, max);\n  const self = {\n    limit\n  };\n  return self;\n}\n\nfunction ScrollLooper(contentSize, limit, location, vectors) {\n  const jointSafety = 0.1;\n  const min = limit.min + jointSafety;\n  const max = limit.max + jointSafety;\n  const {\n    reachedMin,\n    reachedMax\n  } = Limit(min, max);\n  function shouldLoop(direction) {\n    if (direction === 1) return reachedMax(location.get());\n    if (direction === -1) return reachedMin(location.get());\n    return false;\n  }\n  function loop(direction) {\n    if (!shouldLoop(direction)) return;\n    const loopDistance = contentSize * (direction * -1);\n    vectors.forEach(v => v.add(loopDistance));\n  }\n  const self = {\n    loop\n  };\n  return self;\n}\n\nfunction ScrollProgress(limit) {\n  const {\n    max,\n    length\n  } = limit;\n  function get(n) {\n    const currentLocation = n - max;\n    return length ? currentLocation / -length : 0;\n  }\n  const self = {\n    get\n  };\n  return self;\n}\n\nfunction ScrollSnaps(axis, alignment, containerRect, slideRects, slidesToScroll) {\n  const {\n    startEdge,\n    endEdge\n  } = axis;\n  const {\n    groupSlides\n  } = slidesToScroll;\n  const alignments = measureSizes().map(alignment.measure);\n  const snaps = measureUnaligned();\n  const snapsAligned = measureAligned();\n  function measureSizes() {\n    return groupSlides(slideRects).map(rects => arrayLast(rects)[endEdge] - rects[0][startEdge]).map(mathAbs);\n  }\n  function measureUnaligned() {\n    return slideRects.map(rect => containerRect[startEdge] - rect[startEdge]).map(snap => -mathAbs(snap));\n  }\n  function measureAligned() {\n    return groupSlides(snaps).map(g => g[0]).map((snap, index) => snap + alignments[index]);\n  }\n  const self = {\n    snaps,\n    snapsAligned\n  };\n  return self;\n}\n\nfunction SlideRegistry(containSnaps, containScroll, scrollSnaps, scrollContainLimit, slidesToScroll, slideIndexes) {\n  const {\n    groupSlides\n  } = slidesToScroll;\n  const {\n    min,\n    max\n  } = scrollContainLimit;\n  const slideRegistry = createSlideRegistry();\n  function createSlideRegistry() {\n    const groupedSlideIndexes = groupSlides(slideIndexes);\n    const doNotContain = !containSnaps || containScroll === 'keepSnaps';\n    if (scrollSnaps.length === 1) return [slideIndexes];\n    if (doNotContain) return groupedSlideIndexes;\n    return groupedSlideIndexes.slice(min, max).map((group, index, groups) => {\n      const isFirst = !index;\n      const isLast = arrayIsLastIndex(groups, index);\n      if (isFirst) {\n        const range = arrayLast(groups[0]) + 1;\n        return arrayFromNumber(range);\n      }\n      if (isLast) {\n        const range = arrayLastIndex(slideIndexes) - arrayLast(groups)[0] + 1;\n        return arrayFromNumber(range, arrayLast(groups)[0]);\n      }\n      return group;\n    });\n  }\n  const self = {\n    slideRegistry\n  };\n  return self;\n}\n\nfunction ScrollTarget(loop, scrollSnaps, contentSize, limit, targetVector) {\n  const {\n    reachedAny,\n    removeOffset,\n    constrain\n  } = limit;\n  function minDistance(distances) {\n    return distances.concat().sort((a, b) => mathAbs(a) - mathAbs(b))[0];\n  }\n  function findTargetSnap(target) {\n    const distance = loop ? removeOffset(target) : constrain(target);\n    const ascDiffsToSnaps = scrollSnaps.map((snap, index) => ({\n      diff: shortcut(snap - distance, 0),\n      index\n    })).sort((d1, d2) => mathAbs(d1.diff) - mathAbs(d2.diff));\n    const {\n      index\n    } = ascDiffsToSnaps[0];\n    return {\n      index,\n      distance\n    };\n  }\n  function shortcut(target, direction) {\n    const targets = [target, target + contentSize, target - contentSize];\n    if (!loop) return target;\n    if (!direction) return minDistance(targets);\n    const matchingTargets = targets.filter(t => mathSign(t) === direction);\n    if (matchingTargets.length) return minDistance(matchingTargets);\n    return arrayLast(targets) - contentSize;\n  }\n  function byIndex(index, direction) {\n    const diffToSnap = scrollSnaps[index] - targetVector.get();\n    const distance = shortcut(diffToSnap, direction);\n    return {\n      index,\n      distance\n    };\n  }\n  function byDistance(distance, snap) {\n    const target = targetVector.get() + distance;\n    const {\n      index,\n      distance: targetSnapDistance\n    } = findTargetSnap(target);\n    const reachedBound = !loop && reachedAny(target);\n    if (!snap || reachedBound) return {\n      index,\n      distance\n    };\n    const diffToSnap = scrollSnaps[index] - targetSnapDistance;\n    const snapDistance = distance + shortcut(diffToSnap, 0);\n    return {\n      index,\n      distance: snapDistance\n    };\n  }\n  const self = {\n    byDistance,\n    byIndex,\n    shortcut\n  };\n  return self;\n}\n\nfunction ScrollTo(animation, indexCurrent, indexPrevious, scrollBody, scrollTarget, targetVector, eventHandler) {\n  function scrollTo(target) {\n    const distanceDiff = target.distance;\n    const indexDiff = target.index !== indexCurrent.get();\n    targetVector.add(distanceDiff);\n    if (distanceDiff) {\n      if (scrollBody.duration()) {\n        animation.start();\n      } else {\n        animation.update();\n        animation.render(1);\n        animation.update();\n      }\n    }\n    if (indexDiff) {\n      indexPrevious.set(indexCurrent.get());\n      indexCurrent.set(target.index);\n      eventHandler.emit('select');\n    }\n  }\n  function distance(n, snap) {\n    const target = scrollTarget.byDistance(n, snap);\n    scrollTo(target);\n  }\n  function index(n, direction) {\n    const targetIndex = indexCurrent.clone().set(n);\n    const target = scrollTarget.byIndex(targetIndex.get(), direction);\n    scrollTo(target);\n  }\n  const self = {\n    distance,\n    index\n  };\n  return self;\n}\n\nfunction SlideFocus(root, slides, slideRegistry, scrollTo, scrollBody, eventStore, eventHandler, watchFocus) {\n  const focusListenerOptions = {\n    passive: true,\n    capture: true\n  };\n  let lastTabPressTime = 0;\n  function init(emblaApi) {\n    if (!watchFocus) return;\n    function defaultCallback(index) {\n      const nowTime = new Date().getTime();\n      const diffTime = nowTime - lastTabPressTime;\n      if (diffTime > 10) return;\n      eventHandler.emit('slideFocusStart');\n      root.scrollLeft = 0;\n      const group = slideRegistry.findIndex(group => group.includes(index));\n      if (!isNumber(group)) return;\n      scrollBody.useDuration(0);\n      scrollTo.index(group, 0);\n      eventHandler.emit('slideFocus');\n    }\n    eventStore.add(document, 'keydown', registerTabPress, false);\n    slides.forEach((slide, slideIndex) => {\n      eventStore.add(slide, 'focus', evt => {\n        if (isBoolean(watchFocus) || watchFocus(emblaApi, evt)) {\n          defaultCallback(slideIndex);\n        }\n      }, focusListenerOptions);\n    });\n  }\n  function registerTabPress(event) {\n    if (event.code === 'Tab') lastTabPressTime = new Date().getTime();\n  }\n  const self = {\n    init\n  };\n  return self;\n}\n\nfunction Vector1D(initialValue) {\n  let value = initialValue;\n  function get() {\n    return value;\n  }\n  function set(n) {\n    value = normalizeInput(n);\n  }\n  function add(n) {\n    value += normalizeInput(n);\n  }\n  function subtract(n) {\n    value -= normalizeInput(n);\n  }\n  function normalizeInput(n) {\n    return isNumber(n) ? n : n.get();\n  }\n  const self = {\n    get,\n    set,\n    add,\n    subtract\n  };\n  return self;\n}\n\nfunction Translate(axis, container) {\n  const translate = axis.scroll === 'x' ? x : y;\n  const containerStyle = container.style;\n  let previousTarget = null;\n  let disabled = false;\n  function x(n) {\n    return `translate3d(${n}px,0px,0px)`;\n  }\n  function y(n) {\n    return `translate3d(0px,${n}px,0px)`;\n  }\n  function to(target) {\n    if (disabled) return;\n    const newTarget = roundToTwoDecimals(axis.direction(target));\n    if (newTarget === previousTarget) return;\n    containerStyle.transform = translate(newTarget);\n    previousTarget = newTarget;\n  }\n  function toggleActive(active) {\n    disabled = !active;\n  }\n  function clear() {\n    if (disabled) return;\n    containerStyle.transform = '';\n    if (!container.getAttribute('style')) container.removeAttribute('style');\n  }\n  const self = {\n    clear,\n    to,\n    toggleActive\n  };\n  return self;\n}\n\nfunction SlideLooper(axis, viewSize, contentSize, slideSizes, slideSizesWithGaps, snaps, scrollSnaps, location, slides) {\n  const roundingSafety = 0.5;\n  const ascItems = arrayKeys(slideSizesWithGaps);\n  const descItems = arrayKeys(slideSizesWithGaps).reverse();\n  const loopPoints = startPoints().concat(endPoints());\n  function removeSlideSizes(indexes, from) {\n    return indexes.reduce((a, i) => {\n      return a - slideSizesWithGaps[i];\n    }, from);\n  }\n  function slidesInGap(indexes, gap) {\n    return indexes.reduce((a, i) => {\n      const remainingGap = removeSlideSizes(a, gap);\n      return remainingGap > 0 ? a.concat([i]) : a;\n    }, []);\n  }\n  function findSlideBounds(offset) {\n    return snaps.map((snap, index) => ({\n      start: snap - slideSizes[index] + roundingSafety + offset,\n      end: snap + viewSize - roundingSafety + offset\n    }));\n  }\n  function findLoopPoints(indexes, offset, isEndEdge) {\n    const slideBounds = findSlideBounds(offset);\n    return indexes.map(index => {\n      const initial = isEndEdge ? 0 : -contentSize;\n      const altered = isEndEdge ? contentSize : 0;\n      const boundEdge = isEndEdge ? 'end' : 'start';\n      const loopPoint = slideBounds[index][boundEdge];\n      return {\n        index,\n        loopPoint,\n        slideLocation: Vector1D(-1),\n        translate: Translate(axis, slides[index]),\n        target: () => location.get() > loopPoint ? initial : altered\n      };\n    });\n  }\n  function startPoints() {\n    const gap = scrollSnaps[0];\n    const indexes = slidesInGap(descItems, gap);\n    return findLoopPoints(indexes, contentSize, false);\n  }\n  function endPoints() {\n    const gap = viewSize - scrollSnaps[0] - 1;\n    const indexes = slidesInGap(ascItems, gap);\n    return findLoopPoints(indexes, -contentSize, true);\n  }\n  function canLoop() {\n    return loopPoints.every(({\n      index\n    }) => {\n      const otherIndexes = ascItems.filter(i => i !== index);\n      return removeSlideSizes(otherIndexes, viewSize) <= 0.1;\n    });\n  }\n  function loop() {\n    loopPoints.forEach(loopPoint => {\n      const {\n        target,\n        translate,\n        slideLocation\n      } = loopPoint;\n      const shiftLocation = target();\n      if (shiftLocation === slideLocation.get()) return;\n      translate.to(shiftLocation);\n      slideLocation.set(shiftLocation);\n    });\n  }\n  function clear() {\n    loopPoints.forEach(loopPoint => loopPoint.translate.clear());\n  }\n  const self = {\n    canLoop,\n    clear,\n    loop,\n    loopPoints\n  };\n  return self;\n}\n\nfunction SlidesHandler(container, eventHandler, watchSlides) {\n  let mutationObserver;\n  let destroyed = false;\n  function init(emblaApi) {\n    if (!watchSlides) return;\n    function defaultCallback(mutations) {\n      for (const mutation of mutations) {\n        if (mutation.type === 'childList') {\n          emblaApi.reInit();\n          eventHandler.emit('slidesChanged');\n          break;\n        }\n      }\n    }\n    mutationObserver = new MutationObserver(mutations => {\n      if (destroyed) return;\n      if (isBoolean(watchSlides) || watchSlides(emblaApi, mutations)) {\n        defaultCallback(mutations);\n      }\n    });\n    mutationObserver.observe(container, {\n      childList: true\n    });\n  }\n  function destroy() {\n    if (mutationObserver) mutationObserver.disconnect();\n    destroyed = true;\n  }\n  const self = {\n    init,\n    destroy\n  };\n  return self;\n}\n\nfunction SlidesInView(container, slides, eventHandler, threshold) {\n  const intersectionEntryMap = {};\n  let inViewCache = null;\n  let notInViewCache = null;\n  let intersectionObserver;\n  let destroyed = false;\n  function init() {\n    intersectionObserver = new IntersectionObserver(entries => {\n      if (destroyed) return;\n      entries.forEach(entry => {\n        const index = slides.indexOf(entry.target);\n        intersectionEntryMap[index] = entry;\n      });\n      inViewCache = null;\n      notInViewCache = null;\n      eventHandler.emit('slidesInView');\n    }, {\n      root: container.parentElement,\n      threshold\n    });\n    slides.forEach(slide => intersectionObserver.observe(slide));\n  }\n  function destroy() {\n    if (intersectionObserver) intersectionObserver.disconnect();\n    destroyed = true;\n  }\n  function createInViewList(inView) {\n    return objectKeys(intersectionEntryMap).reduce((list, slideIndex) => {\n      const index = parseInt(slideIndex);\n      const {\n        isIntersecting\n      } = intersectionEntryMap[index];\n      const inViewMatch = inView && isIntersecting;\n      const notInViewMatch = !inView && !isIntersecting;\n      if (inViewMatch || notInViewMatch) list.push(index);\n      return list;\n    }, []);\n  }\n  function get(inView = true) {\n    if (inView && inViewCache) return inViewCache;\n    if (!inView && notInViewCache) return notInViewCache;\n    const slideIndexes = createInViewList(inView);\n    if (inView) inViewCache = slideIndexes;\n    if (!inView) notInViewCache = slideIndexes;\n    return slideIndexes;\n  }\n  const self = {\n    init,\n    destroy,\n    get\n  };\n  return self;\n}\n\nfunction SlideSizes(axis, containerRect, slideRects, slides, readEdgeGap, ownerWindow) {\n  const {\n    measureSize,\n    startEdge,\n    endEdge\n  } = axis;\n  const withEdgeGap = slideRects[0] && readEdgeGap;\n  const startGap = measureStartGap();\n  const endGap = measureEndGap();\n  const slideSizes = slideRects.map(measureSize);\n  const slideSizesWithGaps = measureWithGaps();\n  function measureStartGap() {\n    if (!withEdgeGap) return 0;\n    const slideRect = slideRects[0];\n    return mathAbs(containerRect[startEdge] - slideRect[startEdge]);\n  }\n  function measureEndGap() {\n    if (!withEdgeGap) return 0;\n    const style = ownerWindow.getComputedStyle(arrayLast(slides));\n    return parseFloat(style.getPropertyValue(`margin-${endEdge}`));\n  }\n  function measureWithGaps() {\n    return slideRects.map((rect, index, rects) => {\n      const isFirst = !index;\n      const isLast = arrayIsLastIndex(rects, index);\n      if (isFirst) return slideSizes[index] + startGap;\n      if (isLast) return slideSizes[index] + endGap;\n      return rects[index + 1][startEdge] - rect[startEdge];\n    }).map(mathAbs);\n  }\n  const self = {\n    slideSizes,\n    slideSizesWithGaps,\n    startGap,\n    endGap\n  };\n  return self;\n}\n\nfunction SlidesToScroll(axis, viewSize, slidesToScroll, loop, containerRect, slideRects, startGap, endGap, pixelTolerance) {\n  const {\n    startEdge,\n    endEdge,\n    direction\n  } = axis;\n  const groupByNumber = isNumber(slidesToScroll);\n  function byNumber(array, groupSize) {\n    return arrayKeys(array).filter(i => i % groupSize === 0).map(i => array.slice(i, i + groupSize));\n  }\n  function bySize(array) {\n    if (!array.length) return [];\n    return arrayKeys(array).reduce((groups, rectB, index) => {\n      const rectA = arrayLast(groups) || 0;\n      const isFirst = rectA === 0;\n      const isLast = rectB === arrayLastIndex(array);\n      const edgeA = containerRect[startEdge] - slideRects[rectA][startEdge];\n      const edgeB = containerRect[startEdge] - slideRects[rectB][endEdge];\n      const gapA = !loop && isFirst ? direction(startGap) : 0;\n      const gapB = !loop && isLast ? direction(endGap) : 0;\n      const chunkSize = mathAbs(edgeB - gapB - (edgeA + gapA));\n      if (index && chunkSize > viewSize + pixelTolerance) groups.push(rectB);\n      if (isLast) groups.push(array.length);\n      return groups;\n    }, []).map((currentSize, index, groups) => {\n      const previousSize = Math.max(groups[index - 1] || 0);\n      return array.slice(previousSize, currentSize);\n    });\n  }\n  function groupSlides(array) {\n    return groupByNumber ? byNumber(array, slidesToScroll) : bySize(array);\n  }\n  const self = {\n    groupSlides\n  };\n  return self;\n}\n\nfunction Engine(root, container, slides, ownerDocument, ownerWindow, options, eventHandler) {\n  // Options\n  const {\n    align,\n    axis: scrollAxis,\n    direction,\n    startIndex,\n    loop,\n    duration,\n    dragFree,\n    dragThreshold,\n    inViewThreshold,\n    slidesToScroll: groupSlides,\n    skipSnaps,\n    containScroll,\n    watchResize,\n    watchSlides,\n    watchDrag,\n    watchFocus\n  } = options;\n  // Measurements\n  const pixelTolerance = 2;\n  const nodeRects = NodeRects();\n  const containerRect = nodeRects.measure(container);\n  const slideRects = slides.map(nodeRects.measure);\n  const axis = Axis(scrollAxis, direction);\n  const viewSize = axis.measureSize(containerRect);\n  const percentOfView = PercentOfView(viewSize);\n  const alignment = Alignment(align, viewSize);\n  const containSnaps = !loop && !!containScroll;\n  const readEdgeGap = loop || !!containScroll;\n  const {\n    slideSizes,\n    slideSizesWithGaps,\n    startGap,\n    endGap\n  } = SlideSizes(axis, containerRect, slideRects, slides, readEdgeGap, ownerWindow);\n  const slidesToScroll = SlidesToScroll(axis, viewSize, groupSlides, loop, containerRect, slideRects, startGap, endGap, pixelTolerance);\n  const {\n    snaps,\n    snapsAligned\n  } = ScrollSnaps(axis, alignment, containerRect, slideRects, slidesToScroll);\n  const contentSize = -arrayLast(snaps) + arrayLast(slideSizesWithGaps);\n  const {\n    snapsContained,\n    scrollContainLimit\n  } = ScrollContain(viewSize, contentSize, snapsAligned, containScroll, pixelTolerance);\n  const scrollSnaps = containSnaps ? snapsContained : snapsAligned;\n  const {\n    limit\n  } = ScrollLimit(contentSize, scrollSnaps, loop);\n  // Indexes\n  const index = Counter(arrayLastIndex(scrollSnaps), startIndex, loop);\n  const indexPrevious = index.clone();\n  const slideIndexes = arrayKeys(slides);\n  // Animation\n  const update = ({\n    dragHandler,\n    scrollBody,\n    scrollBounds,\n    options: {\n      loop\n    }\n  }) => {\n    if (!loop) scrollBounds.constrain(dragHandler.pointerDown());\n    scrollBody.seek();\n  };\n  const render = ({\n    scrollBody,\n    translate,\n    location,\n    offsetLocation,\n    previousLocation,\n    scrollLooper,\n    slideLooper,\n    dragHandler,\n    animation,\n    eventHandler,\n    scrollBounds,\n    options: {\n      loop\n    }\n  }, alpha) => {\n    const shouldSettle = scrollBody.settled();\n    const withinBounds = !scrollBounds.shouldConstrain();\n    const hasSettled = loop ? shouldSettle : shouldSettle && withinBounds;\n    const hasSettledAndIdle = hasSettled && !dragHandler.pointerDown();\n    if (hasSettledAndIdle) animation.stop();\n    const interpolatedLocation = location.get() * alpha + previousLocation.get() * (1 - alpha);\n    offsetLocation.set(interpolatedLocation);\n    if (loop) {\n      scrollLooper.loop(scrollBody.direction());\n      slideLooper.loop();\n    }\n    translate.to(offsetLocation.get());\n    if (hasSettledAndIdle) eventHandler.emit('settle');\n    if (!hasSettled) eventHandler.emit('scroll');\n  };\n  const animation = Animations(ownerDocument, ownerWindow, () => update(engine), alpha => render(engine, alpha));\n  // Shared\n  const friction = 0.68;\n  const startLocation = scrollSnaps[index.get()];\n  const location = Vector1D(startLocation);\n  const previousLocation = Vector1D(startLocation);\n  const offsetLocation = Vector1D(startLocation);\n  const target = Vector1D(startLocation);\n  const scrollBody = ScrollBody(location, offsetLocation, previousLocation, target, duration, friction);\n  const scrollTarget = ScrollTarget(loop, scrollSnaps, contentSize, limit, target);\n  const scrollTo = ScrollTo(animation, index, indexPrevious, scrollBody, scrollTarget, target, eventHandler);\n  const scrollProgress = ScrollProgress(limit);\n  const eventStore = EventStore();\n  const slidesInView = SlidesInView(container, slides, eventHandler, inViewThreshold);\n  const {\n    slideRegistry\n  } = SlideRegistry(containSnaps, containScroll, scrollSnaps, scrollContainLimit, slidesToScroll, slideIndexes);\n  const slideFocus = SlideFocus(root, slides, slideRegistry, scrollTo, scrollBody, eventStore, eventHandler, watchFocus);\n  // Engine\n  const engine = {\n    ownerDocument,\n    ownerWindow,\n    eventHandler,\n    containerRect,\n    slideRects,\n    animation,\n    axis,\n    dragHandler: DragHandler(axis, root, ownerDocument, ownerWindow, target, DragTracker(axis, ownerWindow), location, animation, scrollTo, scrollBody, scrollTarget, index, eventHandler, percentOfView, dragFree, dragThreshold, skipSnaps, friction, watchDrag),\n    eventStore,\n    percentOfView,\n    index,\n    indexPrevious,\n    limit,\n    location,\n    offsetLocation,\n    previousLocation,\n    options,\n    resizeHandler: ResizeHandler(container, eventHandler, ownerWindow, slides, axis, watchResize, nodeRects),\n    scrollBody,\n    scrollBounds: ScrollBounds(limit, offsetLocation, target, scrollBody, percentOfView),\n    scrollLooper: ScrollLooper(contentSize, limit, offsetLocation, [location, offsetLocation, previousLocation, target]),\n    scrollProgress,\n    scrollSnapList: scrollSnaps.map(scrollProgress.get),\n    scrollSnaps,\n    scrollTarget,\n    scrollTo,\n    slideLooper: SlideLooper(axis, viewSize, contentSize, slideSizes, slideSizesWithGaps, snaps, scrollSnaps, offsetLocation, slides),\n    slideFocus,\n    slidesHandler: SlidesHandler(container, eventHandler, watchSlides),\n    slidesInView,\n    slideIndexes,\n    slideRegistry,\n    slidesToScroll,\n    target,\n    translate: Translate(axis, container)\n  };\n  return engine;\n}\n\nfunction EventHandler() {\n  let listeners = {};\n  let api;\n  function init(emblaApi) {\n    api = emblaApi;\n  }\n  function getListeners(evt) {\n    return listeners[evt] || [];\n  }\n  function emit(evt) {\n    getListeners(evt).forEach(e => e(api, evt));\n    return self;\n  }\n  function on(evt, cb) {\n    listeners[evt] = getListeners(evt).concat([cb]);\n    return self;\n  }\n  function off(evt, cb) {\n    listeners[evt] = getListeners(evt).filter(e => e !== cb);\n    return self;\n  }\n  function clear() {\n    listeners = {};\n  }\n  const self = {\n    init,\n    emit,\n    off,\n    on,\n    clear\n  };\n  return self;\n}\n\nconst defaultOptions = {\n  align: 'center',\n  axis: 'x',\n  container: null,\n  slides: null,\n  containScroll: 'trimSnaps',\n  direction: 'ltr',\n  slidesToScroll: 1,\n  inViewThreshold: 0,\n  breakpoints: {},\n  dragFree: false,\n  dragThreshold: 10,\n  loop: false,\n  skipSnaps: false,\n  duration: 25,\n  startIndex: 0,\n  active: true,\n  watchDrag: true,\n  watchResize: true,\n  watchSlides: true,\n  watchFocus: true\n};\n\nfunction OptionsHandler(ownerWindow) {\n  function mergeOptions(optionsA, optionsB) {\n    return objectsMergeDeep(optionsA, optionsB || {});\n  }\n  function optionsAtMedia(options) {\n    const optionsAtMedia = options.breakpoints || {};\n    const matchedMediaOptions = objectKeys(optionsAtMedia).filter(media => ownerWindow.matchMedia(media).matches).map(media => optionsAtMedia[media]).reduce((a, mediaOption) => mergeOptions(a, mediaOption), {});\n    return mergeOptions(options, matchedMediaOptions);\n  }\n  function optionsMediaQueries(optionsList) {\n    return optionsList.map(options => objectKeys(options.breakpoints || {})).reduce((acc, mediaQueries) => acc.concat(mediaQueries), []).map(ownerWindow.matchMedia);\n  }\n  const self = {\n    mergeOptions,\n    optionsAtMedia,\n    optionsMediaQueries\n  };\n  return self;\n}\n\nfunction PluginsHandler(optionsHandler) {\n  let activePlugins = [];\n  function init(emblaApi, plugins) {\n    activePlugins = plugins.filter(({\n      options\n    }) => optionsHandler.optionsAtMedia(options).active !== false);\n    activePlugins.forEach(plugin => plugin.init(emblaApi, optionsHandler));\n    return plugins.reduce((map, plugin) => Object.assign(map, {\n      [plugin.name]: plugin\n    }), {});\n  }\n  function destroy() {\n    activePlugins = activePlugins.filter(plugin => plugin.destroy());\n  }\n  const self = {\n    init,\n    destroy\n  };\n  return self;\n}\n\nfunction EmblaCarousel(root, userOptions, userPlugins) {\n  const ownerDocument = root.ownerDocument;\n  const ownerWindow = ownerDocument.defaultView;\n  const optionsHandler = OptionsHandler(ownerWindow);\n  const pluginsHandler = PluginsHandler(optionsHandler);\n  const mediaHandlers = EventStore();\n  const eventHandler = EventHandler();\n  const {\n    mergeOptions,\n    optionsAtMedia,\n    optionsMediaQueries\n  } = optionsHandler;\n  const {\n    on,\n    off,\n    emit\n  } = eventHandler;\n  const reInit = reActivate;\n  let destroyed = false;\n  let engine;\n  let optionsBase = mergeOptions(defaultOptions, EmblaCarousel.globalOptions);\n  let options = mergeOptions(optionsBase);\n  let pluginList = [];\n  let pluginApis;\n  let container;\n  let slides;\n  function storeElements() {\n    const {\n      container: userContainer,\n      slides: userSlides\n    } = options;\n    const customContainer = isString(userContainer) ? root.querySelector(userContainer) : userContainer;\n    container = customContainer || root.children[0];\n    const customSlides = isString(userSlides) ? container.querySelectorAll(userSlides) : userSlides;\n    slides = [].slice.call(customSlides || container.children);\n  }\n  function createEngine(options) {\n    const engine = Engine(root, container, slides, ownerDocument, ownerWindow, options, eventHandler);\n    if (options.loop && !engine.slideLooper.canLoop()) {\n      const optionsWithoutLoop = Object.assign({}, options, {\n        loop: false\n      });\n      return createEngine(optionsWithoutLoop);\n    }\n    return engine;\n  }\n  function activate(withOptions, withPlugins) {\n    if (destroyed) return;\n    optionsBase = mergeOptions(optionsBase, withOptions);\n    options = optionsAtMedia(optionsBase);\n    pluginList = withPlugins || pluginList;\n    storeElements();\n    engine = createEngine(options);\n    optionsMediaQueries([optionsBase, ...pluginList.map(({\n      options\n    }) => options)]).forEach(query => mediaHandlers.add(query, 'change', reActivate));\n    if (!options.active) return;\n    engine.translate.to(engine.location.get());\n    engine.animation.init();\n    engine.slidesInView.init();\n    engine.slideFocus.init(self);\n    engine.eventHandler.init(self);\n    engine.resizeHandler.init(self);\n    engine.slidesHandler.init(self);\n    if (engine.options.loop) engine.slideLooper.loop();\n    if (container.offsetParent && slides.length) engine.dragHandler.init(self);\n    pluginApis = pluginsHandler.init(self, pluginList);\n  }\n  function reActivate(withOptions, withPlugins) {\n    const startIndex = selectedScrollSnap();\n    deActivate();\n    activate(mergeOptions({\n      startIndex\n    }, withOptions), withPlugins);\n    eventHandler.emit('reInit');\n  }\n  function deActivate() {\n    engine.dragHandler.destroy();\n    engine.eventStore.clear();\n    engine.translate.clear();\n    engine.slideLooper.clear();\n    engine.resizeHandler.destroy();\n    engine.slidesHandler.destroy();\n    engine.slidesInView.destroy();\n    engine.animation.destroy();\n    pluginsHandler.destroy();\n    mediaHandlers.clear();\n  }\n  function destroy() {\n    if (destroyed) return;\n    destroyed = true;\n    mediaHandlers.clear();\n    deActivate();\n    eventHandler.emit('destroy');\n    eventHandler.clear();\n  }\n  function scrollTo(index, jump, direction) {\n    if (!options.active || destroyed) return;\n    engine.scrollBody.useBaseFriction().useDuration(jump === true ? 0 : options.duration);\n    engine.scrollTo.index(index, direction || 0);\n  }\n  function scrollNext(jump) {\n    const next = engine.index.add(1).get();\n    scrollTo(next, jump, -1);\n  }\n  function scrollPrev(jump) {\n    const prev = engine.index.add(-1).get();\n    scrollTo(prev, jump, 1);\n  }\n  function canScrollNext() {\n    const next = engine.index.add(1).get();\n    return next !== selectedScrollSnap();\n  }\n  function canScrollPrev() {\n    const prev = engine.index.add(-1).get();\n    return prev !== selectedScrollSnap();\n  }\n  function scrollSnapList() {\n    return engine.scrollSnapList;\n  }\n  function scrollProgress() {\n    return engine.scrollProgress.get(engine.offsetLocation.get());\n  }\n  function selectedScrollSnap() {\n    return engine.index.get();\n  }\n  function previousScrollSnap() {\n    return engine.indexPrevious.get();\n  }\n  function slidesInView() {\n    return engine.slidesInView.get();\n  }\n  function slidesNotInView() {\n    return engine.slidesInView.get(false);\n  }\n  function plugins() {\n    return pluginApis;\n  }\n  function internalEngine() {\n    return engine;\n  }\n  function rootNode() {\n    return root;\n  }\n  function containerNode() {\n    return container;\n  }\n  function slideNodes() {\n    return slides;\n  }\n  const self = {\n    canScrollNext,\n    canScrollPrev,\n    containerNode,\n    internalEngine,\n    destroy,\n    off,\n    on,\n    emit,\n    plugins,\n    previousScrollSnap,\n    reInit,\n    rootNode,\n    scrollNext,\n    scrollPrev,\n    scrollProgress,\n    scrollSnapList,\n    scrollTo,\n    selectedScrollSnap,\n    slideNodes,\n    slidesInView,\n    slidesNotInView\n  };\n  activate(userOptions, userPlugins);\n  setTimeout(() => eventHandler.emit('init'), 0);\n  return self;\n}\nEmblaCarousel.globalOptions = undefined;\n\n\n//# sourceMappingURL=embla-carousel.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/embla-carousel/esm/embla-carousel.esm.js\n");

/***/ })

};
;