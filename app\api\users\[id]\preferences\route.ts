import { NextRequest, NextResponse } from "next/server";
import { connectDB, User } from "@/app/lib/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/lib/auth";

// GET /api/users/[id]/preferences - Get user preferences
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    const isAdmin = (session.user as any)?.role === "ADMIN";
    const isOwnProfile = session.user.id === params.id;

    if (!isAdmin && !isOwnProfile) {
      return NextResponse.json(
        { success: false, error: "Forbidden" },
        { status: 403 },
      );
    }

    await connectDB();
    // Store preferences document inside User collection as a subdocument for simplicity
    // If you prefer a separate collection, create a model and switch to it.
    const user = await User.findById(params.id).lean();
    let preferences = (user as any)?.preferences || null;

    // Create default preferences if they don't exist
    if (!preferences) {
      // default preferences
      preferences = {
        language: "en-US",
        theme: "light",
        orderUpdates: true,
        promotions: false,
        newsletter: true,
        smsNotifications: false,
        emailNotifications: true,
        inAppNotifications: true,
        orderNotifications: true,
        wishlistNotifications: true,
        reviewNotifications: true,
        priceDropAlerts: false,
        adminMessages: true,
        broadcastMessages: true,
      };
      await User.findByIdAndUpdate(params.id, { $set: { preferences } });
    }

    return NextResponse.json({
      success: true,
      data: preferences,
    });
  } catch (error) {
    console.error("Error fetching user preferences:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch user preferences" },
      { status: 500 },
    );
  }
}

// PATCH /api/users/[id]/preferences - Update user preferences
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    const isAdmin = (session.user as any)?.role === "ADMIN";
    const isOwnProfile = session.user.id === params.id;

    if (!isAdmin && !isOwnProfile) {
      return NextResponse.json(
        { success: false, error: "Forbidden" },
        { status: 403 },
      );
    }

    const body = await request.json();
    const {
      language,
      theme,
      orderUpdates,
      promotions,
      newsletter,
      smsNotifications,
      // Notification preferences
      emailNotifications,
      inAppNotifications,
      orderNotifications,
      wishlistNotifications,
      reviewNotifications,
      priceDropAlerts,
      adminMessages,
      broadcastMessages,
    } = body;

    // Update or create preferences
    const updateDoc: any = {};
    if (language !== undefined) updateDoc["preferences.language"] = language;
    if (theme !== undefined) updateDoc["preferences.theme"] = theme;
    if (orderUpdates !== undefined) updateDoc["preferences.orderUpdates"] = orderUpdates;
    if (promotions !== undefined) updateDoc["preferences.promotions"] = promotions;
    if (newsletter !== undefined) updateDoc["preferences.newsletter"] = newsletter;
    if (smsNotifications !== undefined) updateDoc["preferences.smsNotifications"] = smsNotifications;
    if (emailNotifications !== undefined) updateDoc["preferences.emailNotifications"] = emailNotifications;
    if (inAppNotifications !== undefined) updateDoc["preferences.inAppNotifications"] = inAppNotifications;
    if (orderNotifications !== undefined) updateDoc["preferences.orderNotifications"] = orderNotifications;
    if (wishlistNotifications !== undefined) updateDoc["preferences.wishlistNotifications"] = wishlistNotifications;
    if (reviewNotifications !== undefined) updateDoc["preferences.reviewNotifications"] = reviewNotifications;
    if (priceDropAlerts !== undefined) updateDoc["preferences.priceDropAlerts"] = priceDropAlerts;
    if (adminMessages !== undefined) updateDoc["preferences.adminMessages"] = adminMessages;
    if (broadcastMessages !== undefined) updateDoc["preferences.broadcastMessages"] = broadcastMessages;

    // If user doc missing preferences entirely, set defaults then apply updates
    const defaults = {
      "preferences.language": "en-US",
      "preferences.theme": "light",
      "preferences.orderUpdates": true,
      "preferences.promotions": false,
      "preferences.newsletter": true,
      "preferences.smsNotifications": false,
      "preferences.emailNotifications": true,
      "preferences.inAppNotifications": true,
      "preferences.orderNotifications": true,
      "preferences.wishlistNotifications": true,
      "preferences.reviewNotifications": true,
      "preferences.priceDropAlerts": false,
      "preferences.adminMessages": true,
      "preferences.broadcastMessages": true,
    };

    const result = await User.findByIdAndUpdate(
      params.id,
      {
        $setOnInsert: defaults,
        $set: updateDoc,
      },
      { new: true, upsert: false, projection: { preferences: 1 } }
    ).lean();

    const preferences = (result as any)?.preferences ?? (await (async () => {
      // If result null, ensure user exists and set preferences field
      const exists = await User.exists({ _id: params.id });
      if (!exists) {
        return null;
      }
      await User.findByIdAndUpdate(params.id, { $set: defaults });
      return (await User.findById(params.id, { preferences: 1 }).lean() as any)?.preferences;
    })());

    return NextResponse.json({
      success: true,
      data: preferences,
      message: "Preferences updated successfully",
    });
  } catch (error) {
    console.error("Error updating user preferences:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update user preferences" },
      { status: 500 },
    );
  }
}
