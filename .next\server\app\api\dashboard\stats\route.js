"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/stats/route";
exports.ids = ["app/api/dashboard/stats/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_project_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/dashboard/stats/route.ts */ \"(rsc)/./app/api/dashboard/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/stats/route\",\n        pathname: \"/api/dashboard/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/stats/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\api\\\\dashboard\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_project_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/dashboard/stats/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/dashboard/stats/route.ts":
/*!******************************************!*\
  !*** ./app/api/dashboard/stats/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _app_lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/db */ \"(rsc)/./app/lib/db.ts\");\n/* harmony import */ var _app_lib_logger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/logger */ \"(rsc)/./app/lib/logger.ts\");\n\n\n\n// GET /api/dashboard/stats - Get dashboard statistics\n// NOTE: This endpoint is temporarily using mock data during Prisma to MongoDB migration\nasync function GET() {\n    try {\n        await (0,_app_lib_db__WEBPACK_IMPORTED_MODULE_1__.connectDB)();\n        // Get counts for all main entities using MongoDB\n        const [totalProducts, totalCategories, totalUsers, totalCustomers, totalOrders, activeProducts, featuredProducts] = await Promise.all([\n            _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.Product.countDocuments(),\n            _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.Category.countDocuments(),\n            _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.User.countDocuments(),\n            _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.User.countDocuments({\n                role: \"CUSTOMER\"\n            }),\n            _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.Order.countDocuments(),\n            _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.Product.countDocuments({\n                isActive: true\n            }),\n            _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.Product.countDocuments({\n                isFeatured: true\n            })\n        ]);\n        // Get recent products\n        const recentProducts = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.Product.find().sort({\n            createdAt: -1\n        }).limit(5).lean();\n        // Get recent users\n        const recentUsers = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.User.find().sort({\n            createdAt: -1\n        }).limit(5).select(\"-password\").lean();\n        // Calculate some basic stats\n        const stats = {\n            overview: {\n                totalProducts,\n                totalCategories,\n                totalUsers,\n                totalCustomers,\n                totalOrders,\n                activeProducts,\n                featuredProducts\n            },\n            recent: {\n                products: recentProducts,\n                users: recentUsers\n            },\n            growth: {\n                // These would be calculated based on time periods in a real app\n                productsGrowth: \"+12.5%\",\n                categoriesGrowth: \"+5.2%\",\n                usersGrowth: \"+8.1%\",\n                ordersGrowth: \"+15.3%\"\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: stats\n        });\n    } catch (error) {\n        _app_lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.error(\"Error fetching dashboard stats:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to fetch dashboard statistics\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/dashboard/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Address: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_1__.Address),\n/* harmony export */   Category: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_1__.Category),\n/* harmony export */   Coupon: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_1__.Coupon),\n/* harmony export */   Enquiry: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_1__.Enquiry),\n/* harmony export */   HomepageSetting: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_1__.HomepageSetting),\n/* harmony export */   Newsletter: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_1__.Newsletter),\n/* harmony export */   Notification: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_1__.Notification),\n/* harmony export */   NotificationTemplate: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_1__.NotificationTemplate),\n/* harmony export */   Order: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_1__.Order),\n/* harmony export */   OrderItem: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_1__.OrderItem),\n/* harmony export */   Product: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_1__.Product),\n/* harmony export */   ProductCategory: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_1__.ProductCategory),\n/* harmony export */   ProductImage: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_1__.ProductImage),\n/* harmony export */   ProductVariant: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_1__.ProductVariant),\n/* harmony export */   Review: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_1__.Review),\n/* harmony export */   Testimonial: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_1__.Testimonial),\n/* harmony export */   User: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_1__.User),\n/* harmony export */   Wishlist: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_1__.Wishlist),\n/* harmony export */   WishlistItem: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_1__.WishlistItem),\n/* harmony export */   connectDB: () => (/* reexport safe */ _mongoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mongoose */ \"(rsc)/./app/lib/mongoose.ts\");\n/* harmony import */ var _models__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./models */ \"(rsc)/./app/lib/models.ts\");\n// app/lib/db.ts\n\n // simple named export\n // re-export Mongoose models\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxnQkFBZ0I7QUFDbUI7QUFDZCxDQUFDLHNCQUFzQjtBQUNuQixDQUFDLDRCQUE0QiIsInNvdXJjZXMiOlsid2VicGFjazovL2hlcmJhbGljaW91cy1uZXh0anMvLi9hcHAvbGliL2RiLnRzPzU4ZmQiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gYXBwL2xpYi9kYi50c1xuaW1wb3J0IGNvbm5lY3REQiBmcm9tIFwiLi9tb25nb29zZVwiO1xuZXhwb3J0IHsgY29ubmVjdERCIH07IC8vIHNpbXBsZSBuYW1lZCBleHBvcnRcbmV4cG9ydCAqIGZyb20gXCIuL21vZGVsc1wiOyAvLyByZS1leHBvcnQgTW9uZ29vc2UgbW9kZWxzXG4iXSwibmFtZXMiOlsiY29ubmVjdERCIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/db.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/logger.ts":
/*!***************************!*\
  !*** ./app/lib/logger.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogLevel: () => (/* binding */ LogLevel),\n/* harmony export */   createRequestLogger: () => (/* binding */ createRequestLogger),\n/* harmony export */   devLog: () => (/* binding */ devLog),\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\nvar LogLevel;\n(function(LogLevel) {\n    LogLevel[LogLevel[\"ERROR\"] = 0] = \"ERROR\";\n    LogLevel[LogLevel[\"WARN\"] = 1] = \"WARN\";\n    LogLevel[LogLevel[\"INFO\"] = 2] = \"INFO\";\n    LogLevel[LogLevel[\"DEBUG\"] = 3] = \"DEBUG\";\n})(LogLevel || (LogLevel = {}));\nclass Logger {\n    constructor(){\n        this.isDevelopment = \"development\" === \"development\";\n        // Reduce verbosity in development - only show warnings and errors\n        this.logLevel = this.isDevelopment ? 1 : 2;\n    }\n    shouldLog(level) {\n        return level <= this.logLevel;\n    }\n    formatMessage(entry) {\n        const { timestamp, level, message, context, error, userId, requestId } = entry;\n        const levelName = LogLevel[level];\n        let formatted = `[${timestamp}] ${levelName}: ${message}`;\n        if (userId) {\n            formatted += ` | User: ${userId}`;\n        }\n        if (requestId) {\n            formatted += ` | Request: ${requestId}`;\n        }\n        if (context && Object.keys(context).length > 0) {\n            formatted += ` | Context: ${JSON.stringify(context)}`;\n        }\n        if (error) {\n            formatted += ` | Error: ${error.message}`;\n            if (this.isDevelopment && error.stack) {\n                formatted += `\\nStack: ${error.stack}`;\n            }\n        }\n        return formatted;\n    }\n    log(level, message, context, error) {\n        if (!this.shouldLog(level)) return;\n        const entry = {\n            timestamp: new Date().toISOString(),\n            level,\n            message,\n            context,\n            error\n        };\n        const formatted = this.formatMessage(entry);\n        // In development, use console methods for better formatting\n        if (this.isDevelopment) {\n            switch(level){\n                case 0:\n                    console.error(formatted);\n                    break;\n                case 1:\n                    console.warn(formatted);\n                    break;\n                case 2:\n                    console.info(formatted);\n                    break;\n                case 3:\n                    console.debug(formatted);\n                    break;\n            }\n        } else {\n            // In production, use structured logging (JSON format)\n            console.log(JSON.stringify(entry));\n        }\n    }\n    error(message, error, context) {\n        this.log(0, message, context, error);\n    }\n    warn(message, context) {\n        this.log(1, message, context);\n    }\n    info(message, context) {\n        this.log(2, message, context);\n    }\n    debug(message, context) {\n        this.log(3, message, context);\n    }\n    // API-specific logging methods\n    apiRequest(method, path, userId, context) {\n        this.info(`API ${method} ${path}`, {\n            ...context,\n            userId,\n            type: \"api_request\"\n        });\n    }\n    apiResponse(method, path, statusCode, duration, context) {\n        this.info(`API ${method} ${path} - ${statusCode}`, {\n            ...context,\n            statusCode,\n            duration,\n            type: \"api_response\"\n        });\n    }\n    apiError(method, path, error, userId, context) {\n        this.error(`API ${method} ${path} failed`, error, {\n            ...context,\n            userId,\n            type: \"api_error\"\n        });\n    }\n    // Authentication logging\n    authSuccess(userId, method, context) {\n        this.info(`Authentication successful`, {\n            ...context,\n            userId,\n            method,\n            type: \"auth_success\"\n        });\n    }\n    authFailure(email, method, reason, context) {\n        this.warn(`Authentication failed`, {\n            ...context,\n            email,\n            method,\n            reason,\n            type: \"auth_failure\"\n        });\n    }\n    // Database logging\n    dbQuery(operation, table, duration, context) {\n        this.debug(`DB ${operation} on ${table}`, {\n            ...context,\n            operation,\n            table,\n            duration,\n            type: \"db_query\"\n        });\n    }\n    dbError(operation, table, error, context) {\n        this.error(`DB ${operation} on ${table} failed`, error, {\n            ...context,\n            operation,\n            table,\n            type: \"db_error\"\n        });\n    }\n    // Security logging\n    securityEvent(event, severity, context) {\n        const level = severity === \"high\" ? 0 : severity === \"medium\" ? 1 : 2;\n        this.log(level, `Security event: ${event}`, {\n            ...context,\n            severity,\n            type: \"security_event\"\n        });\n    }\n    // Rate limiting logging\n    rateLimitHit(identifier, limit, window, context) {\n        this.warn(`Rate limit exceeded`, {\n            ...context,\n            identifier,\n            limit,\n            window,\n            type: \"rate_limit\"\n        });\n    }\n    // Email logging\n    emailSent(to, subject, template, context) {\n        this.info(`Email sent`, {\n            ...context,\n            to,\n            subject,\n            template,\n            type: \"email_sent\"\n        });\n    }\n    emailError(to, subject, error, context) {\n        this.error(`Email failed to send`, error, {\n            ...context,\n            to,\n            subject,\n            type: \"email_error\"\n        });\n    }\n    // Performance logging\n    performance(operation, duration, context) {\n        const level = duration > 5000 ? 1 : 3;\n        this.log(level, `Performance: ${operation} took ${duration}ms`, {\n            ...context,\n            operation,\n            duration,\n            type: \"performance\"\n        });\n    }\n}\n// Create singleton instance\nconst logger = new Logger();\n// Request logging middleware helper\nfunction createRequestLogger(req) {\n    const requestId = crypto.randomUUID();\n    const startTime = Date.now();\n    return {\n        requestId,\n        log: (message, context)=>{\n            logger.info(message, {\n                ...context,\n                requestId\n            });\n        },\n        error: (message, error, context)=>{\n            logger.error(message, error, {\n                ...context,\n                requestId\n            });\n        },\n        end: (statusCode)=>{\n            const duration = Date.now() - startTime;\n            logger.apiResponse(req.method || \"UNKNOWN\", new URL(req.url || \"\").pathname, statusCode, duration, {\n                requestId\n            });\n        }\n    };\n}\n// Development-only logging helpers\nconst devLog = {\n    info: (message, data)=>{\n        if (true) {\n            console.log(`🔍 ${message}`, data ? JSON.stringify(data, null, 2) : \"\");\n        }\n    },\n    error: (message, error)=>{\n        if (true) {\n            console.error(`❌ ${message}`, error);\n        }\n    },\n    warn: (message, data)=>{\n        if (true) {\n            console.warn(`⚠️ ${message}`, data ? JSON.stringify(data, null, 2) : \"\");\n        }\n    },\n    success: (message, data)=>{\n        if (true) {\n            console.log(`✅ ${message}`, data ? JSON.stringify(data, null, 2) : \"\");\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/logger.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/models.ts":
/*!***************************!*\
  !*** ./app/lib/models.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Address: () => (/* binding */ Address),\n/* harmony export */   Category: () => (/* binding */ Category),\n/* harmony export */   Coupon: () => (/* binding */ Coupon),\n/* harmony export */   Enquiry: () => (/* binding */ Enquiry),\n/* harmony export */   HomepageSetting: () => (/* binding */ HomepageSetting),\n/* harmony export */   Newsletter: () => (/* binding */ Newsletter),\n/* harmony export */   Notification: () => (/* binding */ Notification),\n/* harmony export */   NotificationTemplate: () => (/* binding */ NotificationTemplate),\n/* harmony export */   Order: () => (/* binding */ Order),\n/* harmony export */   OrderItem: () => (/* binding */ OrderItem),\n/* harmony export */   Product: () => (/* binding */ Product),\n/* harmony export */   ProductCategory: () => (/* binding */ ProductCategory),\n/* harmony export */   ProductImage: () => (/* binding */ ProductImage),\n/* harmony export */   ProductVariant: () => (/* binding */ ProductVariant),\n/* harmony export */   Review: () => (/* binding */ Review),\n/* harmony export */   Testimonial: () => (/* binding */ Testimonial),\n/* harmony export */   User: () => (/* binding */ User),\n/* harmony export */   Wishlist: () => (/* binding */ Wishlist),\n/* harmony export */   WishlistItem: () => (/* binding */ WishlistItem)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst UserSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    email: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    name: String,\n    phone: String,\n    avatar: String,\n    password: String,\n    role: {\n        type: String,\n        enum: [\n            \"ADMIN\",\n            \"CUSTOMER\"\n        ],\n        default: \"CUSTOMER\"\n    },\n    emailVerified: Date,\n    resetToken: String,\n    resetTokenExpiry: Date\n}, {\n    timestamps: true,\n    collection: \"user\"\n});\nconst ProductSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: true\n    },\n    slug: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    description: String,\n    shortDescription: String,\n    comparePrice: Number,\n    costPrice: Number,\n    weight: Number,\n    dimensions: String,\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    isFeatured: {\n        type: Boolean,\n        default: false\n    },\n    metaTitle: String,\n    metaDescription: String,\n    categoryId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Category\"\n    },\n    price: Number\n}, {\n    timestamps: true,\n    collection: \"product\"\n});\nconst CategorySchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    slug: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    description: String,\n    parentId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Category\"\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    }\n}, {\n    timestamps: true,\n    collection: \"category\"\n});\nconst OrderSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    orderNumber: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    status: {\n        type: String,\n        enum: [\n            \"PENDING\",\n            \"CONFIRMED\",\n            \"PROCESSING\",\n            \"SHIPPED\",\n            \"DELIVERED\",\n            \"CANCELLED\",\n            \"REFUNDED\"\n        ],\n        default: \"PENDING\"\n    },\n    paymentStatus: {\n        type: String,\n        enum: [\n            \"PENDING\",\n            \"PAID\",\n            \"FAILED\",\n            \"REFUNDED\"\n        ],\n        default: \"PENDING\"\n    },\n    paymentMethod: String,\n    paymentId: String,\n    subtotal: {\n        type: Number,\n        required: true\n    },\n    tax: {\n        type: Number,\n        default: 0\n    },\n    shipping: {\n        type: Number,\n        default: 0\n    },\n    discount: {\n        type: Number,\n        default: 0\n    },\n    total: {\n        type: Number,\n        required: true\n    },\n    currency: {\n        type: String,\n        default: \"INR\"\n    },\n    notes: String,\n    userId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    couponCodes: [\n        String\n    ],\n    couponDiscount: {\n        type: Number,\n        default: 0\n    },\n    flashSaleDiscount: {\n        type: Number,\n        default: 0\n    },\n    estimatedDelivery: String,\n    trackingNumber: String,\n    addressId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Address\"\n    }\n}, {\n    timestamps: true,\n    collection: \"order\"\n});\nconst HomepageSettingSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    productOfTheMonthId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\"\n    },\n    bannerText: String,\n    bannerCtaText: String,\n    bannerCtaLink: String,\n    isActive: {\n        type: Boolean,\n        default: true\n    }\n}, {\n    timestamps: true,\n    collection: \"homepageSetting\"\n});\nconst TestimonialSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: true\n    },\n    content: {\n        type: String,\n        required: true\n    },\n    rating: {\n        type: Number,\n        default: 5,\n        min: 1,\n        max: 5\n    },\n    image: String,\n    position: String,\n    company: String,\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    order: {\n        type: Number,\n        default: 0\n    }\n}, {\n    timestamps: true,\n    collection: \"testimonials\"\n});\nconst ProductImageSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    productId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\",\n        required: true\n    },\n    url: {\n        type: String,\n        required: true\n    },\n    alt: String,\n    position: {\n        type: Number,\n        default: 0\n    }\n}, {\n    timestamps: true,\n    collection: \"images\"\n});\nconst ProductVariantSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    productId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\",\n        required: true\n    },\n    name: {\n        type: String,\n        required: true\n    },\n    value: {\n        type: String,\n        required: true\n    },\n    price: Number,\n    pricingMode: {\n        type: String,\n        enum: [\n            \"REPLACE\",\n            \"INCREMENT\",\n            \"FIXED\"\n        ],\n        default: \"REPLACE\"\n    }\n}, {\n    timestamps: true,\n    collection: \"variants\"\n});\nconst ReviewSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    productId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\",\n        required: true\n    },\n    userId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    rating: {\n        type: Number,\n        required: true,\n        min: 1,\n        max: 5\n    },\n    title: String,\n    comment: String,\n    isApproved: {\n        type: Boolean,\n        default: false\n    }\n}, {\n    timestamps: true,\n    collection: \"reviews\"\n});\nconst AddressSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    userId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    firstName: {\n        type: String,\n        required: true\n    },\n    lastName: {\n        type: String,\n        required: true\n    },\n    address1: {\n        type: String,\n        required: true\n    },\n    address2: String,\n    city: {\n        type: String,\n        required: true\n    },\n    state: {\n        type: String,\n        required: true\n    },\n    postalCode: {\n        type: String,\n        required: true\n    },\n    country: {\n        type: String,\n        required: true\n    },\n    phone: {\n        type: String,\n        required: true\n    },\n    isDefault: {\n        type: Boolean,\n        default: false\n    }\n}, {\n    timestamps: true,\n    collection: \"address\"\n});\nconst OrderItemSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    orderId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Order\",\n        required: true\n    },\n    productId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\",\n        required: true\n    },\n    quantity: {\n        type: Number,\n        required: true\n    },\n    price: {\n        type: Number,\n        required: true\n    },\n    total: {\n        type: Number,\n        required: true\n    }\n}, {\n    timestamps: true,\n    collection: \"items\"\n});\nconst NotificationSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    userId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    title: {\n        type: String,\n        required: true\n    },\n    message: {\n        type: String,\n        required: true\n    },\n    type: {\n        type: String,\n        enum: [\n            \"ORDER\",\n            \"PRODUCT\",\n            \"SYSTEM\",\n            \"MARKETING\"\n        ],\n        required: true\n    },\n    isRead: {\n        type: Boolean,\n        default: false\n    },\n    data: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.Mixed\n}, {\n    timestamps: true,\n    collection: \"notifications\"\n});\nconst CouponSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    code: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    name: {\n        type: String,\n        required: true\n    },\n    description: String,\n    type: {\n        type: String,\n        enum: [\n            \"PERCENTAGE\",\n            \"FIXED\"\n        ],\n        required: true\n    },\n    value: {\n        type: Number,\n        required: true\n    },\n    minimumAmount: Number,\n    maximumDiscount: Number,\n    usageLimit: Number,\n    usedCount: {\n        type: Number,\n        default: 0\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    validFrom: {\n        type: Date,\n        required: true\n    },\n    validUntil: Date,\n    userUsageLimit: Number,\n    isStackable: Boolean,\n    showInModule: Boolean,\n    applicableProducts: [\n        {\n            type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n            ref: \"Product\"\n        }\n    ],\n    applicableCategories: [\n        {\n            type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n            ref: \"Category\"\n        }\n    ],\n    excludedProducts: [\n        {\n            type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n            ref: \"Product\"\n        }\n    ],\n    excludedCategories: [\n        {\n            type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n            ref: \"Category\"\n        }\n    ],\n    customerSegments: [\n        String\n    ]\n}, {\n    timestamps: true,\n    collection: \"coupons\"\n});\nconst WishlistSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    userId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    productId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\",\n        required: true\n    }\n}, {\n    timestamps: true,\n    collection: \"wishlist\"\n});\nconst NewsletterSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    email: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    }\n}, {\n    timestamps: true,\n    collection: \"newsletter\"\n});\nconst ProductCategorySchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    productId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\",\n        required: true\n    },\n    categoryId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Category\",\n        required: true\n    }\n}, {\n    timestamps: true,\n    collection: \"productCategories\"\n});\nconst WishlistItemSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    userId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    productId: {\n        type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n        ref: \"Product\",\n        required: true\n    }\n}, {\n    timestamps: true,\n    collection: \"wishlistItems\"\n});\n// Create and export models\nconst User = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).User || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"User\", UserSchema);\nconst Product = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Product || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Product\", ProductSchema);\nconst Category = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Category || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Category\", CategorySchema);\nconst Order = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Order || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Order\", OrderSchema);\nconst HomepageSetting = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).HomepageSetting || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"HomepageSetting\", HomepageSettingSchema);\nconst Testimonial = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Testimonial || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Testimonial\", TestimonialSchema);\nconst ProductImage = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).ProductImage || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"ProductImage\", ProductImageSchema);\nconst ProductVariant = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).ProductVariant || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"ProductVariant\", ProductVariantSchema);\nconst Review = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Review || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Review\", ReviewSchema);\nconst Address = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Address || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Address\", AddressSchema);\nconst OrderItem = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).OrderItem || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"OrderItem\", OrderItemSchema);\nconst Notification = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Notification || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Notification\", NotificationSchema);\nconst Coupon = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Coupon || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Coupon\", CouponSchema);\nconst Wishlist = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Wishlist || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Wishlist\", WishlistSchema);\nconst Newsletter = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Newsletter || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Newsletter\", NewsletterSchema);\nconst ProductCategory = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).ProductCategory || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"ProductCategory\", ProductCategorySchema);\nconst WishlistItem = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).WishlistItem || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"WishlistItem\", WishlistItemSchema);\nconst NotificationTemplateSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    type: {\n        type: String,\n        required: true\n    },\n    title: {\n        type: String,\n        required: true\n    },\n    message: {\n        type: String,\n        required: true\n    },\n    emailSubject: String,\n    emailTemplate: String,\n    isActive: {\n        type: Boolean,\n        default: true\n    }\n}, {\n    timestamps: true,\n    collection: \"notification_templates\"\n});\nconst NotificationTemplate = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).NotificationTemplate || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"NotificationTemplate\", NotificationTemplateSchema);\nconst EnquirySchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: true\n    },\n    email: {\n        type: String,\n        required: true\n    },\n    subject: {\n        type: String,\n        required: true\n    },\n    message: {\n        type: String,\n        required: true\n    },\n    status: {\n        type: String,\n        default: \"NEW\"\n    },\n    notes: String\n}, {\n    timestamps: true,\n    collection: \"enquiry\"\n});\nconst Enquiry = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Enquiry || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Enquiry\", EnquirySchema);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/models.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/mongoose.ts":
/*!*****************************!*\
  !*** ./app/lib/mongoose.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error(\"Please define the MONGODB_URI environment variable inside .env\");\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/mongoose.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();