import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { getServerSession } from "next-auth";
import { authOptions } from "../../lib/auth";
import connectDB from "../../lib/mongoose";
import { Order, OrderItem, Address, Product, User } from "../../lib/models";
import {
  handleApiError,
  ValidationError,
  AuthenticationError,
  AppError,
  asyncHandler,
} from "../../lib/errors";
import { logger } from "../../lib/logger";
import { withRateLimit, generalLimiter } from "../../lib/rate-limit";
import { orderNotifications } from "../../lib/notification-helpers";
import crypto from "crypto";

const getOrdersSchema = z.object({
  page: z.string().optional().default("1"),
  limit: z.string().optional().default("10"),
  status: z
    .enum([
      "PENDING",
      "CONFIRMED",
      "PROCESSING",
      "SHIPPED",
      "DELIVERED",
      "CANCELLED",
      "REFUNDED",
    ])
    .optional(),
  paymentStatus: z.enum(["PENDING", "PAID", "FAILED", "REFUNDED"]).optional(),
});

const createOrderSchema = z.object({
  cartItems: z.array(
    z.object({
      productId: z.string(),
      quantity: z.number().min(1),
      price: z.number().min(0),
    }),
  ),
  shippingAddress: z.object({
    firstName: z.string().min(1),
    lastName: z.string().min(1),
    address1: z.string().min(1),
    address2: z.string().optional(),
    city: z.string().min(1),
    state: z.string().min(1),
    postalCode: z.string().min(1),
    country: z.string().min(1),
    phone: z.string().min(1),
  }),
  totalAmount: z.number().min(1),
  paymentMethod: z.enum(["ONLINE", "COD"]),
  appliedCoupons: z
    .array(
      z.object({
        coupon: z.object({
          id: z.string(),
          code: z.string(),
          name: z.string(),
        }),
        discountAmount: z.number().min(0),
      }),
    )
    .optional()
    .default([]),
  flashSaleDiscount: z.number().optional().default(0),
});

// GET /api/orders - Get user's orders or all orders (admin)
export const GET = asyncHandler(async (request: NextRequest) => {
  logger.apiRequest("GET", "/api/orders");

  // Apply rate limiting
  await withRateLimit(request, generalLimiter, 30);

  // Check authentication
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    throw new AuthenticationError("Authentication required");
  }

  const { searchParams } = new URL(request.url);
  const queryParams = Object.fromEntries(searchParams.entries());
  const validatedParams = getOrdersSchema.parse(queryParams);

  const page = parseInt(validatedParams.page);
  const limit = parseInt(validatedParams.limit);
  const offset = (page - 1) * limit;

  const isAdmin = session.user.role === "ADMIN";

  try {
    await connectDB();

    // Build where clause
    const whereClause: any = {};

    // Non-admin users can only see their own orders
    if (!isAdmin) {
      whereClause.userId = session.user.id;
    }

    // Add status filters if provided
    if (validatedParams.status) {
      whereClause.status = validatedParams.status;
    }
    if (validatedParams.paymentStatus) {
      whereClause.paymentStatus = validatedParams.paymentStatus;
    }

    // Get orders with pagination
    const [orders, totalCount] = await Promise.all([
      Order.find(whereClause)
        .sort({ createdAt: -1 })
        .skip(offset)
        .limit(limit)
        .lean(),
      Order.countDocuments(whereClause),
    ]);

    // Populate related data for each order
    const ordersWithRelations = await Promise.all(
      orders.map(async (order) => {
        const [items, address, user] = await Promise.all([
          // Get order items with product details
          Promise.all(
            (
              await OrderItem.find({ orderId: String(order._id) }).lean()
            ).map(async (item) => {
              const product = await Product.findById(item.productId)
                .select("_id name slug price")
                .lean();
              return {
                ...item,
                product: product
                  ? {
                      id: String((product as any)._id),
                      name: (product as any).name,
                      slug: (product as any).slug,
                      price: (product as any).price,
                    }
                  : null,
              };
            }),
          ),
          // Get address
          Address.findOne({
            userId: order.userId,
            // In a more complex system, you'd link address by ID
          }).lean(),
          // Get user if admin
          isAdmin
            ? User.findById(order.userId).select("_id name email phone").lean()
            : null,
        ]);

        return {
          ...order,
          items,
          address,
          user: user
            ? {
                id: String((user as any)._id),
                name: (user as any).name,
                email: (user as any).email,
                phone: (user as any).phone,
              }
            : undefined,
        };
      }),
    );

    const totalPages = Math.ceil(totalCount / limit);

    logger.info("Orders retrieved successfully", {
      userId: session.user.id,
      isAdmin,
      count: orders.length,
      totalCount,
      page,
      filters: {
        status: validatedParams.status,
        paymentStatus: validatedParams.paymentStatus,
      },
    });

    return NextResponse.json({
      success: true,
      orders: ordersWithRelations,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    logger.error("Failed to retrieve orders", error as Error);
    throw error;
  }
});
// POST /api/orders - Create new order (for COD)
export const POST = asyncHandler(async (request: NextRequest) => {
  logger.apiRequest("POST", "/api/orders");

  // Apply rate limiting
  await withRateLimit(request, generalLimiter, 10);

  // Check authentication
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    throw new AuthenticationError("Authentication required");
  }

  await connectDB();

  const body = await request.json();
  const validatedData = createOrderSchema.parse(body);
  const {
    cartItems,
    shippingAddress,
    totalAmount,
    paymentMethod,
    appliedCoupons,
    flashSaleDiscount,
  } = validatedData;

  logger.info("Creating COD order", {
    userId: session.user.id,
    totalAmount,
    itemCount: cartItems.length,
    paymentMethod,
  });

  // Validate cart items and calculate total
  let calculatedTotal = 0;
  const orderItems = [];

  for (const item of cartItems) {
    // Verify product exists and price is correct
    const product = await Product.findById(item.productId);

    if (!product) {
      throw new ValidationError(`Product ${item.productId} not found`);
    }

    const itemPrice = product.price || 0;

    // Verify price matches
    if (Math.abs(itemPrice - item.price) > 0.01) {
      throw new ValidationError(`Price mismatch for product ${item.productId}`);
    }

    const itemTotal = itemPrice * item.quantity;
    calculatedTotal += itemTotal;

    orderItems.push({
      productId: item.productId,
      quantity: item.quantity,
      price: itemPrice,
      total: itemTotal,
    });
  }

  // Calculate coupon discount
  const totalDiscount = appliedCoupons.reduce(
    (sum, coupon) => sum + coupon.discountAmount,
    0,
  );
  const subtotal = calculatedTotal;
  const expectedTotal = subtotal - totalDiscount - flashSaleDiscount;

  // Add COD charges for COD orders
  let finalTotal = expectedTotal;
  if (paymentMethod === "COD") {
    finalTotal += 50; // COD charges
  }

  // Verify total amount
  if (Math.abs(finalTotal - totalAmount) > 0.01) {
    throw new ValidationError(
      `Total amount mismatch. Expected: ${finalTotal}, Received: ${totalAmount}`,
    );
  }

  // Generate order number
  const orderNumber = crypto.randomBytes(4).toString("hex").toUpperCase();

  try {
    // Check if this address already exists for the user
    let userAddress = await Address.findOne({
      userId: session.user.id,
      firstName: shippingAddress.firstName,
      lastName: shippingAddress.lastName,
      address1: shippingAddress.address1,
      city: shippingAddress.city,
      state: shippingAddress.state,
      postalCode: shippingAddress.postalCode,
      country: shippingAddress.country,
    }).lean();

    // Create or use existing address in user's address book
    if (!userAddress) {
      userAddress = await Address.create({
        userId: session.user.id,
        firstName: shippingAddress.firstName,
        lastName: shippingAddress.lastName,
        address1: shippingAddress.address1,
        address2: shippingAddress.address2,
        city: shippingAddress.city,
        state: shippingAddress.state,
        postalCode: shippingAddress.postalCode,
        country: shippingAddress.country,
        phone: shippingAddress.phone,
      });
    }

    // Create order in database
    const order = await Order.create({
      orderNumber: orderNumber,
      userId: session.user.id,
      status: "CONFIRMED", // COD orders are confirmed immediately
      paymentStatus: "PENDING", // Will be marked as PAID when delivered
      paymentMethod: paymentMethod,
      subtotal: subtotal,
      couponDiscount: totalDiscount,
      flashSaleDiscount: flashSaleDiscount,
      total: finalTotal,
      currency: "INR",
      notes: `Order created via ${paymentMethod}${paymentMethod === "COD" ? " with ₹50 COD charges" : ""}${appliedCoupons.length > 0 ? ` | Coupons: ${appliedCoupons.map((c) => c.coupon.code).join(", ")}` : ""}`,
      addressId: userAddress ? String((userAddress as any)._id) : null,
    });

    // Create order items
    await OrderItem.insertMany(
      orderItems.map((item) => ({
        orderId: String((order as any)._id),
        productId: item.productId,
        quantity: item.quantity,
        price: item.price,
        total: item.total,
      })),
    );

    // Create coupon usage records
    if (appliedCoupons.length > 0) {
      // You might have to do something like storing these in another collection
      // or embedding them within order if needed
    }

    logger.info("COD order created successfully", {
      orderId: String((order as any)._id),
      orderNumber: order.orderNumber,
      amount: finalTotal,
      userId: session.user.id,
      paymentMethod: paymentMethod,
    });

    // Send order placed notification
    try {
      await orderNotifications.orderPlaced(session.user.id, {
        orderId: String((order as any)._id),
        orderNumber: order.orderNumber,
        total: finalTotal,
        currency: "INR",
        itemCount: cartItems.length,
      });

      logger.info("Order placed notification sent", {
        orderId: String((order as any)._id),
        userId: session.user.id,
      });
    } catch (notificationError) {
      logger.error(
        "Failed to send order placed notification",
        notificationError as Error,
      );
      // Don't fail the order creation if notification fails
    }

    return NextResponse.json({
      success: true,
      order: {
        id: String((order as any)._id),
        orderNumber: order.orderNumber,
        total: finalTotal,
        currency: "INR",
      },
    });
  } catch (error) {
    logger.error("Failed to create COD order", error as Error);
    throw new AppError("Failed to create order", 500);
  }
});
