export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: Record<string, any>;
  error?: Error;
  userId?: string;
  requestId?: string;
}

class Logger {
  private logLevel: LogLevel;
  private isDevelopment: boolean;

  constructor() {
    this.isDevelopment = process.env.NODE_ENV === "development";
    // Reduce verbosity in development - only show warnings and errors
    this.logLevel = this.isDevelopment ? LogLevel.WARN : LogLevel.INFO;
  }

  private shouldLog(level: LogLevel): boolean {
    return level <= this.logLevel;
  }

  private formatMessage(entry: LogEntry): string {
    const { timestamp, level, message, context, error, userId, requestId } =
      entry;
    const levelName = LogLevel[level];

    let formatted = `[${timestamp}] ${levelName}: ${message}`;

    if (userId) {
      formatted += ` | User: ${userId}`;
    }

    if (requestId) {
      formatted += ` | Request: ${requestId}`;
    }

    if (context && Object.keys(context).length > 0) {
      formatted += ` | Context: ${JSON.stringify(context)}`;
    }

    if (error) {
      formatted += ` | Error: ${error.message}`;
      if (this.isDevelopment && error.stack) {
        formatted += `\nStack: ${error.stack}`;
      }
    }

    return formatted;
  }

  private log(
    level: LogLevel,
    message: string,
    context?: Record<string, any>,
    error?: Error,
  ): void {
    if (!this.shouldLog(level)) return;

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      error,
    };

    const formatted = this.formatMessage(entry);

    // In development, use console methods for better formatting
    if (this.isDevelopment) {
      switch (level) {
        case LogLevel.ERROR:
          console.error(formatted);
          break;
        case LogLevel.WARN:
          console.warn(formatted);
          break;
        case LogLevel.INFO:
          console.info(formatted);
          break;
        case LogLevel.DEBUG:
          console.debug(formatted);
          break;
      }
    } else {
      // In production, use structured logging (JSON format)
      console.log(JSON.stringify(entry));
    }
  }

  error(message: string, error?: Error, context?: Record<string, any>): void {
    this.log(LogLevel.ERROR, message, context, error);
  }

  warn(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, context);
  }

  info(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, context);
  }

  debug(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.DEBUG, message, context);
  }

  // API-specific logging methods
  apiRequest(
    method: string,
    path: string,
    userId?: string,
    context?: Record<string, any>,
  ): void {
    this.info(`API ${method} ${path}`, {
      ...context,
      userId,
      type: "api_request",
    });
  }

  apiResponse(
    method: string,
    path: string,
    statusCode: number,
    duration?: number,
    context?: Record<string, any>,
  ): void {
    this.info(`API ${method} ${path} - ${statusCode}`, {
      ...context,
      statusCode,
      duration,
      type: "api_response",
    });
  }

  apiError(
    method: string,
    path: string,
    error: Error,
    userId?: string,
    context?: Record<string, any>,
  ): void {
    this.error(`API ${method} ${path} failed`, error, {
      ...context,
      userId,
      type: "api_error",
    });
  }

  // Authentication logging
  authSuccess(
    userId: string,
    method: string,
    context?: Record<string, any>,
  ): void {
    this.info(`Authentication successful`, {
      ...context,
      userId,
      method,
      type: "auth_success",
    });
  }

  authFailure(
    email: string,
    method: string,
    reason: string,
    context?: Record<string, any>,
  ): void {
    this.warn(`Authentication failed`, {
      ...context,
      email,
      method,
      reason,
      type: "auth_failure",
    });
  }

  // Database logging
  dbQuery(
    operation: string,
    table: string,
    duration?: number,
    context?: Record<string, any>,
  ): void {
    this.debug(`DB ${operation} on ${table}`, {
      ...context,
      operation,
      table,
      duration,
      type: "db_query",
    });
  }

  dbError(
    operation: string,
    table: string,
    error: Error,
    context?: Record<string, any>,
  ): void {
    this.error(`DB ${operation} on ${table} failed`, error, {
      ...context,
      operation,
      table,
      type: "db_error",
    });
  }

  // Security logging
  securityEvent(
    event: string,
    severity: "low" | "medium" | "high",
    context?: Record<string, any>,
  ): void {
    const level =
      severity === "high"
        ? LogLevel.ERROR
        : severity === "medium"
          ? LogLevel.WARN
          : LogLevel.INFO;
    this.log(level, `Security event: ${event}`, {
      ...context,
      severity,
      type: "security_event",
    });
  }

  // Rate limiting logging
  rateLimitHit(
    identifier: string,
    limit: number,
    window: string,
    context?: Record<string, any>,
  ): void {
    this.warn(`Rate limit exceeded`, {
      ...context,
      identifier,
      limit,
      window,
      type: "rate_limit",
    });
  }

  // Email logging
  emailSent(
    to: string,
    subject: string,
    template?: string,
    context?: Record<string, any>,
  ): void {
    this.info(`Email sent`, {
      ...context,
      to,
      subject,
      template,
      type: "email_sent",
    });
  }

  emailError(
    to: string,
    subject: string,
    error: Error,
    context?: Record<string, any>,
  ): void {
    this.error(`Email failed to send`, error, {
      ...context,
      to,
      subject,
      type: "email_error",
    });
  }

  // Performance logging
  performance(
    operation: string,
    duration: number,
    context?: Record<string, any>,
  ): void {
    const level = duration > 5000 ? LogLevel.WARN : LogLevel.DEBUG;
    this.log(level, `Performance: ${operation} took ${duration}ms`, {
      ...context,
      operation,
      duration,
      type: "performance",
    });
  }
}

// Create singleton instance
export const logger = new Logger();

// Request logging middleware helper
export function createRequestLogger(req: Request) {
  const requestId = crypto.randomUUID();
  const startTime = Date.now();

  return {
    requestId,
    log: (message: string, context?: Record<string, any>) => {
      logger.info(message, { ...context, requestId });
    },
    error: (message: string, error?: Error, context?: Record<string, any>) => {
      logger.error(message, error, { ...context, requestId });
    },
    end: (statusCode: number) => {
      const duration = Date.now() - startTime;
      logger.apiResponse(
        req.method || "UNKNOWN",
        new URL(req.url || "").pathname,
        statusCode,
        duration,
        { requestId },
      );
    },
  };
}

// Development-only logging helpers
export const devLog = {
  info: (message: string, data?: any) => {
    if (process.env.NODE_ENV === "development") {
      console.log(`🔍 ${message}`, data ? JSON.stringify(data, null, 2) : "");
    }
  },
  error: (message: string, error?: any) => {
    if (process.env.NODE_ENV === "development") {
      console.error(`❌ ${message}`, error);
    }
  },
  warn: (message: string, data?: any) => {
    if (process.env.NODE_ENV === "development") {
      console.warn(`⚠️ ${message}`, data ? JSON.stringify(data, null, 2) : "");
    }
  },
  success: (message: string, data?: any) => {
    if (process.env.NODE_ENV === "development") {
      console.log(`✅ ${message}`, data ? JSON.stringify(data, null, 2) : "");
    }
  },
};
