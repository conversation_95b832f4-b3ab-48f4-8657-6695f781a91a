import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getToken } from "next-auth/jwt";

// Rate limiting configuration
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

// Clean up old entries every 5 minutes
setInterval(
  () => {
    const now = Date.now();
    rateLimitMap.forEach((value, key) => {
      if (value.resetTime < now) {
        rateLimitMap.delete(key);
      }
    });
  },
  5 * 60 * 1000,
);

function getClientIdentifier(request: NextRequest): string {
  const forwarded = request.headers.get("x-forwarded-for");
  const realIP = request.headers.get("x-real-ip");
  const ip = forwarded ? forwarded.split(",")[0].trim() : realIP || "unknown";

  // Use a combination of IP and user agent for better identification
  const userAgent = request.headers.get("user-agent") || "unknown";
  return `${ip}:${userAgent}`;
}

function checkRateLimit(
  identifier: string,
  limit: number,
  windowMs: number,
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const record = rateLimitMap.get(identifier);

  if (!record || record.resetTime < now) {
    // Create new record
    rateLimitMap.set(identifier, {
      count: 1,
      resetTime: now + windowMs,
    });
    return { allowed: true, remaining: limit - 1, resetTime: now + windowMs };
  }

  if (record.count >= limit) {
    return { allowed: false, remaining: 0, resetTime: record.resetTime };
  }

  record.count++;
  return {
    allowed: true,
    remaining: limit - record.count,
    resetTime: record.resetTime,
  };
}

// Define rate limits for different endpoint patterns
const rateLimitRules = [
  // Auth endpoints - strict limits
  {
    pattern: /^\/api\/auth\/(register|login|forgot-password)/,
    limit: 5,
    windowMs: 15 * 60 * 1000,
  }, // 5 requests per 15 minutes
  {
    pattern: /^\/api\/auth\/reset-password/,
    limit: 3,
    windowMs: 60 * 60 * 1000,
  }, // 3 requests per hour

  // Payment endpoints - moderate limits
  { pattern: /^\/api\/payments/, limit: 10, windowMs: 60 * 1000 }, // 10 requests per minute

  // File upload - strict limits
  { pattern: /^\/api\/media\/upload/, limit: 5, windowMs: 5 * 60 * 1000 }, // 5 uploads per 5 minutes

  // Admin endpoints - moderate limits for authenticated admins
  { pattern: /^\/api\/admin/, limit: 100, windowMs: 60 * 1000 }, // 100 requests per minute

  // Product reviews - prevent spam
  {
    pattern: /^\/api\/products\/.*\/reviews$/,
    method: "POST",
    limit: 3,
    windowMs: 60 * 60 * 1000,
  }, // 3 reviews per hour

  // Newsletter subscription
  {
    pattern: /^\/api\/newsletter/,
    method: "POST",
    limit: 2,
    windowMs: 24 * 60 * 60 * 1000,
  }, // 2 per day

  // General API endpoints - generous limits
  { pattern: /^\/api\//, limit: 60, windowMs: 60 * 1000 }, // 60 requests per minute
];

export async function middleware(request: NextRequest) {
  // Only apply to API routes
  if (!request.nextUrl.pathname.startsWith("/api/")) {
    return NextResponse.next();
  }

  // Skip rate limiting for static assets and health checks
  if (request.nextUrl.pathname === "/api/health") {
    return NextResponse.next();
  }

  const identifier = getClientIdentifier(request);
  const method = request.method;
  const pathname = request.nextUrl.pathname;

  // Find applicable rate limit rule
  const rule = rateLimitRules.find((r) => {
    const patternMatch = r.pattern.test(pathname);
    const methodMatch = !r.method || r.method === method;
    return patternMatch && methodMatch;
  });

  if (rule) {
    const { allowed, remaining, resetTime } = checkRateLimit(
      `${identifier}:${rule.pattern}`,
      rule.limit,
      rule.windowMs,
    );

    // Add rate limit headers
    const response = allowed
      ? NextResponse.next()
      : NextResponse.json(
          { error: "Too many requests. Please try again later." },
          { status: 429 },
        );

    response.headers.set("X-RateLimit-Limit", rule.limit.toString());
    response.headers.set("X-RateLimit-Remaining", remaining.toString());
    response.headers.set(
      "X-RateLimit-Reset",
      new Date(resetTime).toISOString(),
    );

    if (!allowed) {
      response.headers.set(
        "Retry-After",
        Math.ceil((resetTime - Date.now()) / 1000).toString(),
      );
    }

    return response;
  }

  // Add security headers to all API responses
  const response = NextResponse.next();

  // Security headers
  response.headers.set("X-Content-Type-Options", "nosniff");
  response.headers.set("X-Frame-Options", "DENY");
  response.headers.set("X-XSS-Protection", "1; mode=block");
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");

  // CORS headers for API
  if (request.headers.get("origin")) {
    const origin = request.headers.get("origin")!;
    const allowedOrigins = [
      process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
      "https://herbalicious.in",
      "https://www.herbalicious.in",
    ];

    if (allowedOrigins.includes(origin)) {
      response.headers.set("Access-Control-Allow-Origin", origin);
      response.headers.set("Access-Control-Allow-Credentials", "true");
      response.headers.set(
        "Access-Control-Allow-Methods",
        "GET, POST, PUT, DELETE, OPTIONS",
      );
      response.headers.set(
        "Access-Control-Allow-Headers",
        "Content-Type, Authorization",
      );
    }
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!_next/static|_next/image|favicon.ico|public).*)",
  ],
};
