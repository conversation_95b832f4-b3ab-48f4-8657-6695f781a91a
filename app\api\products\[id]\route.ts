import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/app/lib/mongoose";
import { Types } from "mongoose";
import { Product, Category, ProductImage, ProductVariant, Review, User } from "@/app/lib/models";

// GET /api/products/[id] - Get a specific product
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    await connectDB();

    // Try to find by ID first (lean for perf), then by slug, with explicit lean typing
    type LeanProduct = {
      _id: Types.ObjectId;
      categoryId?: Types.ObjectId | null;
      name?: string;
      slug?: string;
      description?: string;
      shortDescription?: string;
      price?: number;
      comparePrice?: number | null;
      sku?: string;
      quantity?: number;
      isFeatured?: boolean;
      isActive?: boolean;
      createdAt?: Date;
      updatedAt?: Date;
    };

    type LeanImage = {
      _id: Types.ObjectId;
      productId: Types.ObjectId;
      url: string;
      alt?: string | null;
      position?: number;
    };

    type LeanVariant = {
      _id: Types.ObjectId;
      productId: Types.ObjectId;
      name: string;
      value: string;
      price: number | null;
      pricingMode: "REPLACE" | "INCREMENT" | "FIXED";
    };

    type LeanReview = {
      _id: Types.ObjectId;
      productId: Types.ObjectId;
      userId: Types.ObjectId;
      rating: number;
      comment?: string;
      createdAt?: Date;
    };

    type LeanUser = {
      _id: Types.ObjectId;
      name?: string;
      email?: string;
    };

    const byId = Types.ObjectId.isValid(params.id)
      ? await Product.findById(new Types.ObjectId(params.id)).lean<LeanProduct | null>()
      : null;
    const product =
      byId ||
      (await Product.findOne({ slug: params.id }).lean<LeanProduct | null>());

    if (!product) {
      return NextResponse.json(
        { success: false, error: "Product not found" },
        { status: 404 },
      );
    }

    const productId = product._id;

    // Get related data
    const [category, images, variants, reviews] = await Promise.all([
      product.categoryId ? Category.findById(product.categoryId).lean<any>() : null,
      ProductImage.find({ productId }).sort({ position: 1 }).lean<LeanImage[]>(),
      ProductVariant.find({ productId }).lean<LeanVariant[]>(),
      Review.find({ productId }).sort({ createdAt: -1 }).lean<LeanReview[]>(),
    ]);

    // Get user details for reviews
    const reviewsWithUsers = await Promise.all(
      reviews.map(async (review) => {
        const user = await User.findById(review.userId)
          .select("_id name email")
          .lean<LeanUser | null>();
        return {
          ...review,
          user: user ? { _id: user._id, name: user.name, email: user.email } : null,
        };
      })
    );

    const productData = {
      ...product,
      _id: productId,
      category,
      images,
      variants,
      reviews: reviewsWithUsers,
    };

    return NextResponse.json({
      success: true,
      data: productData,
    });
  } catch (error) {
    console.error("Error fetching product:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch product" },
      { status: 500 },
    );
  }
}

// PATCH /api/products/[id] - Update a product
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    await connectDB();

    const body = await request.json();
    const {
      name,
      slug,
      description,
      shortDescription,
      price,
      comparePrice,
      sku,
      quantity,
      isFeatured,
      isActive,
      categoryId,
      images,
      variations = [],
    } = body;

    // Handle category updates
    const updateData: any = {
      ...(name && { name }),
      ...(slug && { slug }),
      ...(description && { description }),
      ...(shortDescription && { shortDescription }),
      ...(price !== undefined && { price }),
      ...(comparePrice !== undefined && { comparePrice }),
      ...(sku && { sku }),
      ...(quantity !== undefined && { quantity }),
      ...(isFeatured !== undefined && { isFeatured }),
      ...(isActive !== undefined && { isActive }),
      ...(categoryId && { categoryId }),
    };

    // Update the product
    const product = await Product.findByIdAndUpdate(
      Types.ObjectId.isValid(params.id) ? new Types.ObjectId(params.id) : params.id,
      updateData,
      { new: true }
    ).lean();

    if (!product) {
      return NextResponse.json(
        { success: false, error: "Product not found" },
        { status: 404 },
      );
    }

    // Handle images update
    if (images !== undefined) {
      // Delete existing images
      const productIdObj = Types.ObjectId.isValid(params.id)
        ? new Types.ObjectId(params.id)
        : (params.id as any);
      await ProductImage.deleteMany({ productId: productIdObj });

      // Create new images
      if (Array.isArray(images) && images.length > 0) {
        const imageData = images.map((img: any, index: number) => ({
          productId: productIdObj,
          url: img.url,
          alt: img.alt || name || "Product image",
          position: index,
        }));
        await ProductImage.insertMany(imageData);
      }
    }

    // Handle variations update
    if (variations !== undefined) {
      // Delete existing variations
      const productIdObj = Types.ObjectId.isValid(params.id)
        ? new Types.ObjectId(params.id)
        : (params.id as any);
      await ProductVariant.deleteMany({ productId: productIdObj });

      // Create new variations
      if (Array.isArray(variations) && variations.length > 0) {
        const variationData = variations.map((variation: any) => ({
          productId: productIdObj,
          name: variation.name,
          value: variation.value,
          price: variation.price ?? null,
          pricingMode: variation.pricingMode || "INCREMENT",
        }));
        await ProductVariant.insertMany(variationData);
      }
    }

    // Get updated product with related data
    // Re-declare minimal lean types in this block scope to satisfy TS
    type PatchLeanImage = { _id: any; productId: string; url: string; alt?: string | null; position?: number };
    type PatchLeanVariant = {
      _id: any;
      productId: string;
      name: string;
      value: string;
      price: number | null;
      pricingMode: "REPLACE" | "INCREMENT" | "FIXED";
    };

    const productIdObj = Types.ObjectId.isValid(params.id)
      ? new Types.ObjectId(params.id)
      : (params.id as any);

    const [category, updatedImages, updatedVariants] = await Promise.all([
      (product as any).categoryId ? Category.findById((product as any).categoryId).lean<any>() : null,
      ProductImage.find({ productId: productIdObj }).sort({ position: 1 }).lean<PatchLeanImage[]>(),
      ProductVariant.find({ productId: productIdObj }).lean<PatchLeanVariant[]>(),
    ]);

    const productData = {
      ...product,
      _id: (product as any)._id,
      category,
      images: updatedImages,
      variants: updatedVariants,
    };

    return NextResponse.json({
      success: true,
      data: productData,
      message: "Product updated successfully",
    });
  } catch (error) {
    console.error("Error updating product:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update product" },
      { status: 500 },
    );
  }
}

// PUT /api/products/[id] - Update a product (same as PATCH for compatibility)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  return PATCH(request, { params });
}

// DELETE /api/products/[id] - Delete a product
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    await connectDB();

    // Check if product exists
    const product = await Product.findById(
      Types.ObjectId.isValid(params.id) ? new Types.ObjectId(params.id) : params.id
    );
    if (!product) {
      return NextResponse.json(
        { success: false, error: "Product not found" },
        { status: 404 },
      );
    }

    // For now, always do a soft delete by setting isActive to false
    // In a real app, you might want to check for order references first
    const updatedProduct = await Product.findByIdAndUpdate(
      Types.ObjectId.isValid(params.id) ? new Types.ObjectId(params.id) : params.id,
      {
        isActive: false,
        name: `[DELETED] ${new Date().toISOString().split("T")[0]} - ${product.name}`,
      },
      { new: true }
    );

    return NextResponse.json({
      success: true,
      message: "Product has been deactivated (soft deleted). It will no longer appear in the store.",
      type: "soft_delete",
      data: updatedProduct,
    });
  } catch (error) {
    console.error("Error deleting product:", error);
    return NextResponse.json(
      { success: false, error: "Failed to delete product" },
      { status: 500 },
    );
  }
}
