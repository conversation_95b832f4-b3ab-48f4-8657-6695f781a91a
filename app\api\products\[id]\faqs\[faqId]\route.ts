import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/app/lib/mongoose";
import { Product } from "@/app/lib/models";
// Inline asyncHandler because '@/app/lib/async-handler' does not exist
const asyncHandler = <T extends (...args: any[]) => Promise<Response>>(handler: T) =>
  (async (...args: Parameters<T>) => {
    try {
      return await handler(...args);
    } catch (err) {
      console.error(err);
      return NextResponse.json({ success: false, error: "Internal Server Error" }, { status: 500 });
    }
  }) as T;

// Lean types to avoid array|object union issues
type LeanProductWithFaq = {
  _id: any;
  faqs?: Array<{
    _id: any;
    question?: string;
    answer?: string;
    position?: number;
    isActive?: boolean;
    updatedAt?: Date | string;
    createdAt?: Date | string;
  }>;
};

// GET /api/products/[id]/faqs/[faqId] - Get a specific FAQ
export const GET = asyncHandler(async (
  request: NextRequest,
  { params }: { params: { id: string; faqId: string } },
) => {
  await connectDB();

  // Fetch specific FAQ from embedded array on Product (typed lean)
  const product = await Product.findOne({ _id: params.id, "faqs._id": params.faqId })
    .select("faqs.$")
    .lean<LeanProductWithFaq | null>();
  const faq = product?.faqs?.[0];

  if (!faq) {
    return NextResponse.json(
      { success: false, error: "FAQ not found" },
      { status: 404 },
    );
  }

  return NextResponse.json({
    success: true,
    data: faq,
  });
});

// PUT /api/products/[id]/faqs/[faqId] - Update a specific FAQ
export const PUT = asyncHandler(async (
  request: NextRequest,
  { params }: { params: { id: string; faqId: string } },
) => {
  await connectDB();

  const body = await request.json();
  const { question, answer, position, isActive } = body || {};

  const update: any = {};
  if (question !== undefined) update.question = question;
  if (answer !== undefined) update.answer = answer;
  if (position !== undefined) update.position = position;
  if (isActive !== undefined) update.isActive = isActive;

  // Update the matching embedded FAQ atomically
  const updateRes = await Product.findOneAndUpdate(
    { _id: params.id, "faqs._id": params.faqId },
    {
      $set: Object.fromEntries(
        Object.entries(update).map(([k, v]) => [`faqs.$.${k}`, v]),
      ),
      $currentDate: { "faqs.$.updatedAt": true },
    },
    { new: true, projection: { "faqs.$": 1 } }
  ).lean<LeanProductWithFaq | null>();
  const faq = updateRes?.faqs?.[0];

  if (!faq) {
    return NextResponse.json(
      { success: false, error: "FAQ not found" },
      { status: 404 },
    );
  }

  return NextResponse.json({
    success: true,
    data: faq,
    message: "FAQ updated successfully",
  });
});

// DELETE /api/products/[id]/faqs/[faqId] - Delete a specific FAQ
export const DELETE = asyncHandler(async (
  request: NextRequest,
  { params }: { params: { id: string; faqId: string } },
) => {
  await connectDB();

  // Pull the embedded FAQ from the product
  const res = await Product.updateOne(
    { _id: params.id },
    { $pull: { faqs: { _id: params.faqId } } }
  );

  // For updateOne, check matchedCount/modifiedCount instead of deletedCount
  // Consider not found if nothing was modified
  // @ts-ignore - depending on mongoose version, matchedCount/modifiedCount may exist
  if (!res || (res as any).modifiedCount === 0) {
    return NextResponse.json(
      { success: false, error: "FAQ not found" },
      { status: 404 },
    );
  }

  return NextResponse.json({
    success: true,
    message: "FAQ deleted successfully",
  });
});
