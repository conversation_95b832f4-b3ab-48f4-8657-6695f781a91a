{"name": "herbalicious-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "seed": "tsx scripts/reset-and-seed.ts", "seed:ts-node": "ts-node --transpile-only scripts/reset-and-seed.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.844.0", "@aws-sdk/s3-request-presigner": "^3.844.0", "@types/dompurify": "^3.2.0", "@types/mongoose": "^5.11.97", "bcryptjs": "^3.0.2", "cuid": "^3.0.0", "dompurify": "^3.2.6", "embla-carousel-react": "^8.6.0", "isomorphic-dompurify": "^2.26.0", "jsdom": "^26.1.0", "jsonwebtoken": "^9.0.2", "lru-cache": "^11.1.0", "lucide-react": "^0.344.0", "mongodb": "^6.18.0", "mongoose": "^8.17.0", "multer": "^2.0.1", "next": "^14.0.0", "next-auth": "^4.24.11", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "razorpay": "^2.9.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "zod": "^4.0.5"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-typescript": "^7.27.1", "@types/bcryptjs": "^3.0.0", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.0.0", "@types/nodemailer": "^6.4.17", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/supertest": "^6.0.3", "autoprefixer": "^10.4.16", "babel-jest": "^30.0.5", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "jest": "^30.0.5", "postcss": "^8.4.31", "supertest": "^7.1.4", "tailwindcss": "^3.3.5", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.2.0"}}