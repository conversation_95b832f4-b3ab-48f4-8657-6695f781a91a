"use client";

import React from "react";

// Base skeleton component
const Skeleton: React.FC<{ className?: string }> = ({ className = "" }) => (
  <div className={`animate-pulse bg-gray-200 rounded ${className}`}></div>
);

// Product card skeleton
export const ProductCardSkeleton: React.FC = () => (
  <div className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
    <Skeleton className="w-full h-48 mb-4" />
    <Skeleton className="h-4 w-3/4 mb-2" />
    <Skeleton className="h-3 w-1/2 mb-3" />
    <div className="flex items-center justify-between">
      <Skeleton className="h-5 w-20" />
      <Skeleton className="h-8 w-8 rounded-full" />
    </div>
  </div>
);

// Product grid skeleton
export const ProductGridSkeleton: React.FC<{ count?: number }> = ({
  count = 8,
}) => (
  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
    {Array.from({ length: count }).map((_, index) => (
      <ProductCardSkeleton key={index} />
    ))}
  </div>
);

// Product detail skeleton
export const ProductDetailSkeleton: React.FC = () => (
  <div className="lg:grid lg:grid-cols-12 lg:gap-8">
    {/* Mobile Layout */}
    <div className="lg:hidden bg-white">
      {/* Header */}
      <div className="sticky top-16 bg-white z-30 flex items-center justify-between px-4 py-3 border-b">
        <Skeleton className="w-8 h-8 rounded-full" />
        <div className="flex items-center space-x-3">
          <Skeleton className="w-8 h-8 rounded-full" />
          <Skeleton className="w-8 h-8 rounded-full" />
        </div>
      </div>

      {/* Product Image */}
      <div className="p-4">
        <Skeleton className="w-full h-80 rounded-xl" />
      </div>

      {/* Product Info */}
      <div className="p-6">
        <Skeleton className="h-8 w-3/4 mb-2" />
        <Skeleton className="h-4 w-full mb-3" />
        <Skeleton className="h-6 w-1/3 mb-4" />

        {/* Tabs */}
        <div className="flex border-b border-gray-200 mb-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Skeleton key={i} className="h-8 w-20 mr-4" />
          ))}
        </div>

        <div className="space-y-3 mb-6">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-5/6" />
          <Skeleton className="h-4 w-4/5" />
        </div>

        {/* Add to cart */}
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <Skeleton className="w-10 h-10 rounded-lg" />
            <Skeleton className="w-10 h-6" />
            <Skeleton className="w-10 h-10 rounded-lg" />
          </div>
          <Skeleton className="flex-1 h-12 rounded-lg" />
        </div>
      </div>
    </div>

    {/* Desktop Layout */}
    <div className="hidden lg:block lg:col-span-12">
      <div className="py-8">
        <Skeleton className="h-6 w-32 mb-8" />

        <div className="grid grid-cols-2 gap-12">
          {/* Product Image */}
          <Skeleton className="w-full h-96 rounded-xl" />

          {/* Product Info */}
          <div>
            <Skeleton className="h-10 w-3/4 mb-4" />
            <Skeleton className="h-6 w-full mb-6" />
            <Skeleton className="h-8 w-1/3 mb-6" />

            <div className="flex items-center space-x-3 mb-8">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="w-5 h-5" />
              ))}
              <Skeleton className="h-4 w-24" />
            </div>

            {/* Quantity and Add to Cart */}
            <div className="flex items-center space-x-4 mb-8">
              <div className="flex items-center space-x-3">
                <Skeleton className="w-12 h-12 rounded-lg" />
                <Skeleton className="w-12 h-6" />
                <Skeleton className="w-12 h-12 rounded-lg" />
              </div>
              <Skeleton className="flex-1 h-14 rounded-lg" />
            </div>

            {/* Tabs */}
            <div className="flex border-b border-gray-200 mb-6">
              {Array.from({ length: 4 }).map((_, i) => (
                <Skeleton key={i} className="h-10 w-24 mr-6" />
              ))}
            </div>

            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-5/6" />
              <Skeleton className="h-4 w-4/5" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

// Wishlist skeleton
export const WishlistSkeleton: React.FC = () => (
  <div className="lg:grid lg:grid-cols-12 lg:gap-8">
    {/* Mobile Layout */}
    <div className="lg:hidden">
      <div className="sticky top-16 bg-white z-30 px-4 py-4 border-b">
        <div className="flex items-center space-x-4">
          <Skeleton className="w-8 h-8 rounded-full" />
          <Skeleton className="h-6 w-24" />
          <div className="ml-auto">
            <Skeleton className="h-4 w-16" />
          </div>
        </div>
      </div>

      <div className="px-4 py-6">
        <Skeleton className="w-full h-12 rounded-2xl mb-6" />

        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <div
              key={index}
              className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100"
            >
              <div className="flex space-x-4">
                <Skeleton className="w-20 h-20 rounded-xl flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between mb-2">
                    <Skeleton className="h-5 w-3/4" />
                    <Skeleton className="w-4 h-4 rounded-full" />
                  </div>
                  <Skeleton className="h-4 w-full mb-2" />
                  <div className="flex items-center space-x-2 mb-3">
                    <Skeleton className="w-4 h-4" />
                    <Skeleton className="h-4 w-8" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-5 w-16" />
                    <Skeleton className="h-8 w-24 rounded-full" />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>

    {/* Desktop Layout */}
    <div className="hidden lg:block lg:col-span-12">
      <div className="py-8">
        <Skeleton className="h-6 w-16 mb-8" />

        <div className="flex items-center justify-between mb-8">
          <Skeleton className="h-10 w-48" />
          <Skeleton className="h-6 w-24" />
        </div>

        <Skeleton className="w-48 h-12 rounded-2xl mb-8" />

        <div className="grid grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <div
              key={index}
              className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
            >
              <Skeleton className="w-full h-48 rounded-xl mb-4" />
              <Skeleton className="h-6 w-3/4 mb-2" />
              <Skeleton className="h-4 w-full mb-3" />
              <div className="flex items-center space-x-2 mb-4">
                <Skeleton className="w-4 h-4" />
                <Skeleton className="h-4 w-8" />
                <Skeleton className="h-4 w-16" />
              </div>
              <div className="flex items-center justify-between">
                <Skeleton className="h-6 w-20" />
                <Skeleton className="h-8 w-24 rounded-xl" />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>
);

// Cart skeleton
export const CartSkeleton: React.FC = () => (
  <div className="lg:grid lg:grid-cols-12 lg:gap-8">
    {/* Mobile Layout */}
    <div className="lg:hidden">
      <div className="sticky top-16 bg-white z-30 px-4 py-4 border-b">
        <div className="flex items-center space-x-4">
          <Skeleton className="w-8 h-8 rounded-full" />
          <Skeleton className="h-6 w-16" />
          <div className="ml-auto">
            <Skeleton className="h-4 w-16" />
          </div>
        </div>
      </div>

      <div className="px-4 py-6">
        <div className="space-y-4 mb-6">
          {Array.from({ length: 3 }).map((_, index) => (
            <div
              key={index}
              className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100"
            >
              <div className="flex space-x-4">
                <Skeleton className="w-20 h-20 rounded-xl flex-shrink-0" />
                <div className="flex-1">
                  <Skeleton className="h-5 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2 mb-3" />
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Skeleton className="w-8 h-8 rounded" />
                      <Skeleton className="w-8 h-4" />
                      <Skeleton className="w-8 h-8 rounded" />
                    </div>
                    <Skeleton className="h-5 w-16" />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Summary */}
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <Skeleton className="h-6 w-32 mb-4" />
          <div className="space-y-3 mb-6">
            <div className="flex justify-between">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-20" />
            </div>
            <div className="flex justify-between">
              <Skeleton className="h-4 w-12" />
              <Skeleton className="h-4 w-16" />
            </div>
            <div className="flex justify-between border-t pt-3">
              <Skeleton className="h-5 w-16" />
              <Skeleton className="h-5 w-24" />
            </div>
          </div>
          <Skeleton className="w-full h-12 rounded-xl" />
        </div>
      </div>
    </div>

    {/* Desktop Layout */}
    <div className="hidden lg:block lg:col-span-12">
      <div className="py-8">
        <Skeleton className="h-6 w-16 mb-8" />

        <div className="flex items-center justify-between mb-8">
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-6 w-24" />
        </div>

        <div className="grid grid-cols-3 gap-8">
          <div className="col-span-2 space-y-6">
            {Array.from({ length: 3 }).map((_, index) => (
              <div
                key={index}
                className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100"
              >
                <div className="flex space-x-6">
                  <Skeleton className="w-32 h-32 rounded-xl flex-shrink-0" />
                  <div className="flex-1">
                    <Skeleton className="h-6 w-3/4 mb-3" />
                    <Skeleton className="h-4 w-1/2 mb-4" />
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Skeleton className="w-10 h-10 rounded" />
                        <Skeleton className="w-10 h-5" />
                        <Skeleton className="w-10 h-10 rounded" />
                      </div>
                      <Skeleton className="h-6 w-20" />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="col-span-1">
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 sticky top-24">
              <Skeleton className="h-6 w-32 mb-6" />
              <div className="space-y-4 mb-6">
                <div className="flex justify-between">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-20" />
                </div>
                <div className="flex justify-between">
                  <Skeleton className="h-4 w-12" />
                  <Skeleton className="h-4 w-16" />
                </div>
                <div className="flex justify-between border-t pt-4">
                  <Skeleton className="h-5 w-16" />
                  <Skeleton className="h-5 w-24" />
                </div>
              </div>
              <Skeleton className="w-full h-12 rounded-xl" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

// Shop page skeleton
export const ShopSkeleton: React.FC = () => (
  <div className="min-h-screen bg-gray-50">
    {/* Mobile Layout */}
    <div className="lg:hidden">
      {/* Sticky Header */}
      <div className="sticky top-0 z-40 bg-white shadow-sm">
        {/* Main Header */}
        <div className="px-4 py-4">
          <div className="flex items-center justify-between mb-3">
            <Skeleton className="h-8 w-16" />
            <div className="flex items-center space-x-3">
              <Skeleton className="w-10 h-10 rounded-full" />
              <Skeleton className="w-20 h-10 rounded-lg" />
            </div>
          </div>

          {/* Quick Stats */}
          <div className="flex items-center justify-between text-sm">
            <Skeleton className="h-4 w-32" />
            <div className="flex items-center space-x-2">
              <Skeleton className="w-8 h-8 rounded" />
              <Skeleton className="w-8 h-8 rounded" />
            </div>
          </div>
        </div>
      </div>

      {/* Products Grid */}
      <div className="px-4 py-6">
        <div className="grid grid-cols-2 gap-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <div
              key={index}
              className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden"
            >
              <Skeleton className="w-full h-48" />
              <div className="p-4">
                <Skeleton className="h-4 w-3/4 mb-2" />
                <Skeleton className="h-3 w-full mb-3" />
                <div className="flex items-center justify-between">
                  <div className="flex flex-col">
                    <Skeleton className="h-5 w-16 mb-1" />
                    <Skeleton className="h-3 w-20" />
                  </div>
                  <Skeleton className="w-10 h-10 rounded-full" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>

    {/* Desktop Layout */}
    <div className="hidden lg:block">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-6 py-8">
          {/* Search, Price Range, and Sort Controls */}
          <div className="flex items-center gap-4 mb-8">
            {/* Search Bar - Made smaller */}
            <Skeleton className="h-12 w-80 rounded-xl" />

            {/* Price Range Filter - Now in the middle */}
            <div className="flex items-center gap-3 flex-1 justify-center">
              <Skeleton className="h-4 w-20" />
              <div className="flex items-center space-x-2">
                <Skeleton className="w-24 h-10 rounded-lg" />
                <Skeleton className="w-4 h-4" />
                <Skeleton className="w-24 h-10 rounded-lg" />
              </div>
            </div>

            {/* Sort and View Controls */}
            <div className="flex items-center gap-4">
              <Skeleton className="h-12 w-40 rounded-xl" />
              <Skeleton className="h-10 w-20 rounded-lg" />
            </div>
          </div>

          {/* Category Filter */}
          <div className="flex justify-center mb-8">
            <div className="flex flex-wrap gap-3 bg-white rounded-2xl p-3 shadow-sm border border-gray-100">
              {Array.from({ length: 6 }).map((_, i) => (
                <Skeleton key={i} className="h-12 w-24 rounded-xl" />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Products Section */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="flex items-center justify-between mb-6">
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-4 w-24" />
        </div>

        <div className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 12 }).map((_, index) => (
            <div
              key={index}
              className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden"
            >
              <Skeleton className="w-full h-56" />
              <div className="p-5">
                <Skeleton className="h-5 w-3/4 mb-2" />
                <Skeleton className="h-4 w-full mb-4" />
                <div className="flex items-center justify-between">
                  <div className="flex flex-col">
                    <Skeleton className="h-5 w-20 mb-1" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                  <Skeleton className="w-12 h-12 rounded-full" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>
);

// Search page skeleton
export const SearchSkeleton: React.FC = () => (
  <div className="lg:grid lg:grid-cols-12 lg:gap-8">
    {/* Mobile Layout */}
    <div className="lg:hidden">
      <div className="px-4 py-6 bg-white sticky top-16 z-30 border-b">
        <div className="relative mb-4">
          <Skeleton className="w-full h-12 rounded-2xl" />
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Skeleton className="h-10 w-20 rounded-full" />
            <Skeleton className="h-8 w-16 rounded-full" />
          </div>
          <Skeleton className="h-4 w-16" />
        </div>
      </div>

      <div className="px-4 py-6">
        <div className="grid grid-cols-2 gap-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <ProductCardSkeleton key={index} />
          ))}
        </div>
      </div>
    </div>

    {/* Desktop Layout */}
    <div className="hidden lg:block lg:col-span-12">
      <div className="py-8">
        <div className="mb-8">
          <Skeleton className="h-10 w-64 mb-6" />
          <Skeleton className="w-full h-14 rounded-2xl max-w-2xl" />
        </div>

        <div className="grid grid-cols-4 gap-8">
          <div className="col-span-1">
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 sticky top-24">
              <Skeleton className="h-6 w-16 mb-6" />
              <div className="space-y-6">
                <div>
                  <Skeleton className="h-4 w-16 mb-3" />
                  <Skeleton className="w-full h-10 rounded-lg" />
                </div>
                <div>
                  <Skeleton className="h-4 w-20 mb-3" />
                  <Skeleton className="w-full h-20" />
                </div>
                <div>
                  <Skeleton className="h-4 w-12 mb-3" />
                  <Skeleton className="w-full h-10 rounded-lg" />
                </div>
              </div>
            </div>
          </div>

          <div className="col-span-3">
            <div className="flex items-center justify-between mb-6">
              <Skeleton className="h-8 w-48" />
              <Skeleton className="h-4 w-16" />
            </div>

            <div className="grid grid-cols-3 gap-6">
              {Array.from({ length: 9 }).map((_, index) => (
                <ProductCardSkeleton key={index} />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

// Home page skeleton
export const HomeSkeleton: React.FC = () => (
  <div>
    {/* Hero Section */}
    <div className="relative">
      <Skeleton className="w-full h-64 md:h-96" />
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center">
          <Skeleton className="h-8 md:h-12 w-64 md:w-96 mb-4 mx-auto" />
          <Skeleton className="h-4 md:h-6 w-48 md:w-72 mb-6 mx-auto" />
          <Skeleton className="h-10 md:h-12 w-32 md:w-40 mx-auto rounded-full" />
        </div>
      </div>
    </div>

    {/* Featured Products */}
    <div className="px-4 md:px-8 py-12">
      <div className="text-center mb-8">
        <Skeleton className="h-8 w-48 mx-auto mb-4" />
        <Skeleton className="h-4 w-64 mx-auto" />
      </div>
      <ProductGridSkeleton count={4} />
    </div>

    {/* Categories */}
    <div className="px-4 md:px-8 py-12 bg-gray-50">
      <div className="text-center mb-8">
        <Skeleton className="h-8 w-40 mx-auto mb-4" />
        <Skeleton className="h-4 w-56 mx-auto" />
      </div>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="text-center">
            <Skeleton className="w-24 h-24 md:w-32 md:h-32 rounded-full mx-auto mb-4" />
            <Skeleton className="h-4 w-20 mx-auto" />
          </div>
        ))}
      </div>
    </div>
  </div>
);
