import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../lib/auth";
import connectDB from "../../../lib/mongoose";
import { HomepageSetting, Coupon } from "../../../lib/models";
// CouponUsage model is not defined/exported in app/lib/models.ts.
// Count per-user coupon usage via Orders instead, using couponCodes array.
import { logger } from "../../../lib/logger";
import { CartItem, CouponValidationResult } from "../../../types";

interface ValidateRequest {
  couponCode: string;
  cartItems: CartItem[];
  userId?: string;
  subtotal: number;
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    const session = await getServerSession(authOptions);
    const { couponCode, cartItems, userId, subtotal }: ValidateRequest =
      await request.json();

    if (!couponCode || !cartItems || subtotal === undefined) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 },
      );
    }

    const result = await validateCoupon(
      couponCode,
      cartItems,
      subtotal,
      userId,
    );
    return NextResponse.json(result);
  } catch (error) {
    logger.error("Error validating coupon", error as Error);
    return NextResponse.json(
      { error: "Failed to validate coupon" },
      { status: 500 },
    );
  }
}

async function isFlashSaleActive(): Promise<boolean> {
  try {
    const latest = await HomepageSetting.find({})
      .sort({ createdAt: -1 })
      .limit(1)
      .lean();

    if (latest.length === 0) {
      return false;
    }

    const setting: any = latest[0];

    if (!setting.showFlashSale) {
      return false;
    }

    if (setting.flashSaleEndDate) {
      const now = new Date();
      const endDate = new Date(setting.flashSaleEndDate);
      if (endDate < now) {
        return false;
      }
    }

    return true;
  } catch (error) {
    logger.error("Error checking flash sale status", error as Error);
    return false;
  }
}

async function validateCoupon(
  couponCode: string,
  cartItems: CartItem[],
  subtotal: number,
  userId?: string,
): Promise<CouponValidationResult> {
  // Check if flash sale is active
  const flashSaleActive = await isFlashSaleActive();
  if (flashSaleActive) {
    return {
      isValid: false,
      discountAmount: 0,
      errorMessage: "Coupons cannot be used during flash sale periods",
    };
  }

  // Find the coupon (narrow type for lean result to avoid TS union-with-array issues)
  type LeanCoupon = {
    _id: any;
    code: string;
    name: string;
    description?: string | null;
    type: "PERCENTAGE" | "FIXED" | string;
    value: number;
    minimumAmount?: number | null;
    maximumDiscount?: number | null;
    usageLimit?: number | null;
    userUsageLimit?: number | null;
    isActive: boolean;
    isStackable?: boolean;
    showInModule?: boolean;
    validFrom: Date;
    validUntil?: Date | null;
    applicableProducts?: string[];
    applicableCategories?: string[];
    excludedProducts?: string[];
    excludedCategories?: string[];
    customerSegments?: string[];
    createdAt: Date;
    updatedAt: Date;
    // Extended fields used by special coupon types (optional)
    discountType?: "PERCENTAGE" | "FIXED_AMOUNT" | "FREE_SHIPPING" | "BUY_X_GET_Y";
    discountValue?: number;
    buyQuantity?: number;
    getQuantity?: number;
    buyProducts?: string[];
    buyCategories?: string[];
    getProducts?: string[];
    getCategories?: string[];
    maxApplications?: number;
    discountApplication?: "FREE" | "PERCENTAGE" | "FIXED_AMOUNT";
    getDiscountValue?: number;
  };

  const coupon = (await Coupon.findOne({ code: couponCode.toUpperCase() }).lean()) as LeanCoupon | null;

  if (!coupon) {
    return {
      isValid: false,
      discountAmount: 0,
      errorMessage: "Coupon code not found",
    };
  }

  // Check if coupon is active
  if (!coupon.isActive) {
    return {
      isValid: false,
      discountAmount: 0,
      errorMessage: "This coupon is no longer active",
    };
  }

  // Check validity dates
  const now = new Date();
  if (coupon.validFrom > now) {
    return {
      isValid: false,
      discountAmount: 0,
      errorMessage: "This coupon is not yet valid",
    };
  }

  if (coupon.validUntil && coupon.validUntil < now) {
    return {
      isValid: false,
      discountAmount: 0,
      errorMessage: "This coupon has expired",
    };
  }

  // Check usage limits (usedCount vs usageLimit)
  if (coupon.usageLimit && (coupon as any).usedCount >= coupon.usageLimit) {
    return {
      isValid: false,
      discountAmount: 0,
      errorMessage: "This coupon has reached its usage limit",
    };
  }

  // Check user usage limit
  if (userId && coupon.userUsageLimit) {
    // Fallback: approximate user usage by counting orders containing this coupon code.
    // This assumes orders store applied codes in 'couponCodes' string[].
    const { Order } = await import("../../../lib/models");
    const userUsageCount = await Order.countDocuments({
      userId,
      couponCodes: { $in: [coupon.code] },
    });
    if (userUsageCount >= coupon.userUsageLimit) {
      return {
        isValid: false,
        discountAmount: 0,
        errorMessage:
          "You have already used this coupon the maximum number of times",
      };
    }
  }

  // Check minimum amount
  if (coupon.minimumAmount && subtotal < coupon.minimumAmount) {
    return {
      isValid: false,
      discountAmount: 0,
      errorMessage: `Minimum order amount of ₹${coupon.minimumAmount} required`,
    };
  }

  // Check product/category applicability
  const applicabilityCheck = checkApplicability(coupon as any, cartItems);
  if (!applicabilityCheck.isApplicable) {
    return {
      isValid: false,
      discountAmount: 0,
      errorMessage: applicabilityCheck.message,
    };
  }

  // Calculate discount
  const discountAmount = calculateDiscount(
    coupon as any,
    cartItems,
    subtotal,
    applicabilityCheck.applicableAmount,
  );

  // Normalize to the app/types Coupon expected shape
  const normalizedCoupon: any = {
    id: String(coupon._id),
    code: coupon.code,
    name: coupon.name,
    description: coupon.description || undefined,
    type: coupon.type,
    value: coupon.value,
    minimumAmount: coupon.minimumAmount ?? undefined,
    maximumDiscount: coupon.maximumDiscount ?? undefined,
    usageLimit: coupon.usageLimit ?? undefined,
    // usageCount not tracked in model; default to 0 for contract compatibility
    usageCount: (coupon as any).usedCount ?? 0,
    userUsageLimit: coupon.userUsageLimit ?? undefined,
    isActive: coupon.isActive,
    isStackable: coupon.isStackable ?? undefined,
    showInModule: coupon.showInModule ?? undefined,
    validFrom: new Date(coupon.validFrom).toISOString(),
    validUntil: coupon.validUntil ? new Date(coupon.validUntil).toISOString() : undefined,
    applicableProducts: coupon.applicableProducts ?? [],
    applicableCategories: coupon.applicableCategories ?? [],
    excludedProducts: coupon.excludedProducts ?? [],
    excludedCategories: coupon.excludedCategories ?? [],
    customerSegments: coupon.customerSegments ?? [],
    createdAt: new Date(coupon.createdAt).toISOString(),
    updatedAt: new Date(coupon.updatedAt).toISOString(),
    // Extended fields (optional)
    discountType: (coupon as any).discountType,
    discountValue: (coupon as any).discountValue,
    buyQuantity: (coupon as any).buyQuantity,
    getQuantity: (coupon as any).getQuantity,
    buyProducts: (coupon as any).buyProducts,
    buyCategories: (coupon as any).buyCategories,
    getProducts: (coupon as any).getProducts,
    getCategories: (coupon as any).getCategories,
    maxApplications: (coupon as any).maxApplications,
    discountApplication: (coupon as any).discountApplication,
    getDiscountValue: (coupon as any).getDiscountValue,
  };

  return {
    isValid: true,
    discountAmount,
    coupon: normalizedCoupon,
  };
}

function checkApplicability(coupon: any, cartItems: CartItem[]) {
  const productIds = cartItems.map((item) => item.product.id);
  const categoryIds = cartItems.flatMap(
    (item) => item.product.categories?.map((cat) => cat.id) || [],
  );

  // Check excluded products
  if (coupon.excludedProducts.length > 0) {
    const hasExcludedProduct = productIds.some((id) =>
      coupon.excludedProducts.includes(id),
    );
    if (hasExcludedProduct) {
      return {
        isApplicable: false,
        message: "This coupon cannot be applied to some items in your cart",
        applicableAmount: 0,
      };
    }
  }

  // Check excluded categories
  if (coupon.excludedCategories.length > 0) {
    const hasExcludedCategory = categoryIds.some((id) =>
      coupon.excludedCategories.includes(id),
    );
    if (hasExcludedCategory) {
      return {
        isApplicable: false,
        message:
          "This coupon cannot be applied to some categories in your cart",
        applicableAmount: 0,
      };
    }
  }

  let applicableAmount = 0;

  switch (coupon.type) {
    case "STORE_WIDE":
      applicableAmount = cartItems.reduce(
        (sum, item) => sum + item.product.price * item.quantity,
        0,
      );
      break;

    case "PRODUCT_SPECIFIC":
      if (coupon.applicableProducts.length === 0) {
        return {
          isApplicable: false,
          message: "No applicable products specified for this coupon",
          applicableAmount: 0,
        };
      }

      const applicableItems = cartItems.filter((item) =>
        coupon.applicableProducts.includes(item.product.id),
      );

      if (applicableItems.length === 0) {
        return {
          isApplicable: false,
          message: "This coupon is not applicable to any items in your cart",
          applicableAmount: 0,
        };
      }

      applicableAmount = applicableItems.reduce(
        (sum, item) => sum + item.product.price * item.quantity,
        0,
      );
      break;

    case "CATEGORY_SPECIFIC":
      if (coupon.applicableCategories.length === 0) {
        return {
          isApplicable: false,
          message: "No applicable categories specified for this coupon",
          applicableAmount: 0,
        };
      }

      const categoryApplicableItems = cartItems.filter((item) =>
        item.product.categories?.some((cat) =>
          coupon.applicableCategories.includes(cat.id),
        ),
      );

      if (categoryApplicableItems.length === 0) {
        return {
          isApplicable: false,
          message:
            "This coupon is not applicable to any categories in your cart",
          applicableAmount: 0,
        };
      }

      applicableAmount = categoryApplicableItems.reduce(
        (sum, item) => sum + item.product.price * item.quantity,
        0,
      );
      break;

    case "BUY_X_GET_Y":
      // For Buy X Get Y, we need to check if the cart has the required buy items
      const buyProductIds = coupon.buyProducts || [];
      const buyCategoryIds = coupon.buyCategories || [];

      // Check if cart has required buy items
      const buyItems = cartItems.filter((item) => {
        const inBuyProducts = buyProductIds.includes(item.product.id);
        const inBuyCategories =
          item.product.categories?.some((cat) =>
            buyCategoryIds.includes(cat.id),
          ) || false;
        return inBuyProducts || inBuyCategories;
      });

      const totalBuyQuantity = buyItems.reduce(
        (sum, item) => sum + item.quantity,
        0,
      );

      if (totalBuyQuantity < (coupon.buyQuantity || 1)) {
        return {
          isApplicable: false,
          message: `You need to buy at least ${coupon.buyQuantity} qualifying items to use this coupon`,
          applicableAmount: 0,
        };
      }

      // For Buy X Get Y, the applicable amount is the total cart value
      applicableAmount = cartItems.reduce(
        (sum, item) => sum + item.product.price * item.quantity,
        0,
      );
      break;

    default:
      applicableAmount = cartItems.reduce(
        (sum, item) => sum + item.product.price * item.quantity,
        0,
      );
  }

  return {
    isApplicable: true,
    message: "",
    applicableAmount,
  };
}

function calculateDiscount(
  coupon: any,
  cartItems: CartItem[],
  subtotal: number,
  applicableAmount: number,
): number {
  let discount = 0;

  // If model uses simple `type` + `value`, map into expected behavior
  if (!coupon.discountType) {
    // Interpret type: "PERCENTAGE" | "FIXED"
    if (coupon.type === "PERCENTAGE") {
      discount = (applicableAmount * (coupon.value || 0)) / 100;
    } else if (coupon.type === "FIXED") {
      discount = Math.min(coupon.value || 0, applicableAmount);
    }
  } else {
    switch (coupon.discountType) {
      case "PERCENTAGE":
        discount = (applicableAmount * (coupon.discountValue || 0)) / 100;
        break;
      case "FIXED_AMOUNT":
        discount = Math.min(coupon.discountValue || 0, applicableAmount);
        break;
      case "FREE_SHIPPING":
        discount = 0;
        break;
      case "BUY_X_GET_Y":
        const buyQuantity = coupon.buyQuantity || 1;
        const getQuantity = coupon.getQuantity || 1;
        const buyProductIds = coupon.buyProducts || [];
        const buyCategoryIds = coupon.buyCategories || [];
        const getProductIds = coupon.getProducts || [];
        const getCategoryIds = coupon.getCategories || [];
        const maxApplications = coupon.maxApplications || 1;

        const buyItems = cartItems.filter((item) => {
          const inBuyProducts = buyProductIds.includes(item.product.id);
          const inBuyCategories =
            item.product.categories?.some((cat) =>
              buyCategoryIds.includes(cat.id),
            ) || false;
          return inBuyProducts || inBuyCategories;
        });

        const getItems = cartItems.filter((item) => {
          const inGetProducts = getProductIds.includes(item.product.id);
          const inGetCategories =
            item.product.categories?.some((cat) =>
              getCategoryIds.includes(cat.id),
            ) || false;
          return inGetProducts || inGetCategories;
        });

        const totalBuyQuantity = buyItems.reduce(
          (sum, item) => sum + item.quantity,
          0,
        );
        const timesApplicable = Math.min(
          Math.floor(totalBuyQuantity / buyQuantity),
          maxApplications,
        );

        if (timesApplicable > 0 && getItems.length > 0) {
          const sortedGetItems = [...getItems].sort(
            (a, b) => b.product.price - a.product.price,
          );

          let remainingGetQuantity = getQuantity * timesApplicable;

          for (const item of sortedGetItems) {
            if (remainingGetQuantity <= 0) break;

            const quantityToDiscount = Math.min(
              item.quantity,
              remainingGetQuantity,
            );

            if (coupon.discountApplication === "FREE") {
              discount += item.product.price * quantityToDiscount;
            } else if (coupon.discountApplication === "PERCENTAGE") {
              discount +=
                (item.product.price *
                  quantityToDiscount *
                  (coupon.getDiscountValue || 0)) /
                100;
            } else if (coupon.discountApplication === "FIXED_AMOUNT") {
              discount +=
                Math.min(coupon.getDiscountValue || 0, item.product.price) *
                quantityToDiscount;
            }

            remainingGetQuantity -= quantityToDiscount;
          }
        }
        break;
      default:
        discount = 0;
    }
  }

  if (coupon.maximumDiscount && discount > coupon.maximumDiscount) {
    discount = coupon.maximumDiscount;
  }

  return Math.round(discount * 100) / 100;
}
