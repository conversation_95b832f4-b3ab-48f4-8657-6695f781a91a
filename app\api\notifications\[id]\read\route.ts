import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../../lib/auth";
import { logger } from "../../../../lib/logger";

/**
 * POST /api/notifications/[id]/read
 * Mark a notification as read
 *
 * NOTE: This endpoint is temporarily disabled during Prisma to MongoDB migration
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const notificationId = params.id;

    logger.info(
      `Mark notification as read API temporarily disabled during migration: ${notificationId}`,
    );

    // Return success during migration
    return NextResponse.json({
      success: true,
      message: "Mark as read temporarily disabled during migration",
    });
  } catch (error) {
    logger.error("Failed to mark notification as read", error as Error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
