import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../lib/auth";
import { sendTestEmail } from "../../lib/email";
import {
  handleApiError,
  AuthenticationError,
  AuthorizationError,
  async<PERSON><PERSON><PERSON>,
} from "../../lib/errors";
import { logger } from "../../lib/logger";

export const POST = asyncHandler(async (request: NextRequest) => {
  logger.apiRequest("POST", "/api/test-email");

  // Check authentication
  const session = await getServerSession(authOptions);
  if (!session?.user) {
    throw new AuthenticationError();
  }

  if (session.user.role !== "ADMIN") {
    throw new AuthorizationError("Only admins can test email configuration");
  }

  const body = await request.json();
  const { email } = body;

  if (!email) {
    return NextResponse.json(
      { success: false, error: "Email address is required" },
      { status: 400 },
    );
  }

  // Send test email
  await sendTestEmail(email);

  logger.info("Test email sent successfully", {
    email,
    userId: session.user.id,
  });

  return NextResponse.json({
    success: true,
    message: "Test email sent successfully",
  });
});
