import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../lib/auth";
import { logger } from "../../lib/logger";

/**
 * GET /api/notifications
 * Get notifications for the current user
 *
 * NOTE: This endpoint is temporarily disabled during Prisma to MongoDB migration
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");

    logger.info("Notifications API temporarily disabled during migration");

    // Return empty results during migration
    return NextResponse.json({
      success: true,
      data: {
        notifications: [],
        pagination: {
          page,
          limit,
          totalCount: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        },
        unreadCount: 0,
      },
    });
  } catch (error) {
    logger.error("Failed to fetch user notifications", error as Error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
