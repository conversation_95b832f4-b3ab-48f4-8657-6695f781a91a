"use client";

import React from "react";
import <PERSON> from "next/link";
import { useSession } from "next-auth/react";
import {
  X,
  Search,
  Home,
  ShoppingBag,
  Info,
  Mail,
  Shield,
  FileText,
  Truck,
  Instagram,
  Facebook,
  Twitter,
  HelpCircle,
} from "lucide-react";

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
}

const MobileMenu: React.FC<MobileMenuProps> = ({ isOpen, onClose }) => {
  const { data: session } = useSession();
  if (!isOpen) return null;

  const mainLinks = [
    { href: "/", icon: <Home className="w-5 h-5 text-white" />, label: "Home" },
    {
      href: "/shop",
      icon: <ShoppingBag className="w-5 h-5 text-white" />,
      label: "Shop",
    },
    {
      href: "/about",
      icon: <Info className="w-5 h-5 text-white" />,
      label: "About",
    },
    {
      href: "/contact",
      icon: <Mail className="w-5 h-5 text-white" />,
      label: "Contact",
    },
  ];

  const secondaryLinks = [
    {
      href: "/privacy",
      icon: <Shield className="w-5 h-5 text-white" />,
      label: "Privacy Policy",
    },
    {
      href: "/terms",
      icon: <FileText className="w-5 h-5 text-white" />,
      label: "Terms and Conditions",
    },
    {
      href: "/shipping",
      icon: <Truck className="w-5 h-5 text-white" />,
      label: "Shippings and Returns",
    },
    {
      href: "/faq",
      icon: <HelpCircle className="w-5 h-5 text-white" />,
      label: "FAQs",
    },
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 lg:hidden">
      <div className="fixed left-0 top-0 h-full w-80 bg-white shadow-xl z-60">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="p-4 flex items-center justify-between border-b">
            <h2 className="font-bold text-lg">
              Welcome, {session?.user?.name?.split(" ")[0] || "User"}
            </h2>
            <button
              onClick={onClose}
              className="p-2 rounded-full hover:bg-gray-100"
            >
              <X className="w-5 h-5 text-gray-600" />
            </button>
          </div>

          {/* Search */}
          <div className="p-4 border-b">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search products..."
                className="w-full bg-gray-100 border border-gray-200 rounded-full py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-green-500"
              />
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 overflow-y-auto p-4 space-y-6">
            <div>
              <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-2">
                Main
              </h3>
              <ul className="space-y-1">
                {mainLinks.map((link) => (
                  <li key={link.href}>
                    <Link
                      href={link.href}
                      onClick={onClose}
                      className="flex items-center p-2 rounded-lg hover:bg-gray-100 text-gray-700 font-medium"
                    >
                      <div className="mr-3 bg-green-600 p-2 rounded-full">
                        {link.icon}
                      </div>
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-2">
                Information
              </h3>
              <ul className="space-y-1">
                {secondaryLinks.map((link) => (
                  <li key={link.href}>
                    <Link
                      href={link.href}
                      onClick={onClose}
                      className="flex items-center p-2 rounded-lg hover:bg-gray-100 text-gray-700 font-medium"
                    >
                      <div className="mr-3 bg-green-600 p-2 rounded-full">
                        {link.icon}
                      </div>
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </nav>

          {/* Footer */}
          <div className="p-4 border-t">
            <div className="flex justify-center space-x-4">
              <a href="#" className="p-2 text-gray-500 hover:text-green-600">
                <Instagram className="w-6 h-6" />
              </a>
              <a href="#" className="p-2 text-gray-500 hover:text-green-600">
                <Facebook className="w-6 h-6" />
              </a>
              <a href="#" className="p-2 text-gray-500 hover:text-green-600">
                <Twitter className="w-6 h-6" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileMenu;
