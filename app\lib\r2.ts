import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
  ListObjectsV2Command,
  GetObjectCommand,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

// Configure R2 client
const r2Client = new S3Client({
  region: "auto",
  endpoint: `https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY!,
  },
  forcePathStyle: true,
});

// Debug function to check R2 configuration
export function checkR2Config() {
  return {
    hasAccessKey: !!process.env.R2_ACCESS_KEY_ID,
    hasSecretKey: !!process.env.R2_SECRET_ACCESS_KEY,
    hasBucketName: !!process.env.R2_BUCKET_NAME,
    hasAccountId: !!process.env.R2_ACCOUNT_ID,
    hasPublicUrl: !!process.env.R2_PUBLIC_URL,
    bucketName: BUCKET_NAME,
    publicUrl: PUBLIC_URL,
    endpoint: `https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  };
}

const BUCKET_NAME = process.env.R2_BUCKET_NAME || "herbalicious-images";
const PUBLIC_URL =
  process.env.R2_PUBLIC_URL ||
  `https://pub-${process.env.R2_ACCOUNT_ID}.r2.dev`;

// Helper function to construct proper public URL
function getPublicUrl(key: string): string {
  // For pub-*.r2.dev URLs, files are directly accessible without bucket name
  if (PUBLIC_URL.includes("pub-") && PUBLIC_URL.includes(".r2.dev")) {
    return `${PUBLIC_URL}/${key}`;
  }
  // If PUBLIC_URL already includes the bucket name, don't add it again
  if (PUBLIC_URL.includes(BUCKET_NAME)) {
    return `${PUBLIC_URL}/${key}`;
  }
  // Otherwise, add the bucket name
  return `${PUBLIC_URL}/${BUCKET_NAME}/${key}`;
}

export interface MediaFile {
  key: string;
  name: string;
  size: number;
  type: string;
  url: string;
  lastModified: Date;
  folder?: string;
}

export interface UploadResult {
  success: boolean;
  file?: MediaFile;
  error?: string;
}

// Upload file to R2 with enhanced security
export async function uploadToR2(
  file: File,
  folder: string = "uploads",
): Promise<UploadResult> {
  try {
    // Validate file before upload
    const validation = validateFile(file);
    if (!validation.valid) {
      return { success: false, error: validation.error };
    }

    const timestamp = Date.now();
    // Enhanced sanitization: remove all special characters except dots and hyphens
    const sanitizedName = file.name
      .toLowerCase()
      .replace(/[^a-z0-9.-]/g, "_")
      .replace(/_{2,}/g, "_") // Replace multiple underscores with single
      .replace(/^_|_$/g, ""); // Remove leading/trailing underscores

    // Ensure the file has a proper extension
    const extension = sanitizedName.split(".").pop();
    if (!extension) {
      return { success: false, error: "File must have an extension" };
    }

    const key = `${folder}/${timestamp}_${sanitizedName}`;

    const buffer = await file.arrayBuffer();

    const command = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
      Body: new Uint8Array(buffer),
      ContentType: file.type,
      ContentLength: file.size,
      // Add security headers
      ContentDisposition: "attachment",
      CacheControl: "max-age=31536000", // 1 year
    });

    await r2Client.send(command);

    const mediaFile: MediaFile = {
      key,
      name: file.name,
      size: file.size,
      type: getFileType(file.name, file.type),
      url: getPublicUrl(key),
      lastModified: new Date(),
      folder,
    };

    return { success: true, file: mediaFile };
  } catch (error) {
    console.error("Error uploading to R2:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Upload failed",
    };
  }
}

// Delete file from R2
export async function deleteFromR2(key: string): Promise<boolean> {
  try {
    const command = new DeleteObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
    });

    await r2Client.send(command);
    return true;
  } catch (error) {
    console.error("Error deleting from R2:", error);
    return false;
  }
}

// List files from R2
export async function listR2Files(
  folder?: string,
  maxKeys: number = 100,
): Promise<MediaFile[]> {
  try {
    const command = new ListObjectsV2Command({
      Bucket: BUCKET_NAME,
      Prefix: folder ? `${folder}/` : undefined,
      MaxKeys: maxKeys,
    });

    const response = await r2Client.send(command);

    if (!response.Contents) {
      return [];
    }

    return response.Contents.map((object) => {
      const key = object.Key!;
      const name = key.split("/").pop() || key;
      return {
        key,
        name,
        size: object.Size || 0,
        type: getFileType(name),
        url: getPublicUrl(key),
        lastModified: object.LastModified || new Date(),
        folder: key.includes("/") ? key.split("/")[0] : undefined,
      };
    });
  } catch (error) {
    console.error("Error listing R2 files:", error);
    return [];
  }
}

// Generate presigned URL for secure uploads
export async function generatePresignedUrl(
  key: string,
  contentType: string,
  expiresIn: number = 3600,
): Promise<string> {
  try {
    const command = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
      ContentType: contentType,
    });

    return await getSignedUrl(r2Client, command, { expiresIn });
  } catch (error) {
    console.error("Error generating presigned URL:", error);
    throw error;
  }
}

// Get file type from extension and MIME type
function getFileType(filename: string, mimeType?: string): string {
  const extension = filename.split(".").pop()?.toLowerCase();

  // Check MIME type first if available
  if (mimeType) {
    if (mimeType.startsWith("image/")) return "image";
    if (mimeType.startsWith("video/")) return "video";
    if (
      mimeType === "application/pdf" ||
      mimeType.startsWith("application/msword") ||
      mimeType.startsWith("application/vnd.openxmlformats-officedocument")
    )
      return "document";
  }

  // Fallback to extension
  const imageTypes = ["jpg", "jpeg", "png", "gif", "webp", "svg", "bmp", "ico"];
  const videoTypes = ["mp4", "avi", "mov", "wmv", "flv", "webm", "mkv", "m4v"];
  const documentTypes = [
    "pdf",
    "doc",
    "docx",
    "txt",
    "rtf",
    "xls",
    "xlsx",
    "ppt",
    "pptx",
  ];

  if (imageTypes.includes(extension || "")) return "image";
  if (videoTypes.includes(extension || "")) return "video";
  if (documentTypes.includes(extension || "")) return "document";

  return "other";
}

// Get file size in human readable format
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

// Validate file type and size with enhanced security
export function validateFile(file: File): { valid: boolean; error?: string } {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = [
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/webp",
    "image/svg+xml",
    "video/mp4",
    "video/webm",
    "application/pdf",
  ];

  const allowedExtensions = [
    ".jpg",
    ".jpeg",
    ".png",
    ".gif",
    ".webp",
    ".svg",
    ".mp4",
    ".webm",
    ".pdf",
  ];

  // Check file size
  if (file.size > maxSize) {
    return { valid: false, error: "File size must be less than 10MB" };
  }

  // Check MIME type
  if (!allowedTypes.includes(file.type)) {
    return { valid: false, error: "File type not supported" };
  }

  // Check file extension
  const fileName = file.name.toLowerCase();
  const hasValidExtension = allowedExtensions.some((ext) =>
    fileName.endsWith(ext),
  );
  if (!hasValidExtension) {
    return { valid: false, error: "File extension not allowed" };
  }

  // Check for double extensions (e.g., file.php.jpg)
  const parts = fileName.split(".");
  if (parts.length > 2) {
    // Allow only specific cases like image.min.js
    const suspiciousExtensions = [
      "php",
      "exe",
      "sh",
      "bat",
      "cmd",
      "com",
      "scr",
      "vbs",
      "js",
      "jar",
    ];
    for (let i = 0; i < parts.length - 1; i++) {
      if (suspiciousExtensions.includes(parts[i])) {
        return { valid: false, error: "Suspicious file name detected" };
      }
    }
  }

  // Check for null bytes in filename
  if (fileName.includes("\0")) {
    return { valid: false, error: "Invalid file name" };
  }

  // Additional check for SVG files (they can contain scripts)
  if (file.type === "image/svg+xml" || fileName.endsWith(".svg")) {
    // Note: In production, you should parse and sanitize SVG content
    // This is a basic check - implement proper SVG sanitization
    return { valid: true, error: undefined };
  }

  return { valid: true };
}
