import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/app/lib/mongoose";
import {
  Product,
  Category,
  ProductVariant,
  ProductCategory,
} from "@/app/lib/models";
import { generateSlug } from "@/app/lib/currency";
// Inline minimal asyncHandler since '@/app/lib/async-handler' does not exist
const asyncHandler = <T extends (...args: any[]) => Promise<Response>>(handler: T) =>
  (async (...args: Parameters<T>) => {
    try {
      return await handler(...args);
    } catch (err) {
      console.error(err);
      return NextResponse.json({ success: false, error: "Internal Server Error" }, { status: 500 });
    }
  }) as T;
import {
  ConflictError,
  ValidationError,
} from "@/app/lib/errors";

// POST /api/products/import - Bulk import products from CSV/JSON payload
export const POST = asyncHandler(async (request: NextRequest) => {
  await connectDB();

  const body = await request.json();
  const { products } = body || {};

  if (!products || !Array.isArray(products)) {
    throw new ValidationError("Invalid products data");
  }

  const results: { success: number; failed: number; errors: string[] } = {
    success: 0,
    failed: 0,
    errors: [],
  };

  // Fetch categories once, active only
  const categories = await Category.find({ isActive: true }).lean();
  const categoryMap = new Map<string, string>(
    categories.map((cat: any) => [String(cat.name).toLowerCase(), String(cat._id)]),
  );

  // Add common variations
  categories.forEach((cat: any) => {
    const lowerName = String(cat.name).toLowerCase();
    if (lowerName === "skincare") {
      categoryMap.set("skin care", String(cat._id));
      categoryMap.set("skin", String(cat._id));
    } else if (lowerName === "hair care") {
      categoryMap.set("haircare", String(cat._id));
      categoryMap.set("hair", String(cat._id));
    } else if (lowerName === "body care") {
      categoryMap.set("bodycare", String(cat._id));
      categoryMap.set("body", String(cat._id));
    }
  });

  for (const productData of products) {
    try {
      const {
        name,
        slug,
        description,
        shortDescription,
        price,
        comparePrice,
        category: categoryName,
        categoryNames = [],
        isFeatured,
        isActive,
        variations: rawVariations = [],
      } = productData || {};

      if (!name) {
        results.failed++;
        results.errors.push(`Product missing required name field`);
        continue;
      }

      // Parse variations from string if needed
      let variations: any[] = rawVariations;
      if (typeof rawVariations === "string") {
        try {
          variations = JSON.parse(rawVariations);
        } catch {
          variations = [];
        }
      }
      if (!Array.isArray(variations)) {
        variations = [];
      }

      // Price handling: allow 0 when variations exist, require non-null otherwise
      let defaultPrice =
        price !== undefined && price !== null ? parseFloat(price.toString()) : null;

      if ((defaultPrice === null || Number.isNaN(defaultPrice)) && variations.length > 0) {
        defaultPrice = 0;
      } else if ((defaultPrice === null || Number.isNaN(defaultPrice)) && variations.length === 0) {
        results.failed++;
        results.errors.push(
          `Product "${name}" missing required price field or variations with pricing`,
        );
        continue;
      }

      // Category name collection
      const allCategoryNames: string[] = [];
      if (categoryName) allCategoryNames.push(String(categoryName));
      if (Array.isArray(categoryNames) && categoryNames.length > 0) {
        allCategoryNames.push(...categoryNames.map((n: any) => String(n)));
      }

      // Map to category ids
      const categoryIds: string[] = [];
      for (const catName of allCategoryNames) {
        let categoryId = categoryMap.get(catName.toLowerCase());
        if (!categoryId) {
          const lower = catName.toLowerCase();
          if (lower.includes("skin")) {
            categoryId = categoryMap.get("skincare");
          } else if (lower.includes("hair")) {
            categoryId = categoryMap.get("hair care");
          } else if (lower.includes("body")) {
            categoryId = categoryMap.get("body care");
          }
        }
        if (categoryId) {
          categoryIds.push(categoryId);
        } else {
          results.errors.push(`Category "${catName}" not found for product "${name}"`);
        }
      }

      if (categoryIds.length === 0) {
        results.failed++;
        results.errors.push(`Product "${name}" has no valid categories`);
        continue;
      }

      // Slug
      const productSlug = slug || generateSlug(name);

      // Uniqueness check
      const existing = await Product.findOne({ slug: productSlug }).lean();
      if (existing) {
        results.failed++;
        results.errors.push(`Product with slug "${productSlug}" already exists`);
        continue;
      }

      // Create product
      const created = await Product.create({
        name,
        slug: productSlug,
        description: description || "",
        shortDescription: shortDescription || "",
        price: defaultPrice,
        comparePrice:
          comparePrice !== undefined && comparePrice !== null
            ? parseFloat(comparePrice.toString())
            : null,
        categoryId: categoryIds[0], // primary/back-compat
        isFeatured: Boolean(isFeatured),
        isActive: isActive !== false,
      });

      // Create many-to-many links if model uses ProductCategory
      if (categoryIds.length > 0 && ProductCategory) {
        const docs = categoryIds.map((cid) => ({
          productId: created._id,
          categoryId: cid,
        }));
        // If duplicates are possible due to data, rely on unique compound index to ignore duplicates
        try {
          await ProductCategory.insertMany(docs, { ordered: false });
        } catch (e: any) {
          // ignore duplicate key errors in batch
          if (!(e?.writeErrors && e.writeErrors.every((we: any) => we.code === 11000))) {
            throw e;
          }
        }
      }

      // Create variants if provided
      if (variations.length > 0 && ProductVariant) {
        const variantDocs = variations
          .filter((v: any) => v && v.name && v.value)
          .map((v: any) => ({
            productId: created._id,
            name: String(v.name),
            value: String(v.value),
            price:
              v.price !== undefined && v.price !== null
                ? parseFloat(v.price.toString())
                : null,
            pricingMode: v.pricingMode && ["REPLACE", "INCREMENT", "FIXED"].includes(String(v.pricingMode))
              ? String(v.pricingMode)
              : "REPLACE",
            createdAt: new Date(),
            updatedAt: new Date(),
          }));

        if (variantDocs.length > 0) {
          try {
            await ProductVariant.insertMany(variantDocs, { ordered: false });
          } catch (e: any) {
            // Ignore duplicates across rows; record error message for visibility
            if (e?.code === 11000 || e?.writeErrors) {
              results.errors.push(
                `Some duplicate variants for product "${name}" were skipped`,
              );
            } else {
              throw e;
            }
          }
        }
      }

      results.success++;
    } catch (err: any) {
      results.failed++;
      if (err?.code === 11000) {
        results.errors.push(`Duplicate key error: ${err?.message || "duplicate detected"}`);
      } else if (err instanceof ConflictError || err instanceof ValidationError) {
        results.errors.push(err.message);
      } else {
        results.errors.push(
          `Failed to create product "${productData?.name ?? "unknown"}": ${String(err)}`,
        );
      }
      console.error("Error creating product:", err);
    }
  }

  return NextResponse.json({
    success: true,
    data: results,
    message: `Import completed: ${results.success} successful, ${results.failed} failed`,
  });
});
