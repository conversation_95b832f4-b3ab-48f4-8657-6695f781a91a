# 🔧 Cloudflare R2 Setup Guide

This guide will help you properly configure Cloudflare R2 for your Herbalicious media management system.

## 📋 Prerequisites

1. Cloudflare account
2. Domain managed by Cloudflare (herbalicous.in)
3. R2 subscription (free tier available)

## 🚀 Step-by-Step Setup

### Step 1: Enable R2 in Cloudflare Dashboard

1. Log into your Cloudflare dashboard
2. Go to **R2 Object Storage** in the sidebar
3. Click **"Create bucket"**
4. Name your bucket: `herbalicious-media`
5. Choose a location close to your users

### Step 2: Get Your Account ID

1. In Cloudflare dashboard, go to the right sidebar
2. Copy your **Account ID** (it looks like: `a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6`)
3. Save this for your environment variables

### Step 3: Create R2 API Token

1. Go to **R2 Object Storage** → **Manage R2 API tokens**
2. Click **"Create API token"**
3. Choose **"Custom token"**
4. Set permissions:
   - **Account**: Your account
   - **Zone Resources**: Include All zones
   - **Account Resources**: Include All accounts
   - **Permissions**:
     - `Cloudflare R2:Edit`
     - `Zone:Read`
5. Click **"Continue to summary"** → **"Create Token"**
6. Copy the **Token ID** and **Token Secret**

### Step 4: Configure Custom Domain (Optional but Recommended)

1. Go to your bucket in R2 dashboard
2. Click **"Settings"** tab
3. Scroll to **"Custom Domains"**
4. Click **"Connect Domain"**
5. Enter: `images.herbalicous.in`
6. Click **"Continue"**
7. Add the CNAME record to your DNS:
   - **Type**: CNAME
   - **Name**: images
   - **Target**: `herbalicious-media.your-account-id.r2.cloudflarestorage.com`

### Step 5: Set Public Access (Important!)

1. In your bucket settings, go to **"Settings"** tab
2. Find **"Public access"** section
3. Enable **"Allow public read access"**
4. This allows files to be accessed via URLs

### Step 6: Update Environment Variables

Update your `.env` file with the correct values:

```env
# Cloudflare R2 Storage
R2_ACCESS_KEY_ID=your_token_id_here
R2_SECRET_ACCESS_KEY=your_token_secret_here
R2_BUCKET_NAME=herbalicious-media
R2_ACCOUNT_ID=your_account_id_here
R2_ENDPOINT=https://your_account_id.r2.cloudflarestorage.com
R2_PUBLIC_URL=https://images.herbalicous.in
```

**Replace these values:**

- `your_token_id_here` → Your R2 API Token ID
- `your_token_secret_here` → Your R2 API Token Secret
- `your_account_id_here` → Your Cloudflare Account ID

## 🔍 Verification Steps

### Test 1: Check Configuration

Visit: `http://localhost:3000/api/media/config`

Should return:

```json
{
  "success": true,
  "config": {
    "hasAccessKey": true,
    "hasSecretKey": true,
    "bucketName": "herbalicious-media",
    "ready": true
  }
}
```

### Test 2: Test Upload

1. Go to `http://localhost:3000/admin/media`
2. Click "Test R2 Upload" button
3. Should show success message with file URL

### Test 3: Manual Bucket Check

1. Go to Cloudflare R2 dashboard
2. Open your `herbalicious-media` bucket
3. You should see uploaded files

## 🛠 Troubleshooting

### Issue: "Access Denied" Error

**Solution:**

1. Check API token permissions
2. Ensure bucket has public read access enabled
3. Verify account ID is correct

### Issue: "Bucket not found"

**Solution:**

1. Verify bucket name matches exactly
2. Check account ID in endpoint URL
3. Ensure bucket exists in correct account

### Issue: "Invalid credentials"

**Solution:**

1. Regenerate R2 API token
2. Double-check token ID and secret
3. Ensure no extra spaces in environment variables

### Issue: Custom domain not working

**Solution:**

1. Verify CNAME record is set correctly
2. Wait for DNS propagation (up to 24 hours)
3. Check domain is managed by Cloudflare
4. Ensure SSL/TLS is set to "Full" or "Full (strict)"

## 📝 Alternative Setup (Without Custom Domain)

If you don't want to use a custom domain, you can use the direct R2 URL:

```env
R2_PUBLIC_URL=https://pub-your-bucket-id.r2.dev
```

To get this URL:

1. Go to your bucket settings
2. Look for "Public bucket URL"
3. Copy the `https://pub-xxxxx.r2.dev` URL

## 🔐 Security Notes

1. **API Tokens**: Keep your R2 API tokens secure
2. **Public Access**: Only enable if you need public file access
3. **CORS**: Configure CORS if accessing from browser
4. **Rate Limiting**: Monitor usage to avoid unexpected charges

## 📊 Monitoring

1. **Usage**: Monitor R2 usage in Cloudflare dashboard
2. **Costs**: Check R2 billing section
3. **Performance**: Use Cloudflare Analytics for CDN performance

## 🎯 Next Steps

After setup:

1. Test file upload in admin panel
2. Verify files are accessible via public URLs
3. Update product images to use R2 URLs
4. Set up automated backups if needed

---

**Need Help?**

- Check Cloudflare R2 documentation
- Verify all environment variables are set correctly
- Test with the built-in configuration checker
