"use client";

import React from "react";
import { Leaf, Heart, Shield, Award, Users, Globe } from "lucide-react";

const About: React.FC = () => {
  const values = [
    {
      icon: Leaf,
      title: "Natural Ingredients",
      description:
        "We source only the finest organic botanicals from sustainable farms worldwide.",
    },
    {
      icon: Heart,
      title: "Gentle Care",
      description:
        "Our formulations are designed to be gentle yet effective for all skin types.",
    },
    {
      icon: Shield,
      title: "Safety First",
      description:
        "Every product is dermatologist-tested and free from harmful chemicals.",
    },
    {
      icon: Award,
      title: "Quality Assured",
      description:
        "We maintain the highest standards in manufacturing and quality control.",
    },
  ];

  const stats = [
    { number: "50K+", label: "Happy Customers" },
    { number: "100%", label: "Natural Ingredients" },
    { number: "5 Years", label: "In Business" },
    { number: "25+", label: "Products" },
  ];

  const team = [
    {
      name: "<PERSON>",
      role: "Founder & CEO",
      bio: "Passionate about natural skincare with 15+ years in the beauty industry.",
    },
    {
      name: "Dr. <PERSON>",
      role: "Chief Formulator",
      bio: "PhD in Chemistry, specializing in botanical skincare formulations.",
    },
    {
      name: "<PERSON>",
      role: "Head of Sustainability",
      bio: "Ensures our practices align with environmental responsibility.",
    },
  ];

  return (
    <div className="lg:grid lg:grid-cols-12 lg:gap-8">
      {/* Mobile Layout */}
      <div className="lg:hidden">
        {/* Hero Section */}
        <div className="px-4 py-8 bg-gradient-to-br from-green-50 to-green-100">
          <div className="text-center">
            <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <Leaf className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-3xl font-bold text-gray-800 mb-4">
              About Herbalicious
            </h1>
            <p className="text-gray-600 leading-relaxed">
              We believe that nature holds the key to beautiful, healthy skin.
              Our journey began with a simple mission: to create effective
              skincare products using only the purest botanical ingredients.
            </p>
          </div>
        </div>

        {/* Our Story */}
        <div className="px-4 py-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Our Story</h2>
          <div className="space-y-4 text-gray-600">
            <p>
              Founded in 2019, Herbalicious was born from a personal struggle
              with sensitive skin and the frustration of finding truly natural,
              effective skincare products. Our founder, Sarah Johnson, spent
              years researching botanical ingredients and their benefits for
              skin health.
            </p>
            <p>
              What started as a small kitchen experiment has grown into a
              trusted brand that serves thousands of customers worldwide. We
              remain committed to our core values of purity, sustainability, and
              effectiveness.
            </p>
          </div>
        </div>

        {/* Values */}
        <div className="px-4 py-8 bg-gray-50">
          <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
            Our Values
          </h2>
          <div className="space-y-6">
            {values.map((value, index) => (
              <div key={index} className="bg-white rounded-2xl p-6 shadow-sm">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <value.icon className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-2">
                      {value.title}
                    </h3>
                    <p className="text-gray-600 text-sm">{value.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Stats */}
        <div className="px-4 py-8">
          <div className="grid grid-cols-2 gap-4">
            {stats.map((stat, index) => (
              <div
                key={index}
                className="text-center p-6 bg-white rounded-2xl shadow-sm"
              >
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 text-sm">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Sustainability */}
        <div className="px-4 py-8 bg-green-50">
          <div className="text-center">
            <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <Globe className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-gray-800 mb-4">
              Sustainability Commitment
            </h2>
            <p className="text-gray-600 mb-6">
              We're committed to protecting the planet that provides our
              ingredients. Our packaging is recyclable, and we partner with
              suppliers who share our environmental values.
            </p>
            <div className="space-y-3 text-left">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                <span className="text-gray-600 text-sm">
                  100% recyclable packaging
                </span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                <span className="text-gray-600 text-sm">
                  Sustainable sourcing practices
                </span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                <span className="text-gray-600 text-sm">
                  Carbon-neutral shipping
                </span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                <span className="text-gray-600 text-sm">
                  Supporting local communities
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Team */}
        <div className="px-4 py-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
            Meet Our Team
          </h2>
          <div className="space-y-6">
            {team.map((member, index) => (
              <div
                key={index}
                className="bg-white rounded-2xl p-6 shadow-sm text-center"
              >
                <div className="w-20 h-20 bg-gray-200 rounded-full mx-auto mb-4"></div>
                <h3 className="font-semibold text-gray-800 mb-1">
                  {member.name}
                </h3>
                <p className="text-green-600 text-sm font-medium mb-3">
                  {member.role}
                </p>
                <p className="text-gray-600 text-sm">{member.bio}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:block lg:col-span-12">
        <div className="py-8">
          {/* Hero Section */}
          <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-3xl p-16 mb-16 text-center">
            <div className="w-24 h-24 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-8">
              <Leaf className="w-12 h-12 text-white" />
            </div>
            <h1 className="text-5xl font-bold text-gray-800 mb-6">
              About Herbalicious
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              We believe that nature holds the key to beautiful, healthy skin.
              Our journey began with a simple mission: to create effective
              skincare products using only the purest botanical ingredients,
              crafted with love and respect for both your skin and our planet.
            </p>
          </div>

          {/* Our Story */}
          <div className="grid grid-cols-2 gap-16 mb-16">
            <div>
              <h2 className="text-3xl font-bold text-gray-800 mb-6">
                Our Story
              </h2>
              <div className="space-y-6 text-gray-600 leading-relaxed">
                <p>
                  Founded in 2019, Herbalicious was born from a personal
                  struggle with sensitive skin and the frustration of finding
                  truly natural, effective skincare products. Our founder, Sarah
                  Johnson, spent years researching botanical ingredients and
                  their incredible benefits for skin health.
                </p>
                <p>
                  What started as a small kitchen experiment has grown into a
                  trusted brand that serves thousands of customers worldwide.
                  Despite our growth, we remain committed to our core values of
                  purity, sustainability, and effectiveness.
                </p>
                <p>
                  Every product we create is a testament to our belief that
                  nature provides the most powerful and gentle solutions for
                  healthy, radiant skin.
                </p>
              </div>
            </div>
            <div className="bg-gradient-to-br from-green-100 to-green-200 rounded-2xl p-8 flex items-center justify-center">
              <div className="text-center">
                <div className="w-32 h-32 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Heart className="w-16 h-16 text-white" />
                </div>
                <h3 className="text-2xl font-semibold text-gray-800 mb-4">
                  Made with Love
                </h3>
                <p className="text-gray-600">
                  Every product is crafted with care, attention to detail, and a
                  deep respect for natural ingredients.
                </p>
              </div>
            </div>
          </div>

          {/* Values */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-gray-800 mb-12 text-center">
              Our Values
            </h2>
            <div className="grid grid-cols-2 gap-8">
              {values.map((value, index) => (
                <div
                  key={index}
                  className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg transition-shadow"
                >
                  <div className="flex items-start space-x-6">
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <value.icon className="w-8 h-8 text-green-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-800 mb-3">
                        {value.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {value.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Stats */}
          <div className="bg-gray-50 rounded-3xl p-12 mb-16">
            <h2 className="text-3xl font-bold text-gray-800 mb-12 text-center">
              Our Impact
            </h2>
            <div className="grid grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl font-bold text-green-600 mb-4">
                    {stat.number}
                  </div>
                  <div className="text-gray-600 text-lg">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Sustainability */}
          <div className="grid grid-cols-2 gap-16 mb-16">
            <div className="bg-green-50 rounded-2xl p-8 flex items-center justify-center">
              <div className="text-center">
                <div className="w-24 h-24 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Globe className="w-12 h-12 text-white" />
                </div>
                <h3 className="text-2xl font-semibold text-gray-800 mb-4">
                  Planet First
                </h3>
                <p className="text-gray-600">
                  Protecting the environment that provides our precious
                  ingredients.
                </p>
              </div>
            </div>
            <div>
              <h2 className="text-3xl font-bold text-gray-800 mb-6">
                Sustainability Commitment
              </h2>
              <p className="text-gray-600 mb-8 leading-relaxed">
                We're committed to protecting the planet that provides our
                ingredients. Our comprehensive sustainability program ensures
                that every aspect of our business contributes to a healthier
                world.
              </p>
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-3 h-3 bg-green-600 rounded-full"></div>
                  <span className="text-gray-600">
                    100% recyclable and biodegradable packaging
                  </span>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-3 h-3 bg-green-600 rounded-full"></div>
                  <span className="text-gray-600">
                    Sustainable and ethical sourcing practices
                  </span>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-3 h-3 bg-green-600 rounded-full"></div>
                  <span className="text-gray-600">
                    Carbon-neutral shipping and operations
                  </span>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-3 h-3 bg-green-600 rounded-full"></div>
                  <span className="text-gray-600">
                    Supporting local farming communities
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Team */}
          <div>
            <h2 className="text-3xl font-bold text-gray-800 mb-12 text-center">
              Meet Our Team
            </h2>
            <div className="grid grid-cols-3 gap-8">
              {team.map((member, index) => (
                <div
                  key={index}
                  className="bg-white rounded-2xl p-8 shadow-sm text-center hover:shadow-lg transition-shadow"
                >
                  <div className="w-32 h-32 bg-gray-200 rounded-full mx-auto mb-6"></div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">
                    {member.name}
                  </h3>
                  <p className="text-green-600 font-medium mb-4">
                    {member.role}
                  </p>
                  <p className="text-gray-600 leading-relaxed">{member.bio}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default About;
