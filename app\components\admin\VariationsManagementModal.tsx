"use client";

import React, { useState, useEffect } from "react";
import { Plus, Edit, Trash2, X, Save, Package } from "lucide-react";
import { formatPrice } from "../../lib/currency";

interface ProductVariation {
  id: string;
  name: string;
  value: string;
  price?: number;
  pricingMode?: "REPLACE" | "INCREMENT" | "FIXED";
}

interface Product {
  id: string;
  name: string;
}

interface VariationsManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product;
}

const VariationsManagementModal: React.FC<VariationsManagementModalProps> = ({
  isOpen,
  onClose,
  product,
}) => {
  const [variations, setVariations] = useState<ProductVariation[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingVariation, setEditingVariation] =
    useState<ProductVariation | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    value: "",
    price: "",
    pricingMode: "INCREMENT",
  });

  useEffect(() => {
    if (isOpen && product) {
      fetchVariations();
    }
  }, [isOpen, product]);

  const fetchVariations = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/products/${product.id}/variations`);
      const result = await response.json();

      if (result.success) {
        setVariations(result.data);
      }
    } catch (error) {
      console.error("Error fetching variations:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddVariation = async () => {
    if (!formData.name.trim() || !formData.value.trim()) {
      alert("Please fill in both name and value");
      return;
    }

    try {
      const response = await fetch(`/api/products/${product.id}/variations`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: formData.name,
          value: formData.value,
          price: formData.price ? parseFloat(formData.price) : null,
          pricingMode: formData.pricingMode,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setVariations((prev) => [...prev, result.data]);
        setFormData({
          name: "",
          value: "",
          price: "",
          pricingMode: "INCREMENT",
        });
        setShowAddForm(false);
      } else {
        alert(result.error || "Failed to add variation");
      }
    } catch (error) {
      console.error("Error adding variation:", error);
      alert("Failed to add variation");
    }
  };

  const handleUpdateVariation = async (variation: ProductVariation) => {
    try {
      const response = await fetch(
        `/api/products/${product.id}/variations/${variation.id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(variation),
        },
      );

      const result = await response.json();

      if (result.success) {
        setVariations((prev) =>
          prev.map((v) => (v.id === variation.id ? result.data : v)),
        );
        setEditingVariation(null);
      } else {
        alert(result.error || "Failed to update variation");
      }
    } catch (error) {
      console.error("Error updating variation:", error);
      alert("Failed to update variation");
    }
  };

  const handleDeleteVariation = async (variationId: string) => {
    if (!confirm("Are you sure you want to delete this variation?")) return;

    try {
      const response = await fetch(
        `/api/products/${product.id}/variations/${variationId}`,
        {
          method: "DELETE",
        },
      );

      const result = await response.json();

      if (result.success) {
        setVariations((prev) => prev.filter((v) => v.id !== variationId));
      } else {
        alert("Failed to delete variation");
      }
    } catch (error) {
      console.error("Error deleting variation:", error);
      alert("Failed to delete variation");
    }
  };

  // Group variations by name
  const groupedVariations = variations.reduce(
    (acc, variation) => {
      if (!acc[variation.name]) {
        acc[variation.name] = [];
      }
      acc[variation.name].push(variation);
      return acc;
    },
    {} as Record<string, ProductVariation[]>,
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-5xl max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
            <Package className="w-5 h-5" />
            <span>Manage Variations - {product.name}</span>
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Add Variation Button */}
          <div className="mb-6">
            <button
              onClick={() => setShowAddForm(true)}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
            >
              <Plus className="w-4 h-4" />
              <span>Add Variation</span>
            </button>
          </div>

          {/* Add Variation Form */}
          {showAddForm && (
            <div className="mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50">
              <h3 className="text-lg font-medium mb-4">Add New Variation</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Name (e.g., Size, Color)
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) =>
                      setFormData({ ...formData, name: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                    placeholder="Size"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Value (e.g., Large, Red)
                  </label>
                  <input
                    type="text"
                    value={formData.value}
                    onChange={(e) =>
                      setFormData({ ...formData, value: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                    placeholder="Large"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Price Adjustment (₹)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.price}
                    onChange={(e) =>
                      setFormData({ ...formData, price: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                    placeholder="0.00"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Pricing Mode
                  </label>
                  <select
                    value={formData.pricingMode}
                    onChange={(e) =>
                      setFormData({ ...formData, pricingMode: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                  >
                    <option value="INCREMENT">Add to Base Price</option>
                    <option value="REPLACE">Replace Base Price</option>
                    <option value="FIXED">Use as Fixed Price</option>
                  </select>
                  <p className="text-xs text-gray-500 mt-1">
                    How this variation affects the final price
                  </p>
                </div>
              </div>
              <div className="flex space-x-3 mt-4">
                <button
                  onClick={handleAddVariation}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
                >
                  <Save className="w-4 h-4" />
                  <span>Save Variation</span>
                </button>
                <button
                  onClick={() => {
                    setShowAddForm(false);
                    setFormData({
                      name: "",
                      value: "",
                      price: "",
                      pricingMode: "INCREMENT",
                    });
                  }}
                  className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          )}

          {/* Variations List */}
          {loading ? (
            <div className="text-center py-8">
              <div className="text-gray-500">Loading variations...</div>
            </div>
          ) : variations.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-500">
                No variations found. Add your first variation above.
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {Object.entries(groupedVariations).map(
                ([name, variationGroup]) => (
                  <div
                    key={name}
                    className="border border-gray-200 rounded-lg p-4"
                  >
                    <h4 className="text-lg font-medium text-gray-900 mb-4 capitalize">
                      {name}
                    </h4>
                    <div className="space-y-3">
                      {variationGroup.map((variation) => (
                        <VariationItem
                          key={variation.id}
                          variation={variation}
                          isEditing={editingVariation?.id === variation.id}
                          onEdit={setEditingVariation}
                          onUpdate={handleUpdateVariation}
                          onDelete={handleDeleteVariation}
                        />
                      ))}
                    </div>
                  </div>
                ),
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Variation Item Component
interface VariationItemProps {
  variation: ProductVariation;
  isEditing: boolean;
  onEdit: (variation: ProductVariation | null) => void;
  onUpdate: (variation: ProductVariation) => void;
  onDelete: (variationId: string) => void;
}

const VariationItem: React.FC<VariationItemProps> = ({
  variation,
  isEditing,
  onEdit,
  onUpdate,
  onDelete,
}) => {
  const [editData, setEditData] = useState({
    name: variation.name,
    value: variation.value,
    price: variation.price?.toString() || "",
    pricingMode: variation.pricingMode || "INCREMENT",
  });

  const handleSave = () => {
    if (!editData.name.trim() || !editData.value.trim()) {
      alert("Please fill in both name and value");
      return;
    }

    onUpdate({
      ...variation,
      name: editData.name,
      value: editData.value,
      price: editData.price ? parseFloat(editData.price) : undefined,
      pricingMode: editData.pricingMode,
    });
  };

  const handleCancel = () => {
    setEditData({
      name: variation.name,
      value: variation.value,
      price: variation.price?.toString() || "",
      pricingMode: variation.pricingMode || "INCREMENT",
    });
    onEdit(null);
  };

  return (
    <div className="border border-gray-200 rounded-lg p-4 bg-white">
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          {isEditing ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Name
                </label>
                <input
                  type="text"
                  value={editData.name}
                  onChange={(e) =>
                    setEditData({ ...editData, name: e.target.value })
                  }
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Value
                </label>
                <input
                  type="text"
                  value={editData.value}
                  onChange={(e) =>
                    setEditData({ ...editData, value: e.target.value })
                  }
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Price Adjustment (₹)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={editData.price}
                  onChange={(e) =>
                    setEditData({ ...editData, price: e.target.value })
                  }
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Pricing Mode
                </label>
                <select
                  value={editData.pricingMode}
                  onChange={(e) =>
                    setEditData({
                      ...editData,
                      pricingMode: e.target.value as
                        | "REPLACE"
                        | "INCREMENT"
                        | "FIXED",
                    })
                  }
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                  <option value="INCREMENT">Add to Base</option>
                  <option value="REPLACE">Replace Base</option>
                  <option value="FIXED">Fixed Price</option>
                </select>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <span className="text-xs text-gray-500">Name</span>
                <div className="font-medium">{variation.name}</div>
              </div>
              <div>
                <span className="text-xs text-gray-500">Value</span>
                <div className="font-medium">{variation.value}</div>
              </div>
              <div>
                <span className="text-xs text-gray-500">Price Adjustment</span>
                <div className="font-medium">
                  {variation.price
                    ? formatPrice(variation.price)
                    : "No adjustment"}
                </div>
              </div>
              <div>
                <span className="text-xs text-gray-500">Mode</span>
                <div className="font-medium text-xs">
                  {variation.pricingMode === "REPLACE"
                    ? "Replace"
                    : variation.pricingMode === "FIXED"
                      ? "Fixed"
                      : "Add to Base"}
                </div>
              </div>
            </div>
          )}
        </div>
        <div className="flex space-x-2 ml-4">
          {isEditing ? (
            <>
              <button
                onClick={handleSave}
                className="p-1 text-green-600 hover:text-green-700"
                title="Save"
              >
                <Save className="w-4 h-4" />
              </button>
              <button
                onClick={handleCancel}
                className="p-1 text-gray-400 hover:text-gray-600"
                title="Cancel"
              >
                <X className="w-4 h-4" />
              </button>
            </>
          ) : (
            <>
              <button
                onClick={() => onEdit(variation)}
                className="p-1 text-blue-600 hover:text-blue-700"
                title="Edit"
              >
                <Edit className="w-4 h-4" />
              </button>
              <button
                onClick={() => onDelete(variation.id)}
                className="p-1 text-red-600 hover:text-red-700"
                title="Delete"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default VariationsManagementModal;
