import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import * as crypto from "crypto";
import { connectDB, User } from "@/app/lib/db";
import { sendPasswordResetEmail } from "../../../lib/email";
import { strictLimiter, withRateLimit } from "@/app/lib/rate-limit";
import {
  handleApiError,
  ValidationError,
  RateLimitError,
  asyncHandler,
} from "@/app/lib/errors";
import { logger } from "@/app/lib/logger";

const forgotPasswordSchema = z.object({
  email: z.string().email("Invalid email address"),
});

export const POST = asyncHandler(async (request: NextRequest) => {
  logger.apiRequest("POST", "/api/auth/forgot-password");

  // Apply strict rate limiting (3 attempts per hour per IP)
  await withRateLimit(request, strictLimiter, 3);

  const body = await request.json();

  // Validate input
  const validatedData = forgotPasswordSchema.parse(body);
  const { email } = validatedData;

  logger.info("Password reset requested", { email });

  await connectDB();

  // Check if user exists
  const user = await User.findOne({ email });

  // Always return success to prevent email enumeration
  if (!user) {
    logger.warn("Password reset requested for non-existent email", { email });
    return NextResponse.json(
      {
        success: true,
        message:
          "If an account with that email exists, we have sent a password reset link.",
      },
      { status: 200 },
    );
  }

  // Generate reset token
  const resetToken = crypto.randomBytes(32).toString("hex");
  const resetTokenExpiry = new Date(Date.now() + 3600000); // 1 hour from now

  // Save reset token to database
  await User.updateOne(
    { email },
    {
      resetToken,
      resetTokenExpiry,
    },
  );

  // Send password reset email
  try {
    await sendPasswordResetEmail(email, resetToken);
    logger.info("Password reset email sent successfully", {
      email,
      userId: user.id,
    });
  } catch (emailError) {
    logger.emailError(email, "Password Reset Email", emailError as Error);
    // Don't expose email sending errors to the user
  }

  return NextResponse.json(
    {
      success: true,
      message:
        "If an account with that email exists, we have sent a password reset link.",
    },
    { status: 200 },
  );
});
