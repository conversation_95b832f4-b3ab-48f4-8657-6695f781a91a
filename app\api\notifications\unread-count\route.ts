import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../lib/auth";
import { logger } from "../../../lib/logger";

/**
 * GET /api/notifications/unread-count
 * Get unread notification count for the current user
 *
 * NOTE: This endpoint is temporarily disabled during Prisma to MongoDB migration
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    logger.info("Unread count API temporarily disabled during migration");

    // Return 0 during migration
    return NextResponse.json({
      success: true,
      unreadCount: 0,
    });
  } catch (error) {
    logger.error("Failed to fetch unread notification count", error as Error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
