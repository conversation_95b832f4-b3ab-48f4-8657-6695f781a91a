import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/lib/auth";
// Use mongoose connection and models
import connectDB from "@/app/lib/mongoose";
import { User, Order, Review, WishlistItem, Address } from "@/app/lib/models";

// GET /api/users/[id]/stats - Get user statistics
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and accessing their own data or is admin
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    const isAdmin = (session.user as any)?.role === "ADMIN";
    const isOwnProfile = session.user.id === params.id;

    if (!isAdmin && !isOwnProfile) {
      return NextResponse.json(
        { success: false, error: "Forbidden" },
        { status: 403 },
      );
    }

    await connectDB();

    // Narrow lean types to avoid array|object unions
    type LeanUser = {
      _id: any;
      name?: string;
      email?: string;
      createdAt?: Date | string;
      role?: string;
    };

    type LeanRecentOrder = {
      _id: any;
      orderNumber?: string;
      status?: string;
      total?: number;
      createdAt?: Date | string;
    };

    // Get user with basic info
    const user = await User.findById(params.id, {
      _id: 1,
      name: 1,
      email: 1,
      createdAt: 1,
      role: 1,
    }).lean<LeanUser | null>();

    if (!user) {
      return NextResponse.json(
        { success: false, error: "User not found" },
        { status: 404 },
      );
    }

    // Get order statistics using aggregation pipeline
    const agg = await Order.aggregate([
      { $match: { userId: params.id, paymentStatus: "PAID" } },
      {
        $group: {
          _id: null,
          totalCount: { $sum: 1 },
          totalSum: { $sum: "$total" },
        },
      },
    ]);
    const orderStats = {
      _count: { id: agg[0]?.totalCount ?? 0 },
      _sum: { total: agg[0]?.totalSum ?? 0 },
    };

    // Get additional counts
    const [reviewCount, wishlistCount, addressCount] = await Promise.all([
      Review.countDocuments({ userId: params.id }),
      WishlistItem.countDocuments({ userId: params.id }),
      Address.countDocuments({ userId: params.id }),
    ]);

    // Get recent orders
    const recentOrders = await Order.find(
      { userId: params.id },
      { _id: 1, orderNumber: 1, status: 1, total: 1, createdAt: 1 }
    )
      .sort({ createdAt: -1 })
      .limit(5)
      .lean<LeanRecentOrder[]>();

    // Build strongly typed user block to avoid union/array inferences
    const userBlock = {
      id: String(user._id),
      name: user.name ?? undefined,
      email: user.email ?? undefined,
      joinDate: user.createdAt,
      role: user.role ?? undefined,
    };

    // Ensure recent orders are strictly typed objects (no FlattenMaps leakage)
    const recent = recentOrders.map((o) => ({
      id: String(o._id),
      orderNumber: o.orderNumber,
      status: o.status,
      total: o.total,
      createdAt: o.createdAt,
    }));

    const stats = {
      user: userBlock,
      orders: {
        total: orderStats._count.id || 0,
        totalSpent: orderStats._sum.total || 0,
        recent,
      },
      counts: {
        reviews: reviewCount,
        wishlist: wishlistCount,
        addresses: addressCount,
      },
      accountStatus: "ACTIVE", // Could be enhanced with more logic
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error("Error fetching user stats:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch user statistics" },
      { status: 500 },
    );
  }
}
