import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/app/lib/mongoose";
import { Category, Product } from "@/app/lib/models";

// GET /api/categories - List all categories
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Check if this is an admin request (for admin panel)
    const isAdminRequest = request.headers.get("x-admin-request") === "true";

    // Build where clause
    const where = isAdminRequest ? {} : { isActive: true };

    const categories = await Category.find(where).sort({ name: 1 }).lean();

    // Get product counts for each category
    const categoriesWithCounts = await Promise.all(
      categories.map(async (category) => {
        const productCount = await Product.countDocuments({
          categoryId: String(category._id),
        });
        return {
          ...category,
          _count: {
            products: productCount,
          },
        };
      }),
    );

    return NextResponse.json({
      success: true,
      data: categoriesWithCounts,
    });
  } catch (error) {
    console.error("Error fetching categories:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch categories" },
      { status: 500 },
    );
  }
}

// POST /api/categories - Create a new category
export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const { name, slug, description, parentId } = body;

    const category = await Category.create({
      name,
      slug,
      description,
      parentId: parentId || undefined,
    });

    // Get parent category if exists
    let parent = null;
    if (category.parentId) {
      parent = await Category.findById(category.parentId);
    }

    // Get counts
    const productCount = await Product.countDocuments({
      categoryId: String(category._id),
    });

    const categoryWithRelations = {
      ...category.toObject(),
      parent,
      _count: {
        products: productCount,
        productCategories: 0, // This would need a join table for many-to-many
      },
    };

    return NextResponse.json({
      success: true,
      data: categoryWithRelations,
      message: "Category created successfully",
    });
  } catch (error) {
    console.error("Error creating category:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create category" },
      { status: 500 },
    );
  }
}
