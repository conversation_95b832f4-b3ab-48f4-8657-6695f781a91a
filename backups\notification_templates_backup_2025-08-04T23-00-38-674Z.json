[{"id": "cmdka6qpm0000ulrgmr6tqjdu", "name": "order_placed", "type": "ORDER_PLACED", "title": "Order Placed Successfully", "message": "Your order #{orderNumber} has been placed successfully. We'll send you updates as your order progresses.", "emailSubject": "Order Confirmation - Herbalicious", "emailTemplate": "\n      <div style=\"max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;\">\n        <div style=\"text-align: center; margin-bottom: 30px;\">\n          <h1 style=\"color: #2d5a27; margin: 0;\">Herbalicious</h1>\n          <p style=\"color: #666; margin: 5px 0 0 0;\">Natural Skincare Essentials</p>\n        </div>\n        \n        <div style=\"background: #f0fdf4; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #2d5a27;\">\n          <h2 style=\"color: #2d5a27; margin: 0 0 15px 0;\">Order Placed Successfully!</h2>\n          <p style=\"color: #333; line-height: 1.6; margin: 0;\">Thank you for your order! We've received your order and will send you updates as it progresses.</p>\n        </div>\n        \n        <div style=\"background: #fff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 20px;\">\n          <h3 style=\"color: #333; margin: 0 0 15px 0;\">Order Details</h3>\n          <p><strong>Order Number:</strong> {orderNumber}</p>\n          <p><strong>Order Total:</strong> {currency} {amount}</p>\n          <p><strong>Items:</strong> {itemCount} item(s)</p>\n        </div>\n        \n        <div style=\"text-align: center; margin: 30px 0;\">\n          <a href=\"{baseUrl}/orders/{orderId}\" \n             style=\"background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;\">\n            View Order Details\n          </a>\n        </div>\n        \n        <div style=\"text-align: center; color: #999; font-size: 12px; margin-top: 30px;\">\n          <p>Thank you for choosing Herbalicious for your natural skincare needs!</p>\n        </div>\n      </div>\n    ", "isActive": true, "createdAt": "2025-07-26T13:23:34.706Z", "updatedAt": "2025-07-26T13:23:34.706Z"}, {"id": "cmdka6qzk0001ulrg307et5bt", "name": "order_shipped", "type": "ORDER_SHIPPED", "title": "Order Shipped", "message": "Great news! Your order #{orderNumber} has been shipped.", "emailSubject": "Your Order Has Shipped - Herbalicious", "emailTemplate": "\n      <div style=\"max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;\">\n        <div style=\"text-align: center; margin-bottom: 30px;\">\n          <h1 style=\"color: #2d5a27; margin: 0;\">Herbalicious</h1>\n          <p style=\"color: #666; margin: 5px 0 0 0;\">Natural Skincare Essentials</p>\n        </div>\n        \n        <div style=\"background: #f0fdf4; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #2d5a27;\">\n          <h2 style=\"color: #2d5a27; margin: 0 0 15px 0;\">Your Order Has Shipped! 📦</h2>\n          <p style=\"color: #333; line-height: 1.6; margin: 0;\">Great news! Your order is on its way to you.</p>\n        </div>\n        \n        <div style=\"background: #fff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 20px;\">\n          <h3 style=\"color: #333; margin: 0 0 15px 0;\">Shipping Details</h3>\n          <p><strong>Order Number:</strong> {orderNumber}</p>\n          <p><strong>Tracking Number:</strong> {trackingNumber}</p>\n          <p><strong>Carrier:</strong> {carrier}</p>\n          <p><strong>Estimated Delivery:</strong> {estimatedDelivery}</p>\n        </div>\n        \n        <div style=\"text-align: center; margin: 30px 0;\">\n          <a href=\"{baseUrl}/orders/{orderId}\" \n             style=\"background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin-right: 10px;\">\n            Track Your Order\n          </a>\n        </div>\n        \n        <div style=\"text-align: center; color: #999; font-size: 12px; margin-top: 30px;\">\n          <p>We can't wait for you to experience your natural skincare products!</p>\n        </div>\n      </div>\n    ", "isActive": true, "createdAt": "2025-07-26T13:23:35.072Z", "updatedAt": "2025-07-26T13:23:35.072Z"}, {"id": "cmdka6r3h0002ulrg2pf5v88u", "name": "order_delivered", "type": "ORDER_DELIVERED", "title": "Order Delivered", "message": "Your order #{orderNumber} has been delivered successfully! We hope you love your natural skincare products.", "emailSubject": "Order Delivered - Herbalicious", "emailTemplate": "\n      <div style=\"max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;\">\n        <div style=\"text-align: center; margin-bottom: 30px;\">\n          <h1 style=\"color: #2d5a27; margin: 0;\">Herbalicious</h1>\n          <p style=\"color: #666; margin: 5px 0 0 0;\">Natural Skincare Essentials</p>\n        </div>\n        \n        <div style=\"background: #f0fdf4; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #2d5a27;\">\n          <h2 style=\"color: #2d5a27; margin: 0 0 15px 0;\">Order Delivered! 🎉</h2>\n          <p style=\"color: #333; line-height: 1.6; margin: 0;\">Your order has been delivered successfully. We hope you love your natural skincare products!</p>\n        </div>\n        \n        <div style=\"background: #fff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 20px;\">\n          <h3 style=\"color: #333; margin: 0 0 15px 0;\">Delivery Details</h3>\n          <p><strong>Order Number:</strong> {orderNumber}</p>\n          <p><strong>Delivered At:</strong> {deliveredAt}</p>\n        </div>\n        \n        <div style=\"text-align: center; margin: 30px 0;\">\n          <a href=\"{baseUrl}/orders/{orderId}\" \n             style=\"background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin-right: 10px;\">\n            View Order\n          </a>\n          <a href=\"{baseUrl}/reviews/new?order={orderId}\" \n             style=\"background-color: #16a34a; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;\">\n            Leave a Review\n          </a>\n        </div>\n        \n        <div style=\"text-align: center; color: #999; font-size: 12px; margin-top: 30px;\">\n          <p>Thank you for choosing Herbalicious! We'd love to hear about your experience.</p>\n        </div>\n      </div>\n    ", "isActive": true, "createdAt": "2025-07-26T13:23:35.213Z", "updatedAt": "2025-07-26T13:23:35.213Z"}, {"id": "cmdka6r7n0003ulrg76muv9z9", "name": "price_drop_alert", "type": "PRICE_DROP_ALERT", "title": "Price Drop Alert!", "message": "Great news! {productName} is now {discountPercentage}% off!", "emailSubject": "Price Drop Alert - {productName} - Herbalicious", "emailTemplate": "\n      <div style=\"max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;\">\n        <div style=\"text-align: center; margin-bottom: 30px;\">\n          <h1 style=\"color: #2d5a27; margin: 0;\">Herbalicious</h1>\n          <p style=\"color: #666; margin: 5px 0 0 0;\">Natural Skincare Essentials</p>\n        </div>\n        \n        <div style=\"background: #fef3c7; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #f59e0b;\">\n          <h2 style=\"color: #92400e; margin: 0 0 15px 0;\">🔥 Price Drop Alert!</h2>\n          <p style=\"color: #333; line-height: 1.6; margin: 0;\">A product in your wishlist is now on sale!</p>\n        </div>\n        \n        <div style=\"background: #fff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 20px;\">\n          <h3 style=\"color: #333; margin: 0 0 15px 0;\">{productName}</h3>\n          <p style=\"color: #ef4444; font-size: 18px; margin: 0;\"><strong>Was:</strong> <span style=\"text-decoration: line-through;\">{currency} {oldPrice}</span></p>\n          <p style=\"color: #16a34a; font-size: 24px; margin: 5px 0;\"><strong>Now:</strong> {currency} {newPrice}</p>\n          <p style=\"color: #f59e0b; font-size: 16px; margin: 0;\"><strong>Save {discountPercentage}%!</strong></p>\n        </div>\n        \n        <div style=\"text-align: center; margin: 30px 0;\">\n          <a href=\"{baseUrl}/products/{productId}\" \n             style=\"background-color: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; font-size: 16px;\">\n            Shop Now - Limited Time!\n          </a>\n        </div>\n        \n        <div style=\"text-align: center; color: #999; font-size: 12px; margin-top: 30px;\">\n          <p>This price drop alert was sent because this product is in your wishlist.</p>\n          <p>Hurry, this offer may not last long!</p>\n        </div>\n      </div>\n    ", "isActive": true, "createdAt": "2025-07-26T13:23:35.363Z", "updatedAt": "2025-07-26T13:23:35.363Z"}, {"id": "cmdka6rbk0004ulrgxrs1g7k3", "name": "review_request", "type": "REVIEW_REQUEST", "title": "How was your experience?", "message": "We'd love to hear about your experience with your recent purchase. Your review helps other customers make informed decisions!", "emailSubject": "How was your experience? - <PERSON><PERSON><PERSON>", "emailTemplate": "\n      <div style=\"max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;\">\n        <div style=\"text-align: center; margin-bottom: 30px;\">\n          <h1 style=\"color: #2d5a27; margin: 0;\">Herbalicious</h1>\n          <p style=\"color: #666; margin: 5px 0 0 0;\">Natural Skincare Essentials</p>\n        </div>\n        \n        <div style=\"background: #f0fdf4; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #2d5a27;\">\n          <h2 style=\"color: #2d5a27; margin: 0 0 15px 0;\">How was your experience? ⭐</h2>\n          <p style=\"color: #333; line-height: 1.6; margin: 0;\">We hope you're loving your natural skincare products! Your feedback helps us improve and helps other customers make informed decisions.</p>\n        </div>\n        \n        <div style=\"background: #fff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 20px;\">\n          <h3 style=\"color: #333; margin: 0 0 15px 0;\">Order #{orderNumber}</h3>\n          <p style=\"color: #666; margin: 0;\">Products: {productNames}</p>\n        </div>\n        \n        <div style=\"text-align: center; margin: 30px 0;\">\n          <a href=\"{baseUrl}/reviews/new?order={orderId}\" \n             style=\"background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;\">\n            Write a Review\n          </a>\n        </div>\n        \n        <div style=\"text-align: center; color: #999; font-size: 12px; margin-top: 30px;\">\n          <p>Your honest review helps other customers discover the best natural skincare products.</p>\n        </div>\n      </div>\n    ", "isActive": true, "createdAt": "2025-07-26T13:23:35.504Z", "updatedAt": "2025-07-26T13:23:35.504Z"}]