// import { prisma } from './db'; // Temporarily disabled - using MongoDB now
import { sendEmail } from "./email";
import { logger } from "./logger";

// Define types locally to avoid import issues
export enum NotificationType {
  ORDER_PLACED = "ORDER_PLACED",
  ORDER_CONFIRMED = "ORDER_CONFIRMED",
  ORDER_PROCESSING = "ORDER_PROCESSING",
  ORDER_SHIPPED = "ORDER_SHIPPED",
  ORDER_DELIVERED = "ORDER_DELIVERED",
  ORDER_CANCELLED = "ORDER_CANCELLED",
  WISHLIST_ADDED = "WISHLIST_ADDED",
  WISHLIST_REMOVED = "WISHLIST_REMOVED",
  PRICE_DROP_ALERT = "PRICE_DROP_ALERT",
  REVIEW_REQUEST = "REVIEW_REQUEST",
  REVIEW_SUBMITTED = "REVIEW_SUBMITTED",
  ADMIN_MESSAGE = "ADMIN_MESSAGE",
  BROADCAST = "BROADCAST",
  PROMOTIONAL = "PROMOTIONAL",
  SYSTEM = "SYSTEM",
}

export interface NotificationData {
  orderId?: string;
  orderNumber?: string;
  productId?: string;
  productName?: string;
  amount?: number;
  currency?: string;
  [key: string]: any;
}

export interface CreateNotificationOptions {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: NotificationData;
  expiresAt?: Date;
  templateId?: string;
  sendEmail?: boolean;
}

export interface SendBroadcastOptions {
  type: NotificationType;
  title: string;
  message: string;
  data?: NotificationData;
  expiresAt?: Date;
  templateId?: string;
  sendEmail?: boolean;
  userIds?: string[];
}

class NotificationService {
  async createNotification(options: CreateNotificationOptions) {
    try {
      // TODO: Replace with MongoDB implementation
      logger.info(
        `Notification service temporarily disabled - would create notification for user ${options.userId}`,
      );
      return null;
    } catch (error) {
      logger.error("Error creating notification:", error as Error);
      throw error;
    }
  }

  async sendBroadcast(options: SendBroadcastOptions) {
    logger.info("Broadcast service temporarily disabled");
    return [];
  }

  async markAsRead(notificationId: string, userId: string) {
    logger.info("Mark as read service temporarily disabled");
    return null;
  }

  async markAllAsRead(userId: string) {
    logger.info("Mark all as read service temporarily disabled");
    return { count: 0 };
  }

  async getUserNotifications(userId: string, options: any = {}) {
    const { page = 1, limit = 20 } = options;
    logger.info("Get user notifications service temporarily disabled");
    return {
      notifications: [],
      total: 0,
      page,
      limit,
      totalPages: 0,
    };
  }

  async getUnreadCount(userId: string) {
    logger.info("Get unread count service temporarily disabled");
    return 0;
  }

  async cleanupOldNotifications(daysOld: number = 30) {
    logger.info("Cleanup notifications service temporarily disabled");
    return { count: 0 };
  }

  private async sendEmailNotification(notification: any) {
    logger.info("Email notification service temporarily disabled");
  }

  private shouldSendNotification(
    type: NotificationType,
    preferences: any,
  ): boolean {
    if (!preferences) return true;
    if (preferences.inAppNotifications === false) return false;

    switch (type) {
      case NotificationType.ORDER_PLACED:
      case NotificationType.ORDER_CONFIRMED:
      case NotificationType.ORDER_PROCESSING:
      case NotificationType.ORDER_SHIPPED:
      case NotificationType.ORDER_DELIVERED:
      case NotificationType.ORDER_CANCELLED:
        return preferences.orderNotifications !== false;

      case NotificationType.WISHLIST_ADDED:
      case NotificationType.WISHLIST_REMOVED:
        return preferences.wishlistNotifications !== false;

      case NotificationType.PRICE_DROP_ALERT:
        return preferences.priceDropAlerts !== false;

      case NotificationType.REVIEW_REQUEST:
      case NotificationType.REVIEW_SUBMITTED:
        return preferences.reviewNotifications !== false;

      case NotificationType.ADMIN_MESSAGE:
        return preferences.adminMessages !== false;

      case NotificationType.BROADCAST:
      case NotificationType.PROMOTIONAL:
        return preferences.broadcastMessages !== false;

      default:
        return true;
    }
  }

  private generateEmailContent(notification: any): string {
    const baseUrl = process.env.NEXTAUTH_URL || "http://localhost:3000";

    return `
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2d5a27; margin: 0;">Herbalicious</h1>
          <p style="color: #666; margin: 5px 0 0 0;">Natural Skincare Essentials</p>
        </div>
        
        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h2 style="color: #333; margin: 0 0 15px 0;">${notification.title}</h2>
          <p style="color: #666; line-height: 1.6; margin: 0;">${notification.message}</p>
        </div>
        
        ${
          notification.data && Object.keys(notification.data).length > 0
            ? `
          <div style="margin-bottom: 20px;">
            <h3 style="color: #333; margin: 0 0 10px 0;">Details:</h3>
            ${this.formatNotificationData(notification.data)}
          </div>
        `
            : ""
        }
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${baseUrl}" 
             style="background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Visit Herbalicious
          </a>
        </div>
        
        <div style="text-align: center; color: #999; font-size: 12px; margin-top: 30px;">
          <p>You received this notification because you have an account with Herbalicious.</p>
          <p>You can manage your notification preferences in your account settings.</p>
        </div>
      </div>
    `;
  }

  private formatNotificationData(data: NotificationData): string {
    let html = '<ul style="color: #666; line-height: 1.6;">';

    if (data.orderNumber) {
      html += `<li><strong>Order Number:</strong> ${data.orderNumber}</li>`;
    }
    if (data.productName) {
      html += `<li><strong>Product:</strong> ${data.productName}</li>`;
    }
    if (data.amount && data.currency) {
      html += `<li><strong>Amount:</strong> ${data.currency} ${data.amount}</li>`;
    }

    html += "</ul>";
    return html;
  }
}

export const notificationService = new NotificationService();
