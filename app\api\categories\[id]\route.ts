import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/app/lib/mongoose";
import { Category, Product, ProductCategory } from "@/app/lib/models";

// NOTE: For better TypeScript safety with lean() results, define a minimal shape
type LeanCategory = {
  _id: any;
  name?: string;
  slug?: string;
  description?: string;
  image?: string;
  isActive?: boolean;
  parentId?: string | null;
  createdAt?: Date | string;
  updatedAt?: Date | string;
} & Record<string, any>;

// GET /api/categories/[id] - Get a specific category
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    await connectDB();

    const category = (await Category.findById(params.id).lean()) as LeanCategory | null;

    if (!category) {
      return NextResponse.json(
        { success: false, error: "Category not found" },
        { status: 404 },
      );
    }

    // Fetch minimal product info (first image) for products directly in this category
    const products = await Product.find(
      { categoryId: params.id },
      { _id: 1, name: 1, slug: 1 } // adjust fields as needed
    ).lean();

    // Parent and children using parentId
    const parent =
      category.parentId
        ? ((await Category.findById(category.parentId).lean()) as LeanCategory | null)
        : null;
    const children = (await Category.find({ parentId: params.id }).lean()) as LeanCategory[];

    const productCount = await Product.countDocuments({ categoryId: params.id });

    const data = {
      ...category,
      products,
      parent,
      children,
      _count: {
        products: productCount,
      },
    };

    return NextResponse.json({
      success: true,
      data,
    });
  } catch (error) {
    console.error("Error fetching category:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch category" },
      { status: 500 },
    );
  }
}

// PATCH /api/categories/[id] - Update a category
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    await connectDB();
    const body = await request.json();
    const { name, slug, description, image, isActive, parentId } = body;

    const update: any = {};
    if (name !== undefined) update.name = name;
    if (slug !== undefined) update.slug = slug;
    if (description !== undefined) update.description = description;
    if (image !== undefined) update.image = image;
    if (typeof isActive === "boolean") update.isActive = isActive;
    if (parentId !== undefined) update.parentId = parentId;

    const category = (await Category.findByIdAndUpdate(
      params.id,
      { $set: update },
      { new: true, lean: true }
    )) as LeanCategory | null;

    if (!category) {
      return NextResponse.json(
        { success: false, error: "Category not found" },
        { status: 404 },
      );
    }

    const parent =
      category.parentId
        ? ((await Category.findById(category.parentId).lean()) as LeanCategory | null)
        : null;
    const productCount = await Product.countDocuments({ categoryId: params.id });

    return NextResponse.json({
      success: true,
      data: {
        ...category,
        parent,
        _count: { products: productCount },
      },
      message: "Category updated successfully",
    });
  } catch (error) {
    console.error("Error updating category:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update category" },
      { status: 500 },
    );
  }
}

// DELETE /api/categories/[id] - Delete a category
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    await connectDB();

    // Check if category has products (direct relationship)
    const productCount = await Product.countDocuments({ categoryId: params.id });

    // Check for products in many-to-many relationship if such a collection exists
    let productCategoryCount = 0;
    try {
      if (ProductCategory) {
        productCategoryCount = await ProductCategory.countDocuments({
          categoryId: params.id,
        });
      }
    } catch (_err) {
      // ProductCategory model may not be wired; skip
      console.log("ProductCategory collection not found, skipping many-to-many check");
    }

    const totalProductCount = productCount + productCategoryCount;

    if (totalProductCount > 0) {
      return NextResponse.json(
        {
          success: false,
          error: `Cannot delete category. ${totalProductCount} product(s) are still assigned to this category.`,
        },
        { status: 400 },
      );
    }

    // Check if category has child categories
    const childCount = await Category.countDocuments({ parentId: params.id });

    if (childCount > 0) {
      return NextResponse.json(
        {
          success: false,
          error: `Cannot delete category. ${childCount} subcategory(ies) exist under this category.`,
        },
        { status: 400 },
      );
    }

    // If all checks pass, delete the category
    await Category.deleteOne({ _id: params.id });

    return NextResponse.json({
      success: true,
      message: "Category deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting category:", error);

    // Handle Mongo duplicate/constraint-like situations
    return NextResponse.json(
      { success: false, error: "Failed to delete category" },
      { status: 500 },
    );
  }
}
