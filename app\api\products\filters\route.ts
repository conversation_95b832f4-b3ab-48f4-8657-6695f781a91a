import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/app/lib/mongoose";
import { Product, Category } from "@/app/lib/models";
// Inline a minimal asyncHandler to remove missing module error
const asyncHandler = <T extends (...args: any[]) => Promise<Response>>(handler: T) =>
  (async (...args: Parameters<T>) => {
    try {
      return await handler(...args);
    } catch (err) {
      console.error(err);
      return NextResponse.json({ success: false, error: "Internal Server Error" }, { status: 500 });
    }
  }) as T;

// GET /api/products/filters - Get optimized filter data
export const GET = asyncHandler(async (request: NextRequest) => {
  await connectDB();

  const { searchParams } = new URL(request.url);
  const category = searchParams.get("category");

  // Build base match clause for products
  type LeanCategoryId = { _id: any };
  const where: any = { isActive: true };
  if (category) {
    const categoryDoc = await Category.findOne({ slug: category }).select("_id").lean<LeanCategoryId | null>();
    if (categoryDoc?._id) {
      where.categoryId = String(categoryDoc._id);
    }
  }

  // Get optimized filter data in parallel
  const [priceRange, categories, totalCount] = await Promise.all([
    // Price range - using MongoDB aggregation
    Product.aggregate([
      { $match: where },
      {
        $group: {
          _id: null,
          minPrice: { $min: "$price" },
          maxPrice: { $max: "$price" },
        },
      },
    ]),

    // Categories with product counts
    Category.aggregate([
      { $match: { isActive: true } },
      {
        $lookup: {
          from: "products",
          localField: "_id",
          foreignField: "categoryId",
          as: "products",
          pipeline: [{ $match: { isActive: true } }],
        },
      },
      {
        $project: {
          id: { $toString: "$_id" },
          name: 1,
          slug: 1,
          description: 1,
          product_count: { $size: "$products" },
        },
      },
      { $sort: { name: 1 } },
    ]),

    // Total count
    Product.countDocuments(where),
  ]);

  // Extract price range from aggregation result
  const priceData = priceRange[0] || { minPrice: 0, maxPrice: 1000 };

  return NextResponse.json({
    success: true,
    data: {
      priceRange: {
        min: Number(priceData.minPrice) || 0,
        max: Number(priceData.maxPrice) || 1000,
      },
      categories,
      totalProducts: Number(totalCount),
    },
  });
});
