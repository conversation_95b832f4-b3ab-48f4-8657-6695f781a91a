import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../lib/auth";
import { uploadToR2, validateFile } from "../../../lib/r2";

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get("file") as File;
    const folder = (formData.get("folder") as string) || "uploads";

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    // Validate file
    const validation = validateFile(file);
    if (!validation.valid) {
      return NextResponse.json(
        {
          error: validation.error,
        },
        { status: 400 },
      );
    }

    // Upload to R2
    const result = await uploadToR2(file, folder);

    if (result.success && result.file) {
      return NextResponse.json({
        success: true,
        file: result.file,
        message: "File uploaded successfully to R2",
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          error: result.error || "Upload failed",
        },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error("Error uploading file to R2:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to upload file to R2",
      },
      { status: 500 },
    );
  }
}
