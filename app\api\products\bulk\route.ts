import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/app/lib/mongoose";
import {
  Product,
  OrderItem,
  Order,
  ProductImage,
  ProductVariant,
  ProductCategory,
  Review,
  WishlistItem,
} from "@/app/lib/models";
// Inline minimal asyncHandler since '@/app/lib/async-handler' does not exist
const asyncHandler = <T extends (...args: any[]) => Promise<Response>>(handler: T) =>
  (async (...args: Parameters<T>) => {
    try {
      return await handler(...args);
    } catch (err) {
      console.error(err);
      return NextResponse.json({ success: false, error: "Internal Server Error" }, { status: 500 });
    }
  }) as T;

// POST /api/products/bulk - Bulk operations on products
export const POST = asyncHandler(async (request: NextRequest) => {
  await connectDB();

  const body = await request.json();
  const { action, productIds } = body || {};

  if (!action || !Array.isArray(productIds) || productIds.length === 0) {
    return NextResponse.json(
      {
        success: false,
        error: "Invalid request. Action and productIds array are required.",
      },
      { status: 400 },
    );
  }

  switch (action) {
    case "delete":
      return await handleBulkDelete(productIds as string[]);
    case "activate":
      return await handleBulkActivate(productIds as string[], true);
    case "deactivate":
      return await handleBulkActivate(productIds as string[], false);
    case "feature":
      return await handleBulkFeature(productIds as string[], true);
    case "unfeature":
      return await handleBulkFeature(productIds as string[], false);
    default:
      return NextResponse.json(
        {
          success: false,
          error:
            "Invalid action. Supported actions: delete, activate, deactivate, feature, unfeature",
        },
        { status: 400 },
      );
  }
});

async function handleBulkDelete(productIds: string[]) {
  const results = {
    success: 0,
    softDeleted: 0,
    failed: 0,
    errors: [] as string[],
  };

  for (const productId of productIds) {
    try {
      // Determine if product has order history
      const orderItemCount = await OrderItem.countDocuments({ productId });

      if (orderItemCount > 0) {
        // Soft delete by setting isActive to false and prefixing name
        interface LeanProductName {
          _id?: any;
          name?: string;
        }
        const product = await Product.findById(productId).select("name").lean<LeanProductName | null>();
        if (product) {
          const today = new Date().toISOString().split("T")[0];
          await Product.updateOne(
            { _id: productId },
            {
              $set: {
                isActive: false,
                name: `[DELETED] ${today} - ${product?.name ?? ""}`,
              },
            },
          );
          results.softDeleted++;
        }
      } else {
        // Hard delete - remove related collections first
        await ProductImage.deleteMany({ productId });
        await ProductVariant.deleteMany({ productId });
        await ProductCategory.deleteMany({ productId });
        await Review.deleteMany({ productId });
        // If FAQs are embedded in Product, they are removed with the product document.
        // If a separate ProductFAQ model exists elsewhere, add its deletion here when defined.
        await WishlistItem.deleteMany({ productId });
        await Product.deleteOne({ _id: productId });
        results.success++;
      }
    } catch (error: any) {
      console.error(`Error deleting product ${productId}:`, error);
      results.failed++;
      results.errors.push(
        `Failed to delete product ${productId}: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      );
    }
  }

  let message = `Bulk delete completed. `;
  if (results.success > 0) {
    message += `${results.success} products deleted permanently. `;
  }
  if (results.softDeleted > 0) {
    message += `${results.softDeleted} products deactivated (had order history). `;
  }
  if (results.failed > 0) {
    message += `${results.failed} products failed to delete.`;
  }

  return NextResponse.json({
    success: true,
    message: message.trim(),
    data: results,
  });
}

async function handleBulkActivate(productIds: string[], isActive: boolean) {
  try {
    const result = await Product.updateMany(
      { _id: { $in: productIds } },
      { $set: { isActive } },
    );

    return NextResponse.json({
      success: true,
      message: `${result.modifiedCount ?? 0} products ${isActive ? "activated" : "deactivated"} successfully`,
      data: { updated: result.modifiedCount ?? 0 },
    });
  } catch (error) {
    console.error("Error in bulk activate/deactivate:", error);
    return NextResponse.json(
      {
        success: false,
        error: `Failed to ${isActive ? "activate" : "deactivate"} products`,
      },
      { status: 500 },
    );
  }
}

async function handleBulkFeature(productIds: string[], isFeatured: boolean) {
  try {
    const result = await Product.updateMany(
      { _id: { $in: productIds } },
      { $set: { isFeatured } },
    );

    return NextResponse.json({
      success: true,
      message: `${result.modifiedCount ?? 0} products ${isFeatured ? "featured" : "unfeatured"} successfully`,
      data: { updated: result.modifiedCount ?? 0 },
    });
  } catch (error) {
    console.error("Error in bulk feature/unfeature:", error);
    return NextResponse.json(
      {
        success: false,
        error: `Failed to ${isFeatured ? "feature" : "unfeature"} products`,
      },
      { status: 500 },
    );
  }
}
