import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../lib/auth";
import { logger } from "../../../lib/logger";

/**
 * POST /api/notifications/price-drop-check
 * Check for price drops on wishlisted items and send notifications
 * This endpoint can be called by a cron job or admin action
 *
 * NOTE: This endpoint is temporarily disabled during Prisma to MongoDB migration
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Only allow admin users to trigger price drop checks
    if (!session?.user?.id || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 401 },
      );
    }

    logger.info("Price drop check API temporarily disabled during migration");

    // Return success with empty stats during migration
    return NextResponse.json({
      success: true,
      message: "Price drop check temporarily disabled during migration",
      stats: {
        totalItems: 0,
        notificationsSent: 0,
      },
    });
  } catch (error) {
    logger.error("Failed to check for price drops", error as Error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
