"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import {
  CheckCircle,
  Package,
  Truck,
  Home,
  Mail,
  Phone,
  Tag,
  MessageCircle,
} from "lucide-react";

interface OrderItem {
  id: string;
  quantity: number;
  price: number;
  total: number;
  product: {
    id: string;
    name: string;
  };
}

interface OrderAddress {
  firstName: string;
  lastName: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
}

interface Order {
  id: string;
  orderNumber: string;
  status: string;
  paymentStatus: string;
  paymentMethod?: string;
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  couponDiscount: number;
  couponCodes: string[];
  total: number;
  currency: string;
  notes?: string;
  createdAt: string;
  items: OrderItem[];
  address: OrderAddress;
}

const OrderConfirmation: React.FC = () => {
  const searchParams = useSearchParams();
  const orderId = searchParams.get("orderId");
  const [orderData, setOrderData] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrder = async () => {
      if (!orderId) {
        setError("Order ID not found");
        setLoading(false);
        return;
      }

      try {
        const response = await fetch(`/api/orders/${orderId}`);
        if (!response.ok) {
          throw new Error("Failed to fetch order details");
        }
        const data = await response.json();
        setOrderData(data.order);
      } catch (err) {
        setError((err as Error).message);
      } finally {
        setLoading(false);
      }
    };

    fetchOrder();
  }, [orderId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-green-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading order details...</p>
        </div>
      </div>
    );
  }

  if (error || !orderData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Package className="w-10 h-10 text-red-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-800 mb-2">
            Order Not Found
          </h1>
          <p className="text-gray-600 mb-6">
            {error || "Unable to load order details"}
          </p>
          <Link
            href="/shop"
            className="bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors"
          >
            Continue Shopping
          </Link>
        </div>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const estimatedDelivery = new Date(
    Date.now() + 5 * 24 * 60 * 60 * 1000,
  ).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  const deliverySteps = [
    {
      icon: CheckCircle,
      label: "Order Confirmed",
      status: "completed",
      date: "Today",
    },
    { icon: Package, label: "Processing", status: "current", date: "1-2 days" },
    { icon: Truck, label: "Shipped", status: "pending", date: "2-3 days" },
    { icon: Home, label: "Delivered", status: "pending", date: "5-7 days" },
  ];

  return (
    <div className="lg:grid lg:grid-cols-12 lg:gap-8">
      {/* Mobile Layout */}
      <div className="lg:hidden">
        <div className="px-4 py-8">
          {/* Success Header */}
          <div className="text-center mb-8">
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-10 h-10 text-green-600" />
            </div>
            <h1 className="text-2xl font-bold text-gray-800 mb-2">
              Order Confirmed!
            </h1>
            <p className="text-gray-600">Thank you for your purchase</p>
          </div>

          {/* Order Details */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6">
            <h2 className="font-semibold text-gray-800 mb-4">Order Details</h2>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Order Number</span>
                <span className="font-medium text-gray-800">
                  {orderData.orderNumber}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Order Date</span>
                <span className="font-medium text-gray-800">
                  {formatDate(orderData.createdAt)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Total Amount</span>
                <span className="font-bold text-gray-900">
                  ₹{orderData.total.toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Estimated Delivery</span>
                <span className="font-medium text-green-600">
                  {estimatedDelivery}
                </span>
              </div>
            </div>
          </div>

          {/* Delivery Timeline */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6">
            <h2 className="font-semibold text-gray-800 mb-4">
              Delivery Timeline
            </h2>
            <div className="space-y-4">
              {deliverySteps.map((step, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center ${
                      step.status === "completed"
                        ? "bg-green-100 text-green-600"
                        : step.status === "current"
                          ? "bg-blue-100 text-blue-600"
                          : "bg-gray-100 text-gray-400"
                    }`}
                  >
                    <step.icon className="w-5 h-5" />
                  </div>
                  <div className="flex-1">
                    <h3
                      className={`font-medium ${
                        step.status === "completed" || step.status === "current"
                          ? "text-gray-800"
                          : "text-gray-500"
                      }`}
                    >
                      {step.label}
                    </h3>
                    <p className="text-sm text-gray-500">{step.date}</p>
                  </div>
                  {step.status === "completed" && (
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Order Items */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6">
            <h2 className="font-semibold text-gray-800 mb-4">Items Ordered</h2>
            <div className="space-y-3">
              {orderData.items.map((item) => (
                <div key={item.id} className="flex justify-between">
                  <div>
                    <span className="font-medium text-gray-800">
                      {item.product.name}
                    </span>
                    <span className="text-gray-600 ml-2">x{item.quantity}</span>
                  </div>
                  <span className="font-medium text-gray-800">
                    ₹{item.total.toFixed(2)}
                  </span>
                </div>
              ))}

              {/* Show applied coupons if any */}
              {orderData.couponCodes.length > 0 && (
                <div className="border-t border-gray-200 pt-3 mt-3">
                  <div className="space-y-2">
                    <div className="flex justify-between text-gray-600">
                      <span>Subtotal</span>
                      <span>₹{orderData.subtotal.toFixed(2)}</span>
                    </div>
                    {orderData.couponCodes.map((code, index) => (
                      <div
                        key={index}
                        className="flex justify-between text-green-600"
                      >
                        <span className="flex items-center space-x-1">
                          <Tag className="w-3 h-3" />
                          <span>{code}</span>
                        </span>
                        <span>
                          -₹
                          {(
                            orderData.couponDiscount /
                            orderData.couponCodes.length
                          ).toFixed(2)}
                        </span>
                      </div>
                    ))}
                    {orderData.tax > 0 && (
                      <div className="flex justify-between text-gray-600">
                        <span>Tax</span>
                        <span>₹{orderData.tax.toFixed(2)}</span>
                      </div>
                    )}
                    {orderData.shipping > 0 && (
                      <div className="flex justify-between text-gray-600">
                        <span>Shipping</span>
                        <span>₹{orderData.shipping.toFixed(2)}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}
              <div className="border-t border-gray-200 pt-3">
                <div className="flex justify-between font-bold text-gray-900">
                  <span>Total</span>
                  <span>₹{orderData.total.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Shipping Address */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6">
            <h2 className="font-semibold text-gray-800 mb-4">
              Shipping Address
            </h2>
            <div className="text-gray-600">
              <p className="font-medium text-gray-800">
                {orderData.address.firstName} {orderData.address.lastName}
              </p>
              <p>{orderData.address.address1}</p>
              {orderData.address.address2 && (
                <p>{orderData.address.address2}</p>
              )}
              <p>
                {orderData.address.city}, {orderData.address.state}{" "}
                {orderData.address.postalCode}
              </p>
              <p>{orderData.address.country}</p>
              {orderData.address.phone && (
                <p>Phone: {orderData.address.phone}</p>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Link
              href="/order-history"
              className="w-full bg-green-600 text-white py-3 rounded-2xl font-semibold hover:bg-green-700 transition-colors flex items-center justify-center"
            >
              View Order History
            </Link>
            <Link
              href="/shop"
              className="w-full border border-gray-300 text-gray-700 py-3 rounded-2xl font-medium hover:bg-gray-50 transition-colors flex items-center justify-center"
            >
              Continue Shopping
            </Link>
          </div>

          {/* Contact Support */}
          <div className="mt-8 text-center">
            <p className="text-gray-600 mb-4">Need help with your order?</p>
            <div className="flex justify-center space-x-4">
              <a
                href="mailto:<EMAIL>"
                className="flex items-center space-x-2 text-green-600 hover:text-green-700"
              >
                <Mail className="w-4 h-4" />
                <span>Email</span>
              </a>
              <a
                href="tel:+1234567890"
                className="flex items-center space-x-2 text-green-600 hover:text-green-700"
              >
                <Phone className="w-4 h-4" />
                <span>Call</span>
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:block lg:col-span-12">
        <div className="py-12">
          {/* Success Header */}
          <div className="text-center mb-12">
            <div className="w-32 h-32 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="w-16 h-16 text-green-600" />
            </div>
            <h1 className="text-4xl font-bold text-gray-800 mb-4">
              Order Confirmed!
            </h1>
            <p className="text-xl text-gray-600">
              Thank you for choosing Herbalicious. Your order has been
              successfully placed.
            </p>
          </div>

          <div className="grid grid-cols-3 gap-8">
            {/* Left Column - Order Details */}
            <div className="col-span-2 space-y-8">
              {/* Order Summary */}
              <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
                <h2 className="text-2xl font-semibold text-gray-800 mb-6">
                  Order Summary
                </h2>
                <div className="grid grid-cols-2 gap-8 mb-6">
                  <div className="space-y-4">
                    <div>
                      <span className="text-gray-600">Order Number</span>
                      <p className="font-semibold text-gray-800 text-lg">
                        {orderData.orderNumber}
                      </p>
                    </div>
                    <div>
                      <span className="text-gray-600">Order Date</span>
                      <p className="font-medium text-gray-800">
                        {formatDate(orderData.createdAt)}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <span className="text-gray-600">Total Amount</span>
                      <p className="font-bold text-gray-900 text-xl">
                        ₹{orderData.total.toFixed(2)}
                      </p>
                    </div>
                    <div>
                      <span className="text-gray-600">Estimated Delivery</span>
                      <p className="font-medium text-green-600">
                        {estimatedDelivery}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Order Items */}
                <div className="border-t border-gray-200 pt-6">
                  <h3 className="font-semibold text-gray-800 mb-4">
                    Items Ordered
                  </h3>
                  <div className="space-y-4">
                    {orderData.items.map((item) => (
                      <div
                        key={item.id}
                        className="flex justify-between items-center"
                      >
                        <div>
                          <span className="font-medium text-gray-800">
                            {item.product.name}
                          </span>
                          <span className="text-gray-600 ml-3">
                            Quantity: {item.quantity}
                          </span>
                        </div>
                        <span className="font-semibold text-gray-800">
                          ₹{item.total.toFixed(2)}
                        </span>
                      </div>
                    ))}

                    {/* Show applied coupons if any */}
                    {orderData.couponCodes.length > 0 && (
                      <div className="border-t border-gray-200 pt-4">
                        <div className="space-y-3">
                          <div className="flex justify-between text-gray-600">
                            <span>Subtotal</span>
                            <span>₹{orderData.subtotal.toFixed(2)}</span>
                          </div>
                          {orderData.couponCodes.map((code, index) => (
                            <div
                              key={index}
                              className="flex justify-between text-green-600"
                            >
                              <span className="flex items-center space-x-2">
                                <Tag className="w-4 h-4" />
                                <span>Coupon: {code}</span>
                              </span>
                              <span>
                                -₹
                                {(
                                  orderData.couponDiscount /
                                  orderData.couponCodes.length
                                ).toFixed(2)}
                              </span>
                            </div>
                          ))}
                          {orderData.tax > 0 && (
                            <div className="flex justify-between text-gray-600">
                              <span>Tax</span>
                              <span>₹{orderData.tax.toFixed(2)}</span>
                            </div>
                          )}
                          {orderData.shipping > 0 && (
                            <div className="flex justify-between text-gray-600">
                              <span>Shipping</span>
                              <span>₹{orderData.shipping.toFixed(2)}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    <div className="border-t border-gray-200 pt-4">
                      <div className="flex justify-between items-center">
                        <span className="text-xl font-bold text-gray-900">
                          Total
                        </span>
                        <span className="text-xl font-bold text-gray-900">
                          ₹{orderData.total.toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Delivery Timeline */}
              <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
                <h2 className="text-2xl font-semibold text-gray-800 mb-6">
                  Delivery Timeline
                </h2>
                <div className="space-y-6">
                  {deliverySteps.map((step, index) => (
                    <div key={index} className="flex items-center space-x-6">
                      <div
                        className={`w-12 h-12 rounded-full flex items-center justify-center ${
                          step.status === "completed"
                            ? "bg-green-100 text-green-600"
                            : step.status === "current"
                              ? "bg-blue-100 text-blue-600"
                              : "bg-gray-100 text-gray-400"
                        }`}
                      >
                        <step.icon className="w-6 h-6" />
                      </div>
                      <div className="flex-1">
                        <h3
                          className={`font-semibold text-lg ${
                            step.status === "completed" ||
                            step.status === "current"
                              ? "text-gray-800"
                              : "text-gray-500"
                          }`}
                        >
                          {step.label}
                        </h3>
                        <p className="text-gray-500">{step.date}</p>
                      </div>
                      {step.status === "completed" && (
                        <CheckCircle className="w-6 h-6 text-green-600" />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Column - Shipping & Actions */}
            <div className="col-span-1 space-y-8">
              {/* Shipping Address */}
              <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">
                  Shipping Address
                </h2>
                <div className="text-gray-600 space-y-1">
                  <p className="font-medium text-gray-800">
                    {orderData.address.firstName} {orderData.address.lastName}
                  </p>
                  <p>{orderData.address.address1}</p>
                  {orderData.address.address2 && (
                    <p>{orderData.address.address2}</p>
                  )}
                  <p>
                    {orderData.address.city}, {orderData.address.state}{" "}
                    {orderData.address.postalCode}
                  </p>
                  <p>{orderData.address.country}</p>
                  {orderData.address.phone && (
                    <p className="text-sm">Phone: {orderData.address.phone}</p>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-4">
                <Link
                  href="/order-history"
                  className="w-full bg-green-600 text-white py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors flex items-center justify-center text-lg"
                >
                  View Order History
                </Link>
                <Link
                  href="/shop"
                  className="w-full border border-gray-300 text-gray-700 py-4 rounded-2xl font-medium hover:bg-gray-50 transition-colors flex items-center justify-center text-lg"
                >
                  Continue Shopping
                </Link>
              </div>

              {/* Contact Support */}
              <div className="bg-gray-50 rounded-2xl p-6">
                <h3 className="font-semibold text-gray-800 mb-4">Need Help?</h3>
                <p className="text-gray-600 mb-4">
                  Our customer support team is here to help with any questions
                  about your order.
                </p>
                <div className="space-y-3">
                  <a
                    href="mailto:<EMAIL>"
                    className="flex items-center space-x-3 text-green-600 hover:text-green-700 transition-colors"
                  >
                    <Mail className="w-5 h-5" />
                    <span><EMAIL></span>
                  </a>
                  <a
                    href="tel:+919987810707"
                    className="flex items-center space-x-3 text-green-600 hover:text-green-700 transition-colors"
                  >
                    <Phone className="w-5 h-5" />
                    <span>+91 99878 10707</span>
                  </a>
                  <a
                    href="https://wa.me/919987810707"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center space-x-3 text-green-600 hover:text-green-700 transition-colors"
                  >
                    <MessageCircle className="w-5 h-5" />
                    <span>WhatsApp Support</span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderConfirmation;
