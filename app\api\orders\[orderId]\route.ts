import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../lib/auth";
import connectDB from "../../../lib/mongoose";
import { Order, OrderItem, Address, Product, User } from "../../../lib/models";
import {
  handleApiError,
  AuthenticationError,
  ValidationError,
  NotFoundError,
  asyncHandler,
} from "../../../lib/errors";
import { logger } from "../../../lib/logger";
import { orderNotifications } from "../../../lib/notification-helpers";

const updateOrderSchema = z.object({
  status: z
    .enum([
      "PENDING",
      "CONFIRMED",
      "PROCESSING",
      "SHIPPED",
      "DELIVERED",
      "CANCELLED",
      "REFUNDED",
    ])
    .optional(),
  paymentStatus: z.enum(["PENDING", "PAID", "FAILED", "REFUNDED"]).optional(),
  trackingNumber: z.string().optional(),
  notes: z.string().optional(),
});

// GET /api/orders/[orderId] - Get single order details
export const GET = asyncHandler(
  async (request: NextRequest, { params }: { params: { orderId: string } }) => {
    logger.apiRequest("GET", `/api/orders/${params.orderId}`);

    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      throw new AuthenticationError("Authentication required");
    }

    const isAdmin = session.user.role === "ADMIN";

    try {
      await connectDB();

      // Find by id or orderNumber with explicit lean typing
      type LeanOrder = {
        _id: any;
        userId: string;
        orderNumber?: string;
        status: string;
        paymentMethod?: string;
        paymentStatus?: string;
        addressId?: string | null;
        updatedAt?: Date | string;
      };

      type LeanOrderItem = {
        _id: any;
        orderId: string;
        productId: string;
        quantity: number;
        price: number;
        variantId?: string | null;
      };

      type LeanProductSummary = {
        _id: any;
        name: string;
        slug: string;
        price: number;
      };

      type LeanAddress = {
        _id: any;
        userId: string;
      } & Record<string, any>;

      type LeanUser = {
        _id: any;
        name?: string;
        email?: string;
        phone?: string;
      };

      const isAdminOr = isAdmin ? {} : { userId: session.user.id };
      const order =
        (await Order.findOne({ _id: params.orderId, ...isAdminOr }).lean<LeanOrder | null>()) ||
        (await Order.findOne({ orderNumber: params.orderId, ...isAdminOr }).lean<LeanOrder | null>());

      if (!order) {
        throw new NotFoundError("Order not found");
      }

      const orderIdStr = String((order as { _id: any })._id);

      // Populate items with product summary
      const rawItems = await OrderItem.find({ orderId: orderIdStr }).lean<LeanOrderItem[]>();
      const items = await Promise.all(
        rawItems.map(async (item) => {
          const product = await Product.findById(item.productId)
            .select("_id name slug price")
            .lean<LeanProductSummary | null>();
          return {
            ...item,
            product: product
              ? {
                  id: product._id,
                  name: product.name,
                  slug: product.slug,
                  price: product.price,
                }
              : null,
          };
        })
      );

      // Address fetch (by addressId saved on order)
      const address: LeanAddress | null = order.addressId
        ? await Address.findById(order.addressId).lean<LeanAddress | null>()
        : await Address.findOne({ userId: order.userId }).lean<LeanAddress | null>();

      // Admin-only user info
      const user: LeanUser | null = isAdmin
        ? await User.findById(order.userId).select("_id name email phone").lean<LeanUser | null>()
        : null;

      const responseOrder: any = {
        ...order,
        items,
        address,
        user: user
          ? {
              id: user._id,
              name: user.name,
              email: user.email,
              phone: user.phone,
            }
          : undefined,
      };


      logger.info("Order retrieved successfully", {
        orderId: params.orderId,
        userId: session.user.id,
        isAdmin,
      });

      return NextResponse.json({
        success: true,
        order: responseOrder,
      });
    } catch (error) {
      logger.error("Failed to retrieve order", error as Error);
      throw error;
    }
  },
);

// PATCH /api/orders/[orderId] - Update order (admin only)
export const PATCH = asyncHandler(
  async (request: NextRequest, { params }: { params: { orderId: string } }) => {
    logger.apiRequest("PATCH", `/api/orders/${params.orderId}`);

    // Check authentication and admin role
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== "ADMIN") {
      throw new AuthenticationError("Admin access required");
    }

    const body = await request.json();
    const validatedData = updateOrderSchema.parse(body);

    // Validate that tracking number is provided when changing status to SHIPPED
    if (validatedData.status === "SHIPPED" && !validatedData.trackingNumber) {
      throw new ValidationError(
        "Tracking number is required when marking order as shipped",
      );
    }

    try {
      await connectDB();

      // Get current order - support both ID and order number
      type LeanOrderForPatch = {
        _id: any;
        userId: string;
        orderNumber?: string;
        status: string;
        paymentMethod?: string;
        paymentStatus?: string;
        total?: number;
        currency?: string;
        estimatedDelivery?: string | Date;
      };

      const currentOrder =
        (await Order.findOne({ _id: params.orderId }).lean<LeanOrderForPatch>()) ||
        (await Order.findOne({ orderNumber: params.orderId }).lean<LeanOrderForPatch>());

      if (!currentOrder) {
        throw new NotFoundError("Order not found");
      }

      const updateDoc: any = { updatedAt: new Date() };
      if (validatedData.status) updateDoc.status = validatedData.status;
      if (validatedData.paymentStatus) updateDoc.paymentStatus = validatedData.paymentStatus;
      if (validatedData.trackingNumber) updateDoc.trackingNumber = validatedData.trackingNumber;
      if (validatedData.notes) updateDoc.notes = validatedData.notes;

      await Order.updateOne({ _id: currentOrder._id }, { $set: updateDoc });

      const updatedOrder = await Order.findById(currentOrder._id).lean<LeanOrderForPatch | null>();

      // Send notifications based on status change
      if (validatedData.status && validatedData.status !== currentOrder.status) {
        try {
          const notifOrderId = updatedOrder ? String(updatedOrder._id) : String(currentOrder._id);
          const notifOrderNumber = updatedOrder?.orderNumber ?? currentOrder.orderNumber;

          switch (validatedData.status) {
            case "CONFIRMED":
              await orderNotifications.orderConfirmed(currentOrder.userId, {
                orderId: notifOrderId,
                orderNumber: notifOrderNumber as any,
              });
              break;
            case "PROCESSING":
              await orderNotifications.orderProcessing(currentOrder.userId, {
                orderId: notifOrderId,
                orderNumber: notifOrderNumber as any,
              });
              break;
            case "SHIPPED":
              await orderNotifications.orderShipped(currentOrder.userId, {
                orderId: notifOrderId,
                orderNumber: notifOrderNumber as any,
                estimatedDelivery: (updatedOrder?.estimatedDelivery as any) || undefined,
              });
              break;
            case "DELIVERED":
              await orderNotifications.orderDelivered(currentOrder.userId, {
                orderId: notifOrderId,
                orderNumber: notifOrderNumber as any,
              });
              // Update payment status to PAID for COD orders
              if (updatedOrder?.paymentMethod === "COD" && updatedOrder?.paymentStatus !== "PAID") {
                await Order.updateOne(
                  { _id: currentOrder._id },
                  { $set: { paymentStatus: "PAID" } }
                );
              }
              break;
            case "CANCELLED":
              await orderNotifications.orderCancelled(currentOrder.userId, {
                orderId: notifOrderId,
                orderNumber: notifOrderNumber as any,
                reason: validatedData.notes,
              });
              break;
            case "REFUNDED":
              await orderNotifications.orderCancelled(currentOrder.userId, {
                orderId: notifOrderId,
                orderNumber: notifOrderNumber as any,
                reason: validatedData.notes || "Order has been refunded",
                refundAmount: (updatedOrder?.total as any),
                currency: (updatedOrder?.currency as any),
              });
              break;
          }
        } catch (notificationError) {
          logger.error(
            "Failed to send status update notification",
            notificationError as Error,
          );
          // Don't fail the update if notification fails
        }
      }

      logger.info("Order updated successfully", {
        orderId: params.orderId,
        updates: validatedData,
        adminId: session.user.id,
      });

      return NextResponse.json({
        success: true,
        order: updatedOrder,
      });
    } catch (error) {
      logger.error("Failed to update order", error as Error);
      throw error;
    }
  },
);

// DELETE /api/orders/[orderId] - Cancel order
export const DELETE = asyncHandler(
  async (request: NextRequest, { params }: { params: { orderId: string } }) => {
    logger.apiRequest("DELETE", `/api/orders/${params.orderId}`);

    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      throw new AuthenticationError("Authentication required");
    }

    const isAdmin = session.user.role === "ADMIN";

    try {
      await connectDB();

      // Get order - support both ID and order number, and user scoping for non-admins
      type LeanOrderForDelete = {
        _id: any;
        userId: string;
        orderNumber?: string;
        status: string;
      };
      const order =
        (await Order.findOne({
          _id: params.orderId,
          ...(isAdmin ? {} : { userId: session.user.id }),
        }).lean<LeanOrderForDelete | null>()) ||
        (await Order.findOne({
          orderNumber: params.orderId,
          ...(isAdmin ? {} : { userId: session.user.id }),
        }).lean<LeanOrderForDelete | null>());

      if (!order) {
        throw new NotFoundError("Order not found");
      }

      // Check if order can be cancelled
      if (!["PENDING", "CONFIRMED"].includes(order.status as any)) {
        throw new ValidationError(
          "Order cannot be cancelled in current status",
        );
      }

      // Update order status to cancelled
      await Order.updateOne(
        { _id: order._id },
        { $set: { status: "CANCELLED", updatedAt: new Date() } }
      );

      // Send cancellation notification
      try {
        await orderNotifications.orderCancelled(order.userId, {
          orderId: String(order._id),
          orderNumber: order.orderNumber as any,
          reason: "Cancelled by " + (isAdmin ? "admin" : "customer"),
        });
      } catch (notificationError) {
        logger.error(
          "Failed to send cancellation notification",
          notificationError as Error,
        );
      }

      logger.info("Order cancelled successfully", {
        orderId: params.orderId,
        userId: session.user.id,
        isAdmin,
      });

      return NextResponse.json({
        success: true,
        message: "Order cancelled successfully",
      });
    } catch (error) {
      logger.error("Failed to cancel order", error as Error);
      throw error;
    }
  },
);
