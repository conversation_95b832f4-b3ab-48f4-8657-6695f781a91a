import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../lib/auth";
import connectDB from "../../../lib/mongoose";
import { Coupon, User, Order } from "../../../lib/models";
import { logger } from "../../../lib/logger";

type CouponUpdateInput = {
  code?: string;
  name: string;
  description?: string | null;
  type: string;
  discountType: "PERCENTAGE" | "AMOUNT";
  discountValue: number;
  minimumAmount?: number | null;
  maximumDiscount?: number | null;
  usageLimit?: number | null;
  userUsageLimit?: number | null;
  isActive?: boolean;
  isStackable?: boolean;
  showInModule?: boolean;
  validFrom?: string | Date | null;
  validUntil?: string | Date | null;
  applicableProducts?: string[];
  applicableCategories?: string[];
  excludedProducts?: string[];
  excludedCategories?: string[];
  customerSegments?: string[];
};

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || (session.user as any).role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await connectDB();

    // Fetch coupon and its usages with user and order summaries
    type LeanCoupon = {
      _id: any;
      code: string;
      name: string;
    };
    const coupon = await Coupon.findById(params.id).lean<LeanCoupon | null>();
    if (!coupon) {
      return NextResponse.json({ error: "Coupon not found" }, { status: 404 });
    }

    // CouponUsage model is not defined; derive usages from orders that include this coupon code
    const usages = await Order.find({ couponCodes: { $in: [String(coupon.code).toUpperCase()] } })
      .select("_id userId orderNumber total createdAt")
      .lean();

    // Hydrate user and order information for usages
    const userIds = Array.from(new Set(usages.map((u: any) => String(u.userId))));
    const orderIds = Array.from(new Set(usages.map((u: any) => String(u._id))));

    const [users, orders] = await Promise.all([
      User.find({ _id: { $in: userIds } })
        .select("_id name email")
        .lean(),
      Order.find({ _id: { $in: orderIds } })
        .select("_id orderNumber total")
        .lean(),
    ]);

    const userById = new Map(users.map((u: any) => [String(u._id), u]));
    const orderById = new Map(orders.map((o: any) => [String(o._id), o]));

    const enriched = usages.map((u: any) => ({
      id: String(u._id),
      usedAt: u.createdAt, // approximate usage time as order creation time
      user: userById.get(String(u.userId))
        ? {
            id: userById.get(String(u.userId))._id,
            name: userById.get(String(u.userId)).name,
            email: userById.get(String(u.userId)).email,
          }
        : null,
      order: orderById.get(String(u._id))
        ? {
            id: orderById.get(String(u._id))._id,
            orderNumber: orderById.get(String(u._id)).orderNumber,
            total: orderById.get(String(u._id)).total,
          }
        : null,
    }));

    return NextResponse.json({ ...coupon, usages: enriched });
  } catch (error) {
    logger.error("Error fetching coupon", error as Error);
    return NextResponse.json(
      { error: "Failed to fetch coupon" },
      { status: 500 },
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || (session.user as any).role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const data: CouponUpdateInput = await request.json();

    // Validate required fields
    if (
      !data.name ||
      !data.type ||
      !data.discountType ||
      data.discountValue === undefined
    ) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 },
      );
    }

    await connectDB();

    // Narrow lean() type to avoid union-with-array TS errors
    type LeanCoupon = {
      _id: any;
      code: string;
      name: string;
      description?: string | null;
      type: "PERCENTAGE" | "FIXED";
      value: number;
      minimumAmount?: number | null;
      maximumDiscount?: number | null;
      usageLimit?: number | null;
      userUsageLimit?: number | null;
      isActive: boolean;
      isStackable?: boolean;
      showInModule?: boolean;
      validFrom: Date;
      validUntil?: Date | null;
      applicableProducts?: string[];
      applicableCategories?: string[];
      excludedProducts?: string[];
      excludedCategories?: string[];
      customerSegments?: string[];
      updatedAt?: Date;
    };

    const existing = (await Coupon.findById(params.id).lean()) as LeanCoupon | null;
    if (!existing) {
      return NextResponse.json({ error: "Coupon not found" }, { status: 404 });
    }

    // Code uniqueness if changed
    let nextCode: string = existing.code;
    if (data.code && data.code.toUpperCase() !== existing.code) {
      const normalized = data.code.toUpperCase();
      const codeExists = await Coupon.findOne({ code: normalized, _id: { $ne: params.id } }).lean();
      if (codeExists) {
        return NextResponse.json(
          { error: "Coupon code already exists" },
          { status: 400 },
        );
      }
      nextCode = normalized;
    }

    const updateDoc: any = {
      code: nextCode,
      name: data.name,
      description: data.description ?? existing.description ?? null,
      type: data.type,
      // Align with model fields: set value from discountValue
      value: data.discountValue,
      minimumAmount: data.minimumAmount ?? existing.minimumAmount ?? null,
      maximumDiscount: data.maximumDiscount ?? existing.maximumDiscount ?? null,
      usageLimit: data.usageLimit ?? existing.usageLimit ?? null,
      userUsageLimit: data.userUsageLimit ?? existing.userUsageLimit ?? null,
      isActive: data.isActive ?? existing.isActive,
      isStackable: data.isStackable ?? existing.isStackable,
      showInModule: data.showInModule ?? existing.showInModule,
      validFrom: data.validFrom ? new Date(data.validFrom) : existing.validFrom,
      validUntil: data.validUntil ? new Date(data.validUntil) : existing.validUntil ?? null,
      applicableProducts: data.applicableProducts ?? existing.applicableProducts ?? [],
      applicableCategories: data.applicableCategories ?? existing.applicableCategories ?? [],
      excludedProducts: data.excludedProducts ?? existing.excludedProducts ?? [],
      excludedCategories: data.excludedCategories ?? existing.excludedCategories ?? [],
      customerSegments: data.customerSegments ?? existing.customerSegments ?? [],
      updatedAt: new Date(),
    };

    await Coupon.updateOne({ _id: params.id }, { $set: updateDoc });

    const updated = await Coupon.findById(params.id).lean();

    return NextResponse.json(updated);
  } catch (error: any) {
    if (error?.code === 11000) {
      return NextResponse.json(
        { error: "Coupon code already exists" },
        { status: 400 },
      );
    }
    logger.error("Error updating coupon", error as Error);
    return NextResponse.json(
      { error: "Failed to update coupon" },
      { status: 500 },
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || (session.user as any).role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await connectDB();

    // Reuse LeanCoupon type declared in GET or declare a minimal local fallback for delete step
    type LeanCouponForDelete = { _id: any; code: string; name?: string };
    const existing = await Coupon.findById(params.id).lean<LeanCouponForDelete | null>();
    if (!existing) {
      return NextResponse.json({ error: "Coupon not found" }, { status: 404 });
    }

    // Derive usage count from orders that reference this coupon code
    const usageCount = await Order.countDocuments({ couponCodes: { $in: [String(existing.code).toUpperCase()] } });
    if (usageCount > 0) {
      return NextResponse.json(
        {
          error:
            "Cannot delete coupon that has been used. Consider deactivating it instead.",
        },
        { status: 400 },
      );
    }

    await Coupon.deleteOne({ _id: params.id });

    return NextResponse.json({ message: "Coupon deleted successfully" });
  } catch (error) {
    logger.error("Error deleting coupon", error as Error);
    return NextResponse.json(
      { error: "Failed to delete coupon" },
      { status: 500 },
    );
  }
}
