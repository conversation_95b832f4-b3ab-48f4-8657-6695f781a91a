# Herbalicious Documentation

This folder contains all the documentation for the Herbalicious e-commerce application.

## 📚 Documentation Index

### Setup & Configuration

- [**DATABASE_README.md**](./DATABASE_README.md) - Database setup and configuration guide
- [**CLOUDFLARE_R2_SETUP.md**](./CLOUDFLARE_R2_SETUP.md) - Cloudflare R2 storage setup
- [**R2_SETUP_GUIDE.md**](./R2_SETUP_GUIDE.md) - Additional R2 configuration guide
- [**MEDIA_MANAGEMENT_README.md**](./MEDIA_MANAGEMENT_README.md) - Media and file management

### Admin Features

- [**ADMIN_README.md**](./ADMIN_README.md) - Admin panel documentation
- [**admin-logout-fix.md**](./admin-logout-fix.md) - Admin logout functionality fixes
- [**logout-fix-test.md**](./logout-fix-test.md) - Logout testing documentation

### Product Management

- [**PRICING_SYSTEM_SUMMARY.md**](./PRICING_SYSTEM_SUMMARY.md) - Product pricing system overview
- [**VARIATIONS_GUIDE.md**](./VARIATIONS_GUIDE.md) - Product variations and variants guide
- [**JSON_IMPORT_GUIDE.md**](./JSON_IMPORT_GUIDE.md) - Product import functionality
- [**csv-import-blank-price-fix.md**](./csv-import-blank-price-fix.md) - CSV import fixes

### Testing & Fixes

- [**test-review-fix.md**](./test-review-fix.md) - Review system testing and fixes

### Production & Deployment

- [**PRODUCTION_PLAN.md**](./PRODUCTION_PLAN.md) - Production deployment plan and checklist

## 🚀 Quick Start

1. **Database Setup**: Start with [DATABASE_README.md](./DATABASE_README.md)
2. **Media Storage**: Configure storage using [CLOUDFLARE_R2_SETUP.md](./CLOUDFLARE_R2_SETUP.md)
3. **Admin Panel**: Learn about admin features in [ADMIN_README.md](./ADMIN_README.md)
4. **Product Management**: Understand pricing and variations with [PRICING_SYSTEM_SUMMARY.md](./PRICING_SYSTEM_SUMMARY.md)
5. **Production**: Follow [PRODUCTION_PLAN.md](./PRODUCTION_PLAN.md) for deployment

## 📋 Project Status

The Herbalicious e-commerce application includes:

✅ **Authentication & Security** - Complete user authentication with role-based access  
✅ **Payment Integration** - Razorpay payment gateway with order management  
✅ **Product Management** - Full CRUD operations with variations and pricing  
✅ **Order Management** - Complete order workflow with status tracking  
✅ **Email System** - Brevo integration with professional templates  
✅ **Admin Dashboard** - Comprehensive admin panel for all operations  
✅ **Error Handling** - Production-ready error handling and logging  
✅ **Rate Limiting** - API protection and security measures

## 🛠️ Technical Stack

- **Frontend**: Next.js 14 with TypeScript and Tailwind CSS
- **Backend**: Next.js API routes with Prisma ORM
- **Database**: PostgreSQL (Xata hosted)
- **Authentication**: NextAuth.js with JWT
- **Payments**: Razorpay integration
- **Email**: Brevo (Sendinblue) API
- **Storage**: Cloudflare R2
- **Deployment**: Ready for Vercel/production deployment

## 📞 Support

For questions or issues, refer to the specific documentation files above or check the main application code in the `/app` directory.
