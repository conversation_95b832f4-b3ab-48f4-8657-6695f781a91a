import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../lib/auth";
import connectDB from "../../../lib/mongoose";
import { User, Order, Notification } from "../../../lib/models";
import { logger } from "../../../lib/logger";

/**
 * GET /api/admin/users
 * Get users for admin management
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Only allow admin users
    if (!session?.user?.id || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 401 },
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");
    const search = searchParams.get("search");
    const role = searchParams.get("role");

    const skip = (page - 1) * limit;

    await connectDB();
    
    // Build query
    const query: any = {};

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
      ];
    }

    if (role) {
      query.role = role;
    }
    
    // First get users
    const [users, totalCount] = await Promise.all([
      User.find(query)
        .select('_id name email role createdAt preferences')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      User.countDocuments(query),
    ]);
    
    // Then fetch counts for each user
    const usersWithCounts = await Promise.all(users.map(async (user) => {
      const [orderCount, notificationCount] = await Promise.all([
        Order.countDocuments({ userId: user._id }),
        Notification.countDocuments({ userId: user._id }),
      ]);
      
      return {
        ...user,
        id: user._id, // Add id field for compatibility
        _count: {
          orders: orderCount,
          notifications: notificationCount,
        }
      };
    }));

    const totalPages = Math.ceil(totalCount / limit);

    logger.info("Admin fetched users list", {
      adminId: session.user.id,
      page,
      limit,
      totalCount,
      search,
      role,
    });

    return NextResponse.json({
      success: true,
      data: {
        users: usersWithCounts,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
    });
  } catch (error) {
    logger.error("Failed to fetch users for admin", error as Error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
