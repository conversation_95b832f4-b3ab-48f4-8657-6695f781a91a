import { NextRequest, NextResponse } from "next/server";
import { deleteFromR2 } from "../../../lib/r2";

export async function DELETE(request: NextRequest) {
  try {
    const { key } = await request.json();

    if (!key) {
      console.error("No file key provided in delete request");
      return NextResponse.json(
        { success: false, error: "No file key provided" },
        { status: 400 },
      );
    }

    const success = await deleteFromR2(key);

    if (success) {
      return NextResponse.json({ success: true });
    } else {
      console.error("Failed to delete file from R2:", key);
      return NextResponse.json(
        { success: false, error: "Failed to delete file" },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error("Delete error:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Delete failed",
      },
      { status: 500 },
    );
  }
}
