[{"id": "cmdixk5is0000ulxkfy2s07bo", "name": "Cleansers & Face Wash", "slug": "cleansers-face-wash", "description": "Gentle cleansers and face washes for daily skincare routine", "parentId": null, "isActive": true, "createdAt": "2025-07-25T14:42:19.252Z", "updatedAt": "2025-07-25T14:42:19.252Z"}, {"id": "cmdixk5r70001ulxk47s4hda2", "name": "Scrubs & Exfoliants", "slug": "scrubs-exfoliants", "description": "Deep cleansing scrubs and exfoliants for smooth skin", "parentId": null, "isActive": true, "createdAt": "2025-07-25T14:42:19.556Z", "updatedAt": "2025-07-25T14:42:19.556Z"}, {"id": "cmdixk5v80002ulxkok19dsx7", "name": "Ubtan & Masks", "slug": "ubtan-masks", "description": "Traditional ubtans and nourishing face masks", "parentId": null, "isActive": true, "createdAt": "2025-07-25T14:42:19.701Z", "updatedAt": "2025-07-25T14:42:19.701Z"}, {"id": "cmdixk5z30003ulxksfymjg2r", "name": "Creams & Moisturizers", "slug": "creams-moisturizers", "description": "Hydrating creams and moisturizers for all skin types", "parentId": null, "isActive": true, "createdAt": "2025-07-25T14:42:19.840Z", "updatedAt": "2025-07-25T14:42:19.840Z"}, {"id": "cmdixk63h0004ulxk0wlb7x3q", "name": "Gels & Serums", "slug": "gels-serums", "description": "Concentrated gels and serums for targeted skincare", "parentId": null, "isActive": true, "createdAt": "2025-07-25T14:42:19.998Z", "updatedAt": "2025-07-25T14:42:19.998Z"}, {"id": "cmdixk67e0005ulxktzrlnvy2", "name": "Facial Oils & Elixirs", "slug": "facial-oils-elixirs", "description": "Nourishing facial oils and elixirs for radiant skin", "parentId": null, "isActive": true, "createdAt": "2025-07-25T14:42:20.138Z", "updatedAt": "2025-07-25T14:42:20.138Z"}, {"id": "cmdixk6bh0006ulxkrj2ezmz8", "name": "Toners", "slug": "toners", "description": "Refreshing toners to balance and prep skin", "parentId": null, "isActive": true, "createdAt": "2025-07-25T14:42:20.285Z", "updatedAt": "2025-07-25T14:42:20.285Z"}, {"id": "cmdixk6jo0008ulxkfa87a9za", "name": "Hair Oils", "slug": "hair-oils", "description": "Natural hair oils for healthy hair and scalp", "parentId": null, "isActive": true, "createdAt": "2025-07-25T14:42:20.580Z", "updatedAt": "2025-07-25T14:42:20.580Z"}, {"id": "cmdixk6no0009ulxkzo0jgttw", "name": "Hair Masks", "slug": "hair-masks", "description": "Deep conditioning hair masks for damaged hair", "parentId": null, "isActive": true, "createdAt": "2025-07-25T14:42:20.725Z", "updatedAt": "2025-07-25T14:42:20.725Z"}, {"id": "cmdixk6rn000aulxkygzyk40y", "name": "Eye & Lip Care", "slug": "eye-lip-care", "description": "Specialized care for delicate eye and lip areas", "parentId": null, "isActive": true, "createdAt": "2025-07-25T14:42:20.867Z", "updatedAt": "2025-07-25T14:42:20.867Z"}, {"id": "cmdixk6vx000bulxkxwnshk95", "name": "Massage & Body Oils", "slug": "massage-body-oils", "description": "Luxurious massage and body oils for relaxation", "parentId": null, "isActive": true, "createdAt": "2025-07-25T14:42:21.021Z", "updatedAt": "2025-07-25T14:42:21.021Z"}, {"id": "cmdixk70f000culxkpbadkefz", "name": "Combo & Complete Care", "slug": "combo-complete-care", "description": "Complete skincare combinations and sets", "parentId": null, "isActive": true, "createdAt": "2025-07-25T14:42:21.183Z", "updatedAt": "2025-07-25T14:42:21.183Z"}]