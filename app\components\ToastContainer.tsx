"use client";

import React from "react";
import Toast from "./Toast";
import { useToast } from "../hooks/useToast";

interface ToastContainerProps {
  toasts: { id: number; message: string; type: "success" | "error" | "info" }[];
  hideToast: (id: number) => void;
}

const ToastContainer: React.FC<ToastContainerProps> = ({
  toasts,
  hideToast,
}) => {
  return (
    <div className="fixed bottom-5 right-5 z-50">
      {toasts.map((toast) => (
        <Toast
          key={toast.id}
          message={toast.message}
          type={toast.type}
          onClose={() => hideToast(toast.id)}
        />
      ))}
    </div>
  );
};

export default ToastContainer;
