import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../lib/auth";
import { logger } from "../../../lib/logger";

/**
 * POST /api/admin/cleanup
 * Clean up database by removing notifications, reviews, and orders
 *
 * NOTE: This endpoint is temporarily disabled during Prisma to MongoDB migration
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is admin
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    logger.info("Admin cleanup API temporarily disabled during migration");

    // Return mock success during migration
    return NextResponse.json({
      success: true,
      message: "Database cleanup temporarily disabled during migration",
      deletedCounts: {
        notifications: 0,
        reviews: 0,
        orders: 0,
        orderItems: 0,
        orderAddresses: 0,
        couponUsages: 0,
      },
    });
  } catch (error) {
    logger.error("Error during database cleanup:", error as Error);
    return NextResponse.json(
      { success: false, error: "Failed to cleanup database" },
      { status: 500 },
    );
  }
}
