import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../lib/auth";
import connectDB from "../../../lib/mongoose";
import { Order, User } from "../../../lib/models";
import {
  handleApiError,
  AuthenticationError,
  ValidationError,
  asyncHandler,
} from "../../../lib/errors";
import { logger } from "../../../lib/logger";
import { orderNotifications } from "../../../lib/notification-helpers";

const bulkUpdateSchema = z.object({
  orderIds: z.array(z.string()).min(1, "At least one order ID is required"),
  updates: z
    .object({
      status: z
        .enum([
          "PENDING",
          "CONFIRMED",
          "PROCESSING",
          "SHIPPED",
          "DELIVERED",
          "CANCELLED",
          "REFUNDED",
        ])
        .optional(),
      paymentStatus: z
        .enum(["PENDING", "PAID", "FAILED", "REFUNDED"])
        .optional(),
    })
    .refine((data) => data.status || data.paymentStatus, {
      message: "At least one update field is required",
    }),
});

// POST /api/orders/bulk-update - Bulk update orders (admin only)
export const POST = asyncHandler(async (request: NextRequest) => {
  logger.apiRequest("POST", "/api/orders/bulk-update");

  // Check authentication and admin role
  const session = await getServerSession(authOptions);
  if (!session?.user?.id || session.user.role !== "ADMIN") {
    throw new AuthenticationError("Admin access required");
  }

  const body = await request.json();
  const validatedData = bulkUpdateSchema.parse(body);
  const { orderIds, updates } = validatedData;

  try {
    await connectDB();

    // Get current orders
    const currentOrders = await Order.find({ _id: { $in: orderIds } }).lean();

    if (currentOrders.length === 0) {
      throw new ValidationError("No valid orders found");
    }

    // Update orders
    const baseSet: any = { updatedAt: new Date() };
    if (updates.status) baseSet.status = updates.status;
    if (updates.paymentStatus) baseSet.paymentStatus = updates.paymentStatus;

    const updateResultRaw = await Order.updateMany(
      { _id: { $in: orderIds } },
      { $set: baseSet }
    );
    // Mongoose 6/7 uses modifiedCount; fallback to nModified for older typings if present
    const updateResult = { count: (updateResultRaw as any).modifiedCount ?? (updateResultRaw as any).nModified ?? 0 };

    // Send notifications for status changes
    if (updates.status) {
      const notificationPromises = currentOrders
        .filter((order: any) => order.status !== updates.status)
        .map(async (order: any) => {
          try {
            switch (updates.status) {
              case "CONFIRMED":
                await orderNotifications.orderConfirmed(order.userId, {
                  orderId: order._id,
                  orderNumber: order.orderNumber,
                });
                break;
              case "SHIPPED":
                await orderNotifications.orderShipped(order.userId, {
                  orderId: order._id,
                  orderNumber: order.orderNumber,
                });
                break;
              case "DELIVERED":
                await orderNotifications.orderDelivered(order.userId, {
                  orderId: order._id,
                  orderNumber: order.orderNumber,
                });
                // Update payment status to PAID for COD orders
                if (
                  order.paymentMethod === "COD" &&
                  order.paymentStatus !== "PAID"
                ) {
                  await Order.updateOne(
                    { _id: order._id },
                    { $set: { paymentStatus: "PAID" } }
                  );
                }
                break;
              case "CANCELLED":
                await orderNotifications.orderCancelled(order.userId, {
                  orderId: order._id,
                  orderNumber: order.orderNumber,
                  reason: "Bulk update by admin",
                });
                break;
            }
          } catch (error) {
            logger.error(
              `Failed to send notification for order ${order._id}`,
              error as Error,
            );
          }
        });

      await Promise.allSettled(notificationPromises);
    }

    logger.info("Bulk order update completed", {
      adminId: session.user.id,
      orderCount: updateResult.count,
      updates,
    });

    return NextResponse.json({
      success: true,
      updatedCount: updateResult.count,
      message: `Successfully updated ${updateResult.count} orders`,
    });
  } catch (error) {
    logger.error("Failed to bulk update orders", error as Error);
    throw error;
  }
});
