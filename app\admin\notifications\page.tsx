"use client";

import React, { useState, useEffect } from "react";
import <PERSON> from "next/link";
import {
  Bell,
  Send,
  History,
  Settings,
  Users,
  MessageSquare,
  TrendingUp,
  Plus,
  Eye,
  Calendar,
  Filter,
} from "lucide-react";

interface NotificationStats {
  totalSent: number;
  totalDelivered: number;
  totalRead: number;
  deliveryRate: number;
  readRate: number;
  recentActivity: number;
}

const AdminNotifications = () => {
  const [stats, setStats] = useState<NotificationStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchNotificationStats();
  }, []);

  const fetchNotificationStats = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/admin/notifications/stats");
      const data = await response.json();

      if (data.success) {
        setStats(data.stats);
      } else {
        setError("Failed to fetch notification statistics");
      }
    } catch (error) {
      console.error("Error fetching notification stats:", error);
      setError("Failed to fetch notification statistics");
    } finally {
      setLoading(false);
    }
  };

  const quickActions = [
    {
      title: "Send Notification",
      description: "Send a notification to users",
      href: "/admin/notifications/send",
      icon: Send,
      color: "bg-green-500",
      textColor: "text-green-600",
    },
    {
      title: "Broadcast Message",
      description: "Send message to all users",
      href: "/admin/notifications/broadcast",
      icon: MessageSquare,
      color: "bg-blue-500",
      textColor: "text-blue-600",
    },
    {
      title: "Notification History",
      description: "View sent notifications",
      href: "/admin/notifications/history",
      icon: History,
      color: "bg-purple-500",
      textColor: "text-purple-600",
    },
    {
      title: "Templates",
      description: "Manage notification templates",
      href: "/admin/notifications/templates",
      icon: Settings,
      color: "bg-orange-500",
      textColor: "text-orange-600",
    },
  ];

  const statCards = [
    {
      title: "Total Sent",
      value: stats?.totalSent || 0,
      icon: Send,
      color: "bg-blue-500",
    },
    {
      title: "Delivered",
      value: stats?.totalDelivered || 0,
      icon: TrendingUp,
      color: "bg-green-500",
    },
    {
      title: "Read",
      value: stats?.totalRead || 0,
      icon: Eye,
      color: "bg-purple-500",
    },
    {
      title: "Recent Activity",
      value: stats?.recentActivity || 0,
      icon: Calendar,
      color: "bg-orange-500",
    },
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div
              key={i}
              className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 animate-pulse"
            >
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-600">{error}</p>
        <button
          onClick={fetchNotificationStats}
          className="mt-2 text-red-600 hover:text-red-800 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Notifications</h1>
          <p className="text-gray-600 mt-2">
            Manage and send notifications to your customers
          </p>
        </div>
        <Link
          href="/admin/notifications/send"
          className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Send Notification</span>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div
              key={index}
              className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">
                    {stat.value.toLocaleString()}
                  </p>
                </div>
                <div
                  className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center`}
                >
                  <Icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Performance Metrics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Delivery Rate
            </h3>
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-600 h-2 rounded-full"
                    style={{ width: `${stats.deliveryRate}%` }}
                  ></div>
                </div>
              </div>
              <span className="text-2xl font-bold text-gray-900">
                {stats.deliveryRate.toFixed(1)}%
              </span>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Read Rate
            </h3>
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full"
                    style={{ width: `${stats.readRate}%` }}
                  ></div>
                </div>
              </div>
              <span className="text-2xl font-bold text-gray-900">
                {stats.readRate.toFixed(1)}%
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Quick Actions
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickActions.map((action, index) => {
            const Icon = action.icon;
            return (
              <Link
                key={index}
                href={action.href}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow group"
              >
                <div className="flex items-center space-x-4">
                  <div
                    className={`w-12 h-12 ${action.color} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform`}
                  >
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3
                      className={`font-semibold ${action.textColor} group-hover:text-gray-900 transition-colors`}
                    >
                      {action.title}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {action.description}
                    </p>
                  </div>
                </div>
              </Link>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default AdminNotifications;
