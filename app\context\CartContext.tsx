"use client";

import React, {
  createContext,
  useContext,
  useReducer,
  ReactNode,
  useEffect,
} from "react";
import { CartItem, Product, AppliedCoupon, CartCouponState } from "../types";

interface CartState {
  items: CartItem[];
  total: number;
  itemCount: number;
  subtotal: number;
  coupons: CartCouponState;
  finalTotal: number;
  flashSaleDiscount: number;
  flashSaleActive: boolean;
  flashSalePercentage: number;
  isHydrated: boolean;
}

type CartAction =
  | {
      type: "ADD_ITEM";
      payload: Product;
      selectedVariants?: Array<{
        id: string;
        name: string;
        value: string;
        price?: number;
      }>;
    }
  | { type: "REMOVE_ITEM"; payload: string }
  | { type: "UPDATE_QUANTITY"; payload: { id: string; quantity: number } }
  | { type: "CLEAR_CART" }
  | { type: "APPLY_COUPON"; payload: AppliedCoupon }
  | { type: "REMOVE_COUPON"; payload: string }
  | { type: "CLEAR_COUPONS" }
  | { type: "SET_CART_STATE"; payload: CartState };

const CartContext = createContext<{
  state: CartState;
  dispatch: React.Dispatch<CartAction>;
} | null>(null);

// localStorage utilities
const CART_STORAGE_KEY = "herbalicious_cart";

const saveCartToStorage = (state: CartState) => {
  try {
    const { isHydrated, ...stateToSave } = state;
    if (typeof window !== "undefined") {
      localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(stateToSave));
    }
  } catch (error) {
    console.error("Error saving cart to localStorage:", error);
  }
};

const loadCartFromStorage = (): CartState | null => {
  try {
    if (typeof window !== "undefined") {
      const stored = localStorage.getItem(CART_STORAGE_KEY);
      if (stored) {
        const parsedState = JSON.parse(stored);
        return { ...parsedState, isHydrated: true };
      }
    }
  } catch (error) {
    console.error("Error loading cart from localStorage:", error);
  }
  return null;
};

// Helper function to generate unique variant key
const generateVariantKey = (
  productId: string,
  selectedVariants?: Array<{
    id: string;
    name: string;
    value: string;
    price?: number;
  }>,
) => {
  if (!selectedVariants || selectedVariants.length === 0) {
    return productId;
  }

  const sortedVariants = [...selectedVariants].sort((a, b) =>
    a.name.localeCompare(b.name),
  );
  const variantString = sortedVariants
    .map((v) => `${v.name}:${v.value}`)
    .join("|");
  return `${productId}__${variantString}`;
};

// Helper function to get item identifier
const getItemIdentifier = (item: any) => {
  return item.variantKey || item.product?.id || item.id;
};

const getInitialCartState = (): CartState => {
  return {
    items: [],
    total: 0,
    subtotal: 0,
    itemCount: 0,
    finalTotal: 0,
    coupons: {
      appliedCoupons: [],
      totalDiscount: 0,
      availableCoupons: [],
    },
    flashSaleDiscount: 0,
    flashSaleActive: false,
    flashSalePercentage: 0,
    isHydrated: false,
  };
};

// Default flash sale settings
const DEFAULT_FLASH_SALE_PERCENTAGE = 25;

// Helper function to get flash sale settings (client-side only)
const getFlashSaleSettings = () => {
  try {
    if (typeof window !== "undefined") {
      const settings = localStorage.getItem("flashSaleSettings");
      if (settings) {
        const parsed = JSON.parse(settings);
        const isActive = parsed.showFlashSale === true;
        const percentage =
          parsed.flashSalePercentage || DEFAULT_FLASH_SALE_PERCENTAGE;
        const endDate = parsed.flashSaleEndDate;

        if (endDate) {
          const now = new Date();
          const end = new Date(endDate);
          return {
            flashSaleActive: isActive && now < end,
            flashSalePercentage: percentage,
          };
        }

        return { flashSaleActive: isActive, flashSalePercentage: percentage };
      }
    }
  } catch (error) {
    console.error("Error loading flash sale settings:", error);
  }

  return { flashSaleActive: false, flashSalePercentage: 0 };
};

// Helper function to calculate flash sale prices
const calculateFlashSalePrices = (
  items: CartItem[],
  appliedCoupons: AppliedCoupon[],
  flashSaleActive: boolean,
  flashSalePercentage: number,
) => {
  let subtotal = 0;
  let flashSaleDiscount = 0;

  items.forEach((item) => {
    const originalPrice = item.product.price;
    if (flashSaleActive && flashSalePercentage > 0) {
      const discountAmount = (originalPrice * flashSalePercentage) / 100;
      const salePrice = originalPrice - discountAmount;
      subtotal += salePrice * item.quantity;
      flashSaleDiscount += discountAmount * item.quantity;
    } else {
      subtotal += originalPrice * item.quantity;
    }
  });

  const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);
  const couponDiscount = appliedCoupons.reduce(
    (sum, coupon) => sum + coupon.discountAmount,
    0,
  );
  const finalTotal = subtotal - couponDiscount;

  return {
    subtotal,
    itemCount,
    total: subtotal,
    finalTotal,
    flashSaleDiscount,
    couponDiscount,
  };
};

const cartReducer = (state: CartState, action: CartAction): CartState => {
  // Get flash sale settings - use client-side only to prevent hydration issues
  const { flashSaleActive, flashSalePercentage } =
    typeof window !== "undefined"
      ? getFlashSaleSettings()
      : { flashSaleActive: false, flashSalePercentage: 0 };

  switch (action.type) {
    case "SET_CART_STATE":
      return { ...action.payload, isHydrated: true };

    case "ADD_ITEM": {
      const variantKey = generateVariantKey(
        action.payload.id,
        action.selectedVariants,
      );
      const existingItem = state.items.find(
        (item) => getItemIdentifier(item) === variantKey,
      );

      let updatedItems: CartItem[];
      if (existingItem) {
        updatedItems = state.items.map((item) =>
          getItemIdentifier(item) === variantKey
            ? { ...item, quantity: item.quantity + 1, variantKey }
            : item,
        );
      } else {
        const newCartItem: CartItem = {
          product: action.payload,
          quantity: 1,
          selectedVariants: action.selectedVariants || [],
          variantKey,
        };
        updatedItems = [...state.items, newCartItem];
      }

      const prices = calculateFlashSalePrices(
        updatedItems,
        state.coupons.appliedCoupons,
        flashSaleActive,
        flashSalePercentage,
      );

      const newState = {
        ...state,
        items: updatedItems,
        subtotal: prices.subtotal,
        itemCount: prices.itemCount,
        total: prices.total,
        finalTotal: prices.finalTotal,
        coupons: {
          ...state.coupons,
          totalDiscount: prices.couponDiscount,
        },
        flashSaleDiscount: prices.flashSaleDiscount,
        flashSaleActive,
        flashSalePercentage,
      };

      saveCartToStorage(newState);
      return newState;
    }

    case "REMOVE_ITEM": {
      const filteredItems = state.items.filter(
        (item) => getItemIdentifier(item) !== action.payload,
      );
      const prices = calculateFlashSalePrices(
        filteredItems,
        state.coupons.appliedCoupons,
        flashSaleActive,
        flashSalePercentage,
      );

      const newState = {
        ...state,
        items: filteredItems,
        subtotal: prices.subtotal,
        itemCount: prices.itemCount,
        total: prices.total,
        finalTotal: prices.finalTotal,
        coupons: {
          ...state.coupons,
          totalDiscount: prices.couponDiscount,
        },
        flashSaleDiscount: prices.flashSaleDiscount,
        flashSaleActive,
        flashSalePercentage,
      };

      saveCartToStorage(newState);
      return newState;
    }

    case "UPDATE_QUANTITY": {
      const updatedItems = state.items
        .map((item) =>
          getItemIdentifier(item) === action.payload.id
            ? { ...item, quantity: action.payload.quantity }
            : item,
        )
        .filter((item) => item.quantity > 0);

      const prices = calculateFlashSalePrices(
        updatedItems,
        state.coupons.appliedCoupons,
        flashSaleActive,
        flashSalePercentage,
      );

      const newState = {
        ...state,
        items: updatedItems,
        subtotal: prices.subtotal,
        itemCount: prices.itemCount,
        total: prices.total,
        finalTotal: prices.finalTotal,
        coupons: {
          ...state.coupons,
          totalDiscount: prices.couponDiscount,
        },
        flashSaleDiscount: prices.flashSaleDiscount,
        flashSaleActive,
        flashSalePercentage,
      };

      saveCartToStorage(newState);
      return newState;
    }

    case "APPLY_COUPON": {
      const isAlreadyApplied = state.coupons.appliedCoupons.some(
        (coupon) => coupon.coupon.id === action.payload.coupon.id,
      );

      if (isAlreadyApplied) {
        return state;
      }

      const hasNonStackableCoupon = state.coupons.appliedCoupons.some(
        (coupon) => !coupon.coupon.isStackable,
      );

      if (hasNonStackableCoupon && !action.payload.coupon.isStackable) {
        return state;
      }

      const updatedAppliedCoupons = [
        ...state.coupons.appliedCoupons,
        action.payload,
      ];

      const prices = calculateFlashSalePrices(
        state.items,
        updatedAppliedCoupons,
        flashSaleActive,
        flashSalePercentage,
      );

      const newState = {
        ...state,
        subtotal: prices.subtotal,
        itemCount: prices.itemCount,
        total: prices.total,
        finalTotal: prices.finalTotal,
        coupons: {
          ...state.coupons,
          appliedCoupons: updatedAppliedCoupons,
          totalDiscount: prices.couponDiscount,
        },
        flashSaleDiscount: prices.flashSaleDiscount,
        flashSaleActive,
        flashSalePercentage,
      };

      saveCartToStorage(newState);
      return newState;
    }

    case "REMOVE_COUPON": {
      const updatedAppliedCoupons = state.coupons.appliedCoupons.filter(
        (coupon) => coupon.coupon.id !== action.payload,
      );

      const prices = calculateFlashSalePrices(
        state.items,
        updatedAppliedCoupons,
        flashSaleActive,
        flashSalePercentage,
      );

      const newState = {
        ...state,
        subtotal: prices.subtotal,
        itemCount: prices.itemCount,
        total: prices.total,
        finalTotal: prices.finalTotal,
        coupons: {
          ...state.coupons,
          appliedCoupons: updatedAppliedCoupons,
          totalDiscount: prices.couponDiscount,
        },
        flashSaleDiscount: prices.flashSaleDiscount,
        flashSaleActive,
        flashSalePercentage,
      };

      saveCartToStorage(newState);
      return newState;
    }

    case "CLEAR_COUPONS": {
      const prices = calculateFlashSalePrices(
        state.items,
        [],
        flashSaleActive,
        flashSalePercentage,
      );

      const newState = {
        ...state,
        subtotal: prices.subtotal,
        itemCount: prices.itemCount,
        total: prices.total,
        finalTotal: prices.finalTotal,
        coupons: {
          appliedCoupons: [],
          totalDiscount: 0,
          availableCoupons: [],
        },
        flashSaleDiscount: prices.flashSaleDiscount,
        flashSaleActive,
        flashSalePercentage,
      };

      saveCartToStorage(newState);
      return newState;
    }

    case "CLEAR_CART": {
      const newState = {
        ...getInitialCartState(),
        isHydrated: true,
        flashSaleActive,
        flashSalePercentage,
      };

      saveCartToStorage(newState);
      return newState;
    }

    default:
      return state;
  }
};

export const CartProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(cartReducer, getInitialCartState());

  useEffect(() => {
    const storedCart = loadCartFromStorage();
    if (storedCart) {
      dispatch({ type: "SET_CART_STATE", payload: storedCart });
    } else {
      dispatch({ type: "SET_CART_STATE", payload: getInitialCartState() });
    }
  }, []);

  // Re-calculate cart when flash sale settings change
  useEffect(() => {
    if (state.isHydrated && state.items.length > 0) {
      dispatch({
        type: "UPDATE_QUANTITY",
        payload: {
          id: state.items[0].variantKey || state.items[0].product.id,
          quantity: state.items[0].quantity,
        },
      });
    }
  }, [state.isHydrated]);

  // Listen for flash sale settings changes
  useEffect(() => {
    if (!state.isHydrated) return;

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "flashSaleSettings" && state.items.length > 0) {
        dispatch({
          type: "UPDATE_QUANTITY",
          payload: {
            id: state.items[0].variantKey || state.items[0].product.id,
            quantity: state.items[0].quantity,
          },
        });
      }
    };

    const handleFlashSaleUpdate = () => {
      if (state.items.length > 0) {
        dispatch({
          type: "UPDATE_QUANTITY",
          payload: {
            id: state.items[0].variantKey || state.items[0].product.id,
            quantity: state.items[0].quantity,
          },
        });
      }
    };

    window.addEventListener("storage", handleStorageChange);
    window.addEventListener("flashSaleSettingsUpdated", handleFlashSaleUpdate);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
      window.removeEventListener(
        "flashSaleSettingsUpdated",
        handleFlashSaleUpdate,
      );
    };
  }, [state.isHydrated, state.items]);

  return (
    <CartContext.Provider value={{ state, dispatch }}>
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error("useCart must be used within a CartProvider");
  }
  return context;
};
