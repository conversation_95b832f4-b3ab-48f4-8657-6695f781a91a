"use client";

import React from "react";
import Link from "next/link";
import {
  <PERSON>,
  Droplets,
  Spark<PERSON>,
  Eye,
  Zap,
  Heart,
  Flower2,
  FlaskConical,
  Package,
  Waves,
  Hand,
  Droplet,
  ShowerHead,
  Pa<PERSON>,
  <PERSON>,
  <PERSON>,
} from "lucide-react";
import { getProductCategories } from "../lib/productUtils";

interface ProductCategoriesProps {
  product: any;
  showAsLinks?: boolean;
  className?: string;
  maxCategories?: number; // Limit number of categories to display
  size?: "xs" | "sm" | "base" | "lg"; // Add size prop
}

const ProductCategories: React.FC<ProductCategoriesProps> = ({
  product,
  showAsLinks = false,
  className = "",
  maxCategories,
  size = "xs",
}) => {
  const allCategories = getProductCategories(product);
  const categories = maxCategories
    ? allCategories.slice(0, maxCategories)
    : allCategories;

  if (categories.length === 0) {
    return null;
  }

  const sizeClasses = {
    xs: "text-xs",
    sm: "text-sm",
    base: "text-base",
    lg: "text-lg",
  };

  const iconSizeClasses = {
    xs: "w-3 h-3",
    sm: "w-4 h-4",
    base: "w-5 h-5",
    lg: "w-6 h-6",
  };

  const getCategoryIcon = (categoryName: string) => {
    const iconClass = iconSizeClasses[size];
    const normalizedName = categoryName.toLowerCase();

    const icons: { [key: string]: React.ReactNode } = {
      // Main categories
      skincare: <Leaf className={iconClass} />,
      "hair care": <Sparkles className={iconClass} />,
      "hair oils": <Droplets className={iconClass} />,
      "body care": <Heart className={iconClass} />,

      // Specific product categories from screenshot
      "all products": <Package className={iconClass} />,
      "cleansers & face wash": <ShowerHead className={iconClass} />,
      "combo & complete care": <Star className={iconClass} />,
      "creams & moisturizers": <Droplets className={iconClass} />,
      "eye & lip care": <Eye className={iconClass} />,
      "facial kits": <Palette className={iconClass} />,
      "facial oils & elixirs": <FlaskConical className={iconClass} />,
      "gels & serums": <Zap className={iconClass} />,
      "hair masks": <Flower2 className={iconClass} />,
      "massage & body oils": <Hand className={iconClass} />,
      "scrubs & exfoliants": <Sparkles className={iconClass} />,
      toners: <Droplet className={iconClass} />,
      "ubtan & masks": <Waves className={iconClass} />,

      // Individual keywords
      cleanser: <ShowerHead className={iconClass} />,
      serum: <Zap className={iconClass} />,
      moisturizer: <Droplets className={iconClass} />,
      mask: <Flower2 className={iconClass} />,
      exfoliator: <Sparkles className={iconClass} />,
      "eye-care": <Eye className={iconClass} />,
      oil: <Droplets className={iconClass} />,
      toner: <Droplet className={iconClass} />,
      kit: <Palette className={iconClass} />,
      ubtan: <Waves className={iconClass} />,
      baby: <Baby className={iconClass} />,
    };

    // Try exact match first
    if (icons[normalizedName]) {
      return icons[normalizedName];
    }

    // Try to find partial matches
    for (const [key, icon] of Object.entries(icons)) {
      if (normalizedName.includes(key) || key.includes(normalizedName)) {
        return icon;
      }
    }

    // Default icon
    return <Leaf className={iconClass} />;
  };

  return (
    <div className={`flex flex-wrap gap-3 ${className}`}>
      {categories.map((category) => {
        const icon = getCategoryIcon(category.name);

        if (showAsLinks) {
          return (
            <Link
              key={category.id}
              href={`/shop?category=${category.slug}`}
              className={`inline-flex items-center gap-1.5 ${sizeClasses[size]} font-medium text-green-600 hover:text-green-700 transition-colors`}
            >
              {icon}
              <span>{category.name}</span>
            </Link>
          );
        }

        return (
          <span
            key={category.id}
            className={`inline-flex items-center gap-1.5 ${sizeClasses[size]} font-medium text-green-600`}
          >
            {icon}
            <span>{category.name}</span>
          </span>
        );
      })}
    </div>
  );
};

export default ProductCategories;
