import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../lib/auth";
import {
  createPaymentOrder,
  convertToPaise,
  validatePaymentAmount,
} from "../../../lib/payment";
import { logger } from "../../../lib/logger";
import { asyncHandler, AuthenticationError } from "../../../lib/errors";

export const GET = asyncHandler(async (request: NextRequest) => {
  logger.apiRequest("GET", "/api/payments/test");

  // Check authentication (admin only)
  const session = await getServerSession(authOptions);
  if (!session?.user || session.user.role !== "ADMIN") {
    throw new AuthenticationError("Admin access required");
  }

  try {
    // Test payment configuration
    const testAmount = 100; // ₹1 in paise
    const receiptId = `TEST_${Date.now()}`;

    // Validate environment variables
    const requiredEnvVars = [
      "RAZORPAY_KEY_ID",
      "RAZORPAY_KEY_SECRET",
      "NEXT_PUBLIC_RAZORPAY_KEY_ID",
    ];

    const missingVars = requiredEnvVars.filter(
      (varName) => !process.env[varName],
    );

    if (missingVars.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: "Missing environment variables",
          missingVars,
        },
        { status: 500 },
      );
    }

    // Test amount validation
    const isValidAmount = validatePaymentAmount(testAmount);

    // Test order creation
    const testOrder = await createPaymentOrder({
      amount: testAmount,
      currency: "INR",
      receipt: receiptId,
      notes: {
        test: "true",
        userId: session.user.id,
        timestamp: new Date().toISOString(),
      },
    });

    logger.info("Payment test successful", {
      testOrderId: testOrder.id,
      amount: testAmount,
      receipt: receiptId,
    });

    return NextResponse.json({
      success: true,
      message: "Payment integration test successful",
      test_results: {
        environment_variables: "OK",
        amount_validation: isValidAmount ? "OK" : "FAILED",
        order_creation: "OK",
        razorpay_connection: "OK",
      },
      test_order: {
        id: testOrder.id,
        amount: testOrder.amount,
        currency: testOrder.currency,
        receipt: testOrder.receipt,
        status: testOrder.status,
      },
      configuration: {
        razorpay_key_id: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,
        environment: process.env.NODE_ENV,
      },
    });
  } catch (error) {
    logger.error("Payment test failed", error as Error);

    return NextResponse.json(
      {
        success: false,
        error: "Payment integration test failed",
        details: (error as Error).message,
        test_results: {
          environment_variables: "UNKNOWN",
          amount_validation: "UNKNOWN",
          order_creation: "FAILED",
          razorpay_connection: "FAILED",
        },
      },
      { status: 500 },
    );
  }
});
