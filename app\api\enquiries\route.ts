import { NextRequest, NextResponse } from "next/server";
import connectDB from "../../lib/mongoose";
import { Enquiry } from "../../lib/models";
import { getServerSession } from "next-auth";
import { authOptions } from "../../lib/auth";

// POST /api/enquiries - Create a new enquiry from contact form
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, subject, message } = body;

    // Validate required fields
    if (!name || !email || !subject || !message) {
      return NextResponse.json(
        { error: "All fields are required" },
        { status: 400 },
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: "Invalid email format" },
        { status: 400 },
      );
    }

    await connectDB();
    
    // Create the enquiry
    const enquiry = await Enquiry.create({
      name,
      email,
      subject,
      message,
      status: "NEW",
    });

    return NextResponse.json({
      success: true,
      message:
        "Your enquiry has been submitted successfully. We will get back to you within 24 hours.",
      enquiry: {
        id: enquiry.id,
        createdAt: enquiry.createdAt,
      },
    });
  } catch (error) {
    console.error("Error creating enquiry:", error);
    return NextResponse.json(
      { error: "Failed to submit enquiry. Please try again later." },
      { status: 500 },
    );
  }
}

// GET /api/enquiries - Get all enquiries (admin only)
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and is an admin
    if (!session?.user || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status") as any;
    const search = searchParams.get("search") || "";

    const skip = (page - 1) * limit;

    await connectDB();
    
    // Build query
    const query: any = {};

    if (status && status !== "ALL") {
      query.status = status;
    }

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { subject: { $regex: search, $options: 'i' } },
        { message: { $regex: search, $options: 'i' } },
      ];
    }

    // Get total count
    const total = await Enquiry.countDocuments(query);

    // Get enquiries
    const enquiries = await Enquiry.find(query)
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 })
      .lean();

    return NextResponse.json({
      enquiries,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching enquiries:", error);
    return NextResponse.json(
      { error: "Failed to fetch enquiries" },
      { status: 500 },
    );
  }
}
