"use client";

import React, { useState, useEffect, useCallback } from "react";
import { <PERSON>, Quote, ChevronLeft, ChevronRight } from "lucide-react";
import useEmblaCarousel from "embla-carousel-react";

interface Testimonial {
  id: string;
  name: string;
  content: string;
  rating: number;
  image?: string;
  position?: string;
  company?: string;
}

interface TestimonialsSectionProps {
  title?: string;
  subtitle?: string;
  backgroundColor?: string;
}

const TestimonialsSection: React.FC<TestimonialsSectionProps> = ({
  title = "What Our Customers Say",
  subtitle = "Real reviews from real customers who love our natural skincare",
  backgroundColor = "#f0fdf4",
}) => {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true, align: "start" });
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([]);

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const scrollTo = useCallback(
    (index: number) => {
      if (emblaApi) emblaApi.scrollTo(index);
    },
    [emblaApi],
  );

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi, setSelectedIndex]);

  useEffect(() => {
    if (!emblaApi) return;
    onSelect();
    setScrollSnaps(emblaApi.scrollSnapList());
    emblaApi.on("select", onSelect);
    emblaApi.on("reInit", onSelect);
  }, [emblaApi, onSelect]);

  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        const response = await fetch("/api/testimonials?active=true");
        const data = await response.json();

        if (data.success) {
          setTestimonials(data.data);
        }
      } catch (error) {
        console.error("Error fetching testimonials:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchTestimonials();
  }, []);
  return (
    <div className="px-4 py-8 lg:px-8 lg:py-12" style={{ backgroundColor }}>
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8 lg:mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-2 lg:text-3xl">
            {title}
          </h2>
          <p className="text-gray-600 lg:text-lg">{subtitle}</p>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div
                key={i}
                className="bg-white rounded-2xl p-6 shadow-sm animate-pulse"
              >
                <div className="w-8 h-8 bg-gray-200 rounded mb-4"></div>
                <div className="flex space-x-1 mb-3">
                  {[...Array(5)].map((_, j) => (
                    <div key={j} className="w-4 h-4 bg-gray-200 rounded"></div>
                  ))}
                </div>
                <div className="space-y-2 mb-4">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                </div>
                <div className="border-t border-gray-100 pt-3">
                  <div className="h-4 bg-gray-200 rounded mb-1"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : testimonials.length === 0 ? (
          <div className="text-center py-12">
            <Quote className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">
              No testimonials available at the moment.
            </p>
          </div>
        ) : (
          <div className="relative">
            <div className="overflow-hidden" ref={emblaRef}>
              <div className="flex">
                {testimonials.map((testimonial) => (
                  <div
                    className="flex-shrink-0 w-full md:w-1/2 lg:w-1/4 p-3"
                    key={testimonial.id}
                  >
                    <div className="bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300 h-full flex flex-col">
                      <div className="flex items-center mb-4">
                        <Quote className="w-8 h-8 text-green-500 opacity-50" />
                      </div>

                      <div className="flex items-center mb-3">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-4 h-4 ${
                              i < testimonial.rating
                                ? "text-yellow-400 fill-current"
                                : "text-gray-300"
                            }`}
                          />
                        ))}
                      </div>

                      <p className="text-gray-700 text-sm mb-4 leading-relaxed flex-grow">
                        "{testimonial.content}"
                      </p>

                      <div className="border-t border-gray-100 pt-3">
                        <p className="font-semibold text-gray-900 text-sm">
                          {testimonial.name}
                        </p>
                        {testimonial.position && testimonial.company && (
                          <p className="text-gray-500 text-xs">
                            {testimonial.position} at {testimonial.company}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <button
              className="absolute top-1/2 -translate-y-1/2 left-0 z-10 bg-white/80 rounded-full p-2 hover:bg-white transition-all duration-300 shadow-md"
              onClick={scrollPrev}
            >
              <ChevronLeft className="h-6 w-6 text-gray-800" />
            </button>
            <button
              className="absolute top-1/2 -translate-y-1/2 right-0 z-10 bg-white/80 rounded-full p-2 hover:bg-white transition-all duration-300 shadow-md"
              onClick={scrollNext}
            >
              <ChevronRight className="h-6 w-6 text-gray-800" />
            </button>
          </div>
        )}

        <div className="text-center mt-8">
          <div className="flex justify-center items-center space-x-2">
            {scrollSnaps.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === selectedIndex ? "bg-green-600" : "bg-gray-300"
                }`}
                onClick={() => scrollTo(index)}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestimonialsSection;
