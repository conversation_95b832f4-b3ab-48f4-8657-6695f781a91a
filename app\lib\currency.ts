// Currency utility functions for Indian Rupee (₹)

/**
 * Format a number as Indian Rupee currency
 * @param amount - The amount to format
 * @param showDecimals - Whether to show decimal places (default: true)
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number,
  showDecimals: boolean = true,
): string {
  if (isNaN(amount)) return "₹0";

  const formatter = new Intl.NumberFormat("en-IN", {
    style: "currency",
    currency: "INR",
    minimumFractionDigits: showDecimals ? 2 : 0,
    maximumFractionDigits: showDecimals ? 2 : 0,
  });

  return formatter.format(amount);
}

/**
 * Format a number as Indian Rupee with custom symbol
 * @param amount - The amount to format
 * @param showDecimals - Whether to show decimal places (default: true)
 * @returns Formatted currency string with ₹ symbol
 */
export function formatRupee(
  amount: number,
  showDecimals: boolean = true,
): string {
  if (isNaN(amount)) return "₹0";

  const formatted = new Intl.NumberFormat("en-IN", {
    minimumFractionDigits: showDecimals ? 2 : 0,
    maximumFractionDigits: showDecimals ? 2 : 0,
  }).format(amount);

  return `₹${formatted}`;
}

/**
 * Format a number as Indian Rupee for display in tables/lists
 * @param amount - The amount to format
 * @returns Formatted currency string
 */
export function formatPrice(amount: number): string {
  return formatRupee(amount, true);
}

/**
 * Format a number as Indian Rupee without decimals
 * @param amount - The amount to format
 * @returns Formatted currency string without decimals
 */
export function formatPriceWhole(amount: number): string {
  return formatRupee(amount, false);
}

/**
 * Get the currency symbol
 * @returns The Rupee symbol
 */
export function getCurrencySymbol(): string {
  return "₹";
}

/**
 * Get the currency code
 * @returns The currency code for Indian Rupee
 */
export function getCurrencyCode(): string {
  return "INR";
}

/**
 * Format stock quantity for display
 * @param quantity - The stock quantity (-1 for unlimited)
 * @returns Formatted stock string
 */
export function formatStock(quantity: number): string {
  if (quantity === -1) {
    return "Unlimited";
  }
  return quantity.toString();
}

/**
 * Get stock status based on quantity
 * @param quantity - The stock quantity
 * @returns Stock status
 */
export function getStockStatus(
  quantity: number,
): "unlimited" | "in-stock" | "low-stock" | "out-of-stock" {
  if (quantity === -1) return "unlimited";
  if (quantity === 0) return "out-of-stock";
  if (quantity <= 10) return "low-stock";
  return "in-stock";
}

/**
 * Get CSS color class for stock display
 * @param quantity - The stock quantity
 * @returns CSS color class
 */
export function getStockColor(quantity: number): string {
  const status = getStockStatus(quantity);
  switch (status) {
    case "unlimited":
      return "text-green-600";
    case "in-stock":
      return "text-gray-900";
    case "low-stock":
      return "text-orange-600";
    case "out-of-stock":
      return "text-red-600";
    default:
      return "text-gray-900";
  }
}

/**
 * Generate a URL-friendly slug from text
 * @param text - The text to convert to slug
 * @returns URL-friendly slug
 */
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, "") // Remove special characters except spaces and hyphens
    .replace(/[\s_-]+/g, "-") // Replace spaces, underscores, and multiple hyphens with single hyphen
    .replace(/^-+|-+$/g, ""); // Remove leading and trailing hyphens
}

/**
 * Validate and ensure slug uniqueness (basic validation)
 * @param slug - The slug to validate
 * @param fallbackText - Fallback text if slug is empty
 * @returns Valid slug
 */
export function validateSlug(slug: string, fallbackText: string): string {
  const cleanSlug = slug ? generateSlug(slug) : generateSlug(fallbackText);
  return cleanSlug || "product"; // Fallback to 'product' if all else fails
}
