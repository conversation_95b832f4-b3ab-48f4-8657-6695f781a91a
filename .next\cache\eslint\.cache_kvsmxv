[{"C:\\Users\\<USER>\\Desktop\\project\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\project\\app\\addresses\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\categories\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\coupons\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\customers\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\enquiry\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\homepage\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\layout.tsx": "8", "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\media\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\newsletter\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\notifications\\broadcast\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\notifications\\history\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\notifications\\page.tsx": "13", "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\notifications\\send\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\notifications\\templates\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\orders\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\orders\\[id]\\page.tsx": "17", "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\page.tsx": "18", "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\products\\page.tsx": "19", "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\reviews\\page.tsx": "20", "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\settings\\page.tsx": "21", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\cleanup\\route.ts": "22", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\notifications\\broadcast\\route.ts": "23", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\notifications\\history\\route.ts": "24", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\notifications\\send\\route.ts": "25", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\notifications\\stats\\route.ts": "26", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\notifications\\templates\\route.ts": "27", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\products\\export\\route.ts": "28", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\reviews\\route.ts": "29", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\users\\route.ts": "30", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\auth\\forgot-password\\route.ts": "31", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\auth\\register\\route.ts": "32", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\auth\\reset-password\\route.ts": "33", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\auth\\session-debug\\route.ts": "34", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\auth\\[...nextauth]\\route.ts": "35", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\categories\\route.ts": "36", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\categories\\[id]\\route.ts": "37", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\coupons\\route.ts": "38", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\coupons\\validate\\route.ts": "39", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\coupons\\[id]\\route.ts": "40", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\dashboard\\stats\\route.ts": "41", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\debug\\r2-check\\route.ts": "42", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\enquiries\\route.ts": "43", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\enquiries\\[id]\\route.ts": "44", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\homepage-settings\\route.ts": "45", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\media\\config\\route.ts": "46", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\media\\delete\\route.ts": "47", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\media\\list\\route.ts": "48", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\media\\upload\\route.ts": "49", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\newsletter\\export\\route.ts": "50", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\newsletter\\route.ts": "51", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\notifications\\mark-all-read\\route.ts": "52", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\notifications\\price-drop-check\\route.ts": "53", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\notifications\\review-requests\\route.ts": "54", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\notifications\\route.ts": "55", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\notifications\\unread-count\\route.ts": "56", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\notifications\\[id]\\read\\route.ts": "57", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\orders\\bulk-update\\route.ts": "58", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\orders\\route.ts": "59", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\orders\\[orderId]\\route.ts": "60", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\payments\\config\\route.ts": "61", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\payments\\create-order\\route.ts": "62", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\payments\\test\\route.ts": "63", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\payments\\verify\\route.ts": "64", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\bulk\\route.ts": "65", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\filters\\route.ts": "66", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\import\\route.ts": "67", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\optimized\\route.ts": "68", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\route.ts": "69", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\faqs\\route.ts": "70", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\faqs\\[faqId]\\route.ts": "71", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\reviews\\route.ts": "72", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\route.ts": "73", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\variations\\route.ts": "74", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\variations\\[variationId]\\route.ts": "75", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\test-email\\route.ts": "76", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\testimonials\\route.ts": "77", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\testimonials\\[id]\\route.ts": "78", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\users\\route.ts": "79", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\users\\[id]\\preferences\\route.ts": "80", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\users\\[id]\\route.ts": "81", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\users\\[id]\\stats\\route.ts": "82", "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\wishlist\\route.ts": "83", "C:\\Users\\<USER>\\Desktop\\project\\app\\cart\\page.tsx": "84", "C:\\Users\\<USER>\\Desktop\\project\\app\\categories\\page.tsx": "85", "C:\\Users\\<USER>\\Desktop\\project\\app\\checkout\\page.tsx": "86", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\CategoryMultiSelect.tsx": "87", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\ColorPicker.tsx": "88", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\FAQManagementModal.tsx": "89", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\ImageGalleryManager.tsx": "90", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\ImageSelector.tsx": "91", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\MediaPicker.tsx": "92", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\MediaPickerSimple.tsx": "93", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\OrderManagement.tsx": "94", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\ReviewManagement.tsx": "95", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\SearchableProductDropdown.tsx": "96", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\TestimonialsModal.tsx": "97", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\VariationsManagementModal.tsx": "98", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\CountdownBanner.tsx": "99", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\CouponModule.tsx": "100", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\FlashSaleBanner.tsx": "101", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\Hero.tsx": "102", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\InfiniteScroll.tsx": "103", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\Layout.tsx": "104", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\loaders\\AdminLoaders.tsx": "105", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\loaders\\SkeletonLoaders.tsx": "106", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\MobileMenu.tsx": "107", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\NewsletterSignup.tsx": "108", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\NotificationDropdown.tsx": "109", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\About.tsx": "110", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Cart.tsx": "111", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Checkout.tsx": "112", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Contact.tsx": "113", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Home.tsx": "114", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Login.tsx": "115", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\OrderConfirmation.tsx": "116", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\OrderHistory.tsx": "117", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\ProductDetail.tsx": "118", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Profile.tsx": "119", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Settings.tsx": "120", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Shop.tsx": "121", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Signup.tsx": "122", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Wishlist.tsx": "123", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\PaymentButton.tsx": "124", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\ProductCard.tsx": "125", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\ProductCategories.tsx": "126", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\ProductFAQs.tsx": "127", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\ProductImageGallery.tsx": "128", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\ProductReviews.tsx": "129", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\ProductTabs.tsx": "130", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\ProductVariationSelector.tsx": "131", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\ReviewForm.tsx": "132", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\TestimonialsSection.tsx": "133", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\Toast.tsx": "134", "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\ToastContainer.tsx": "135", "C:\\Users\\<USER>\\Desktop\\project\\app\\contact\\page.tsx": "136", "C:\\Users\\<USER>\\Desktop\\project\\app\\context\\CartContext.tsx": "137", "C:\\Users\\<USER>\\Desktop\\project\\app\\context\\FlashSaleContext.tsx": "138", "C:\\Users\\<USER>\\Desktop\\project\\app\\context\\NotificationContext.tsx": "139", "C:\\Users\\<USER>\\Desktop\\project\\app\\context\\SessionProvider.tsx": "140", "C:\\Users\\<USER>\\Desktop\\project\\app\\context\\ToastContext.tsx": "141", "C:\\Users\\<USER>\\Desktop\\project\\app\\data\\products.ts": "142", "C:\\Users\\<USER>\\Desktop\\project\\app\\edit-profile\\page.tsx": "143", "C:\\Users\\<USER>\\Desktop\\project\\app\\faq\\page.tsx": "144", "C:\\Users\\<USER>\\Desktop\\project\\app\\hooks\\useToast.ts": "145", "C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx": "146", "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\auth.ts": "147", "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\cors.ts": "148", "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\currency.ts": "149", "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\db.ts": "150", "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\email.ts": "151", "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\error-handler.ts": "152", "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\errors.ts": "153", "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\flash-sale.ts": "154", "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\images.ts": "155", "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\logger.ts": "156", "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\models.ts": "157", "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\mongoose-utils.ts": "158", "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\mongoose.ts": "159", "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\notification-helpers.ts": "160", "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\notifications.ts": "161", "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\payment.ts": "162", "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\productUtils.ts": "163", "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\r2.ts": "164", "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\rate-limit.ts": "165", "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\sanitize.ts": "166", "C:\\Users\\<USER>\\Desktop\\project\\app\\login\\page.tsx": "167", "C:\\Users\\<USER>\\Desktop\\project\\app\\notifications\\page.tsx": "168", "C:\\Users\\<USER>\\Desktop\\project\\app\\order-confirmation\\page.tsx": "169", "C:\\Users\\<USER>\\Desktop\\project\\app\\order-history\\page.tsx": "170", "C:\\Users\\<USER>\\Desktop\\project\\app\\page.tsx": "171", "C:\\Users\\<USER>\\Desktop\\project\\app\\privacy\\page.tsx": "172", "C:\\Users\\<USER>\\Desktop\\project\\app\\product\\[id]\\page.tsx": "173", "C:\\Users\\<USER>\\Desktop\\project\\app\\profile\\page.tsx": "174", "C:\\Users\\<USER>\\Desktop\\project\\app\\shipping\\page.tsx": "175", "C:\\Users\\<USER>\\Desktop\\project\\app\\shop\\page.tsx": "176", "C:\\Users\\<USER>\\Desktop\\project\\app\\signup\\page.tsx": "177", "C:\\Users\\<USER>\\Desktop\\project\\app\\sw.js\\route.ts": "178", "C:\\Users\\<USER>\\Desktop\\project\\app\\terms\\page.tsx": "179", "C:\\Users\\<USER>\\Desktop\\project\\app\\types\\index.ts": "180", "C:\\Users\\<USER>\\Desktop\\project\\app\\wishlist\\page.tsx": "181"}, {"size": 190, "mtime": 1754355069052, "results": "182", "hashOfConfig": "183"}, {"size": 5129, "mtime": 1754355069082, "results": "184", "hashOfConfig": "183"}, {"size": 18980, "mtime": 1754355069157, "results": "185", "hashOfConfig": "183"}, {"size": 56575, "mtime": 1754355069289, "results": "186", "hashOfConfig": "183"}, {"size": 13555, "mtime": 1754355069382, "results": "187", "hashOfConfig": "183"}, {"size": 13403, "mtime": 1754355069545, "results": "188", "hashOfConfig": "183"}, {"size": 49190, "mtime": 1754355069656, "results": "189", "hashOfConfig": "183"}, {"size": 5821, "mtime": 1754355069676, "results": "190", "hashOfConfig": "183"}, {"size": 27272, "mtime": 1754355069741, "results": "191", "hashOfConfig": "183"}, {"size": 16502, "mtime": 1754355069780, "results": "192", "hashOfConfig": "183"}, {"size": 8169, "mtime": 1754355069816, "results": "193", "hashOfConfig": "183"}, {"size": 12001, "mtime": 1754355069852, "results": "194", "hashOfConfig": "183"}, {"size": 8571, "mtime": 1754355069882, "results": "195", "hashOfConfig": "183"}, {"size": 12073, "mtime": 1754355069916, "results": "196", "hashOfConfig": "183"}, {"size": 12489, "mtime": 1754355069952, "results": "197", "hashOfConfig": "183"}, {"size": 36557, "mtime": 1754355070074, "results": "198", "hashOfConfig": "183"}, {"size": 25025, "mtime": 1754355070010, "results": "199", "hashOfConfig": "183"}, {"size": 9051, "mtime": 1754355070093, "results": "200", "hashOfConfig": "183"}, {"size": 58280, "mtime": 1754355070216, "results": "201", "hashOfConfig": "183"}, {"size": 562, "mtime": 1754355070227, "results": "202", "hashOfConfig": "183"}, {"size": 16039, "mtime": 1754355070262, "results": "203", "hashOfConfig": "183"}, {"size": 1354, "mtime": 1754355070281, "results": "204", "hashOfConfig": "183"}, {"size": 2586, "mtime": 1754506123710, "results": "205", "hashOfConfig": "183"}, {"size": 4037, "mtime": 1754506123655, "results": "206", "hashOfConfig": "183"}, {"size": 3716, "mtime": 1754506123710, "results": "207", "hashOfConfig": "183"}, {"size": 2435, "mtime": 1754506123706, "results": "208", "hashOfConfig": "183"}, {"size": 1371, "mtime": 1754506123707, "results": "209", "hashOfConfig": "183"}, {"size": 5306, "mtime": 1754506123707, "results": "210", "hashOfConfig": "183"}, {"size": 3652, "mtime": 1754506123711, "results": "211", "hashOfConfig": "183"}, {"size": 2808, "mtime": 1754506123708, "results": "212", "hashOfConfig": "183"}, {"size": 2287, "mtime": 1754355070463, "results": "213", "hashOfConfig": "183"}, {"size": 2583, "mtime": 1754493188055, "results": "214", "hashOfConfig": "183"}, {"size": 2310, "mtime": 1754493200500, "results": "215", "hashOfConfig": "183"}, {"size": 2534, "mtime": 1754355070502, "results": "216", "hashOfConfig": "183"}, {"size": 165, "mtime": 1754355070448, "results": "217", "hashOfConfig": "183"}, {"size": 2488, "mtime": 1754506123712, "results": "218", "hashOfConfig": "183"}, {"size": 5520, "mtime": 1754500478972, "results": "219", "hashOfConfig": "183"}, {"size": 6870, "mtime": 1754502246755, "results": "220", "hashOfConfig": "183"}, {"size": 15892, "mtime": 1754501728769, "results": "221", "hashOfConfig": "183"}, {"size": 8822, "mtime": 1754501856179, "results": "222", "hashOfConfig": "183"}, {"size": 2092, "mtime": 1754355070674, "results": "223", "hashOfConfig": "183"}, {"size": 583, "mtime": 1754355070688, "results": "224", "hashOfConfig": "183"}, {"size": 3245, "mtime": 1754506123709, "results": "225", "hashOfConfig": "183"}, {"size": 3194, "mtime": 1754506123709, "results": "226", "hashOfConfig": "183"}, {"size": 10773, "mtime": 1754506123712, "results": "227", "hashOfConfig": "183"}, {"size": 528, "mtime": 1754355070998, "results": "228", "hashOfConfig": "183"}, {"size": 985, "mtime": 1754355071015, "results": "229", "hashOfConfig": "183"}, {"size": 1067, "mtime": 1754355071028, "results": "230", "hashOfConfig": "183"}, {"size": 1626, "mtime": 1754355071045, "results": "231", "hashOfConfig": "183"}, {"size": 2917, "mtime": 1754503662094, "results": "232", "hashOfConfig": "183"}, {"size": 5190, "mtime": 1754506123712, "results": "233", "hashOfConfig": "183"}, {"size": 1141, "mtime": 1754355071112, "results": "234", "hashOfConfig": "183"}, {"size": 1364, "mtime": 1754355071122, "results": "235", "hashOfConfig": "183"}, {"size": 5925, "mtime": 1754503662083, "results": "236", "hashOfConfig": "183"}, {"size": 1418, "mtime": 1754355071149, "results": "237", "hashOfConfig": "183"}, {"size": 1048, "mtime": 1754355071160, "results": "238", "hashOfConfig": "183"}, {"size": 1208, "mtime": 1754355071103, "results": "239", "hashOfConfig": "183"}, {"size": 4767, "mtime": 1754502586585, "results": "240", "hashOfConfig": "183"}, {"size": 11568, "mtime": 1754506123714, "results": "241", "hashOfConfig": "183"}, {"size": 12160, "mtime": 1754501772063, "results": "242", "hashOfConfig": "183"}, {"size": 1435, "mtime": 1754355071292, "results": "243", "hashOfConfig": "183"}, {"size": 7501, "mtime": 1754503662083, "results": "244", "hashOfConfig": "183"}, {"size": 2970, "mtime": 1754355071362, "results": "245", "hashOfConfig": "183"}, {"size": 7458, "mtime": 1754506123714, "results": "246", "hashOfConfig": "183"}, {"size": 5647, "mtime": 1754501639410, "results": "247", "hashOfConfig": "183"}, {"size": 2503, "mtime": 1754501244675, "results": "248", "hashOfConfig": "183"}, {"size": 8122, "mtime": 1754501293179, "results": "249", "hashOfConfig": "183"}, {"size": 4728, "mtime": 1754501307244, "results": "250", "hashOfConfig": "183"}, {"size": 9153, "mtime": 1754511564973, "results": "251", "hashOfConfig": "183"}, {"size": 3070, "mtime": 1754501218019, "results": "252", "hashOfConfig": "183"}, {"size": 3612, "mtime": 1754501964988, "results": "253", "hashOfConfig": "183"}, {"size": 6373, "mtime": 1754508222304, "results": "254", "hashOfConfig": "183"}, {"size": 9072, "mtime": 1754512206739, "results": "255", "hashOfConfig": "183"}, {"size": 3903, "mtime": 1754512295804, "results": "256", "hashOfConfig": "183"}, {"size": 5301, "mtime": 1754512325345, "results": "257", "hashOfConfig": "183"}, {"size": 1199, "mtime": 1754355071674, "results": "258", "hashOfConfig": "183"}, {"size": 2473, "mtime": 1754355071705, "results": "259", "hashOfConfig": "183"}, {"size": 2994, "mtime": 1754506123714, "results": "260", "hashOfConfig": "183"}, {"size": 2634, "mtime": 1754508222301, "results": "261", "hashOfConfig": "183"}, {"size": 6314, "mtime": 1754493144434, "results": "262", "hashOfConfig": "183"}, {"size": 3447, "mtime": 1754493113733, "results": "263", "hashOfConfig": "183"}, {"size": 3916, "mtime": 1754501144795, "results": "264", "hashOfConfig": "183"}, {"size": 6527, "mtime": 1754512430948, "results": "265", "hashOfConfig": "183"}, {"size": 186, "mtime": 1754355072034, "results": "266", "hashOfConfig": "183"}, {"size": 13150, "mtime": 1754355072084, "results": "267", "hashOfConfig": "183"}, {"size": 202, "mtime": 1754355072092, "results": "268", "hashOfConfig": "183"}, {"size": 6099, "mtime": 1754355072118, "results": "269", "hashOfConfig": "183"}, {"size": 5767, "mtime": 1754355072132, "results": "270", "hashOfConfig": "183"}, {"size": 13148, "mtime": 1754355072161, "results": "271", "hashOfConfig": "183"}, {"size": 7926, "mtime": 1754355072192, "results": "272", "hashOfConfig": "183"}, {"size": 2903, "mtime": 1754355072203, "results": "273", "hashOfConfig": "183"}, {"size": 13822, "mtime": 1754355072235, "results": "274", "hashOfConfig": "183"}, {"size": 11693, "mtime": 1754355072266, "results": "275", "hashOfConfig": "183"}, {"size": 20740, "mtime": 1754355072313, "results": "276", "hashOfConfig": "183"}, {"size": 6589, "mtime": 1754355072332, "results": "277", "hashOfConfig": "183"}, {"size": 7308, "mtime": 1754355072349, "results": "278", "hashOfConfig": "183"}, {"size": 17674, "mtime": 1754355072382, "results": "279", "hashOfConfig": "183"}, {"size": 17900, "mtime": 1754355072436, "results": "280", "hashOfConfig": "183"}, {"size": 4038, "mtime": 1754355072456, "results": "281", "hashOfConfig": "183"}, {"size": 22123, "mtime": 1754355072507, "results": "282", "hashOfConfig": "183"}, {"size": 3847, "mtime": 1754355072521, "results": "283", "hashOfConfig": "183"}, {"size": 3852, "mtime": 1754355072530, "results": "284", "hashOfConfig": "183"}, {"size": 2203, "mtime": 1754355072540, "results": "285", "hashOfConfig": "183"}, {"size": 9441, "mtime": 1754355072557, "results": "286", "hashOfConfig": "183"}, {"size": 4406, "mtime": 1754355072573, "results": "287", "hashOfConfig": "183"}, {"size": 21314, "mtime": 1754355072617, "results": "288", "hashOfConfig": "183"}, {"size": 5060, "mtime": 1754355072632, "results": "289", "hashOfConfig": "183"}, {"size": 6091, "mtime": 1754355072650, "results": "290", "hashOfConfig": "183"}, {"size": 7977, "mtime": 1754355072667, "results": "291", "hashOfConfig": "183"}, {"size": 16172, "mtime": 1754355072693, "results": "292", "hashOfConfig": "183"}, {"size": 18541, "mtime": 1754355072767, "results": "293", "hashOfConfig": "183"}, {"size": 78260, "mtime": 1754355072885, "results": "294", "hashOfConfig": "183"}, {"size": 11333, "mtime": 1754355072907, "results": "295", "hashOfConfig": "183"}, {"size": 19770, "mtime": 1754355072947, "results": "296", "hashOfConfig": "183"}, {"size": 9096, "mtime": 1754519605263, "results": "297", "hashOfConfig": "183"}, {"size": 24372, "mtime": 1754355073023, "results": "298", "hashOfConfig": "183"}, {"size": 22598, "mtime": 1754355073066, "results": "299", "hashOfConfig": "183"}, {"size": 30782, "mtime": 1754355073120, "results": "300", "hashOfConfig": "183"}, {"size": 12349, "mtime": 1754355073143, "results": "301", "hashOfConfig": "183"}, {"size": 48178, "mtime": 1754355073194, "results": "302", "hashOfConfig": "183"}, {"size": 27247, "mtime": 1754355073371, "results": "303", "hashOfConfig": "183"}, {"size": 11891, "mtime": 1754355073413, "results": "304", "hashOfConfig": "183"}, {"size": 11881, "mtime": 1754355073434, "results": "305", "hashOfConfig": "183"}, {"size": 6394, "mtime": 1754355073454, "results": "306", "hashOfConfig": "183"}, {"size": 13624, "mtime": 1754355073489, "results": "307", "hashOfConfig": "183"}, {"size": 4279, "mtime": 1754355073505, "results": "308", "hashOfConfig": "183"}, {"size": 3421, "mtime": 1754355073518, "results": "309", "hashOfConfig": "183"}, {"size": 10134, "mtime": 1754355073546, "results": "310", "hashOfConfig": "183"}, {"size": 10495, "mtime": 1754355073625, "results": "311", "hashOfConfig": "183"}, {"size": 3484, "mtime": 1754355073637, "results": "312", "hashOfConfig": "183"}, {"size": 8366, "mtime": 1754355073660, "results": "313", "hashOfConfig": "183"}, {"size": 4575, "mtime": 1754355073673, "results": "314", "hashOfConfig": "183"}, {"size": 7089, "mtime": 1754355073688, "results": "315", "hashOfConfig": "183"}, {"size": 1276, "mtime": 1754355073696, "results": "316", "hashOfConfig": "183"}, {"size": 669, "mtime": 1754355073705, "results": "317", "hashOfConfig": "183"}, {"size": 198, "mtime": 1754355073713, "results": "318", "hashOfConfig": "183"}, {"size": 14026, "mtime": 1754355073754, "results": "319", "hashOfConfig": "183"}, {"size": 2683, "mtime": 1754355073764, "results": "320", "hashOfConfig": "183"}, {"size": 6081, "mtime": 1754355073788, "results": "321", "hashOfConfig": "183"}, {"size": 271, "mtime": 1754355073794, "results": "322", "hashOfConfig": "183"}, {"size": 899, "mtime": 1754355073800, "results": "323", "hashOfConfig": "183"}, {"size": 5902, "mtime": 1754355073815, "results": "324", "hashOfConfig": "183"}, {"size": 14245, "mtime": 1754355073849, "results": "325", "hashOfConfig": "183"}, {"size": 1882, "mtime": 1754355073864, "results": "326", "hashOfConfig": "183"}, {"size": 659, "mtime": 1754355073905, "results": "327", "hashOfConfig": "183"}, {"size": 1316, "mtime": 1754355073915, "results": "328", "hashOfConfig": "183"}, {"size": 6343, "mtime": 1754518982456, "results": "329", "hashOfConfig": "183"}, {"size": 952, "mtime": 1754355073948, "results": "330", "hashOfConfig": "183"}, {"size": 4008, "mtime": 1754355073963, "results": "331", "hashOfConfig": "183"}, {"size": 153, "mtime": 1754355073969, "results": "332", "hashOfConfig": "183"}, {"size": 10774, "mtime": 1754355073991, "results": "333", "hashOfConfig": "183"}, {"size": 5490, "mtime": 1754355074015, "results": "334", "hashOfConfig": "183"}, {"size": 9484, "mtime": 1754355074052, "results": "335", "hashOfConfig": "183"}, {"size": 1272, "mtime": 1754355074061, "results": "336", "hashOfConfig": "183"}, {"size": 3201, "mtime": 1754355074073, "results": "337", "hashOfConfig": "183"}, {"size": 7860, "mtime": 1754355074102, "results": "338", "hashOfConfig": "183"}, {"size": 17389, "mtime": 1754510175079, "results": "339", "hashOfConfig": "183"}, {"size": 9537, "mtime": 1754506123770, "results": "340", "hashOfConfig": "183"}, {"size": 1006, "mtime": 1754355074185, "results": "341", "hashOfConfig": "183"}, {"size": 12671, "mtime": 1754355074213, "results": "342", "hashOfConfig": "183"}, {"size": 6510, "mtime": 1754355074239, "results": "343", "hashOfConfig": "183"}, {"size": 4793, "mtime": 1754355074258, "results": "344", "hashOfConfig": "183"}, {"size": 2116, "mtime": 1754355074266, "results": "345", "hashOfConfig": "183"}, {"size": 8907, "mtime": 1754355074292, "results": "346", "hashOfConfig": "183"}, {"size": 1897, "mtime": 1754355074302, "results": "347", "hashOfConfig": "183"}, {"size": 4287, "mtime": 1754355074317, "results": "348", "hashOfConfig": "183"}, {"size": 190, "mtime": 1754355074326, "results": "349", "hashOfConfig": "183"}, {"size": 14808, "mtime": 1754355074355, "results": "350", "hashOfConfig": "183"}, {"size": 893, "mtime": 1754355074363, "results": "351", "hashOfConfig": "183"}, {"size": 218, "mtime": 1754355074371, "results": "352", "hashOfConfig": "183"}, {"size": 184, "mtime": 1754355074376, "results": "353", "hashOfConfig": "183"}, {"size": 1425, "mtime": 1754355074386, "results": "354", "hashOfConfig": "183"}, {"size": 275, "mtime": 1754355074401, "results": "355", "hashOfConfig": "183"}, {"size": 198, "mtime": 1754355074409, "results": "356", "hashOfConfig": "183"}, {"size": 1236, "mtime": 1754355074417, "results": "357", "hashOfConfig": "183"}, {"size": 810, "mtime": 1754355074427, "results": "358", "hashOfConfig": "183"}, {"size": 194, "mtime": 1754355074434, "results": "359", "hashOfConfig": "183"}, {"size": 650, "mtime": 1754355074444, "results": "360", "hashOfConfig": "183"}, {"size": 995, "mtime": 1754355074452, "results": "361", "hashOfConfig": "183"}, {"size": 4620, "mtime": 1754506123716, "results": "362", "hashOfConfig": "183"}, {"size": 202, "mtime": 1754355074720, "results": "363", "hashOfConfig": "183"}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1rapwej", {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 11, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 15, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 16, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 21, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 13, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 16, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 23, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 17, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 11, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 13, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 24, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 22, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 13, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "883", "messages": "884", "suppressedMessages": "885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "901", "messages": "902", "suppressedMessages": "903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "904", "messages": "905", "suppressedMessages": "906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\project\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\addresses\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\categories\\page.tsx", ["907", "908", "909", "910", "911"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\coupons\\page.tsx", ["912", "913", "914", "915", "916", "917", "918"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\customers\\page.tsx", ["919", "920", "921", "922", "923", "924", "925", "926", "927", "928", "929"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\enquiry\\page.tsx", ["930"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\homepage\\page.tsx", ["931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\media\\page.tsx", ["949", "950", "951", "952", "953", "954", "955"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\newsletter\\page.tsx", ["956", "957", "958", "959", "960"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\notifications\\broadcast\\page.tsx", ["961"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\notifications\\history\\page.tsx", ["962", "963", "964", "965"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\notifications\\page.tsx", ["966", "967", "968"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\notifications\\send\\page.tsx", ["969"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\notifications\\templates\\page.tsx", ["970", "971"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\orders\\page.tsx", ["972", "973", "974", "975", "976", "977", "978", "979", "980"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\orders\\[id]\\page.tsx", ["981", "982", "983"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\page.tsx", ["984", "985", "986", "987", "988", "989", "990", "991"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\products\\page.tsx", ["992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\reviews\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\settings\\page.tsx", ["1009", "1010", "1011", "1012", "1013"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\cleanup\\route.ts", ["1014"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\notifications\\broadcast\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\notifications\\history\\route.ts", ["1015", "1016"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\notifications\\send\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\notifications\\stats\\route.ts", ["1017"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\notifications\\templates\\route.ts", ["1018"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\products\\export\\route.ts", ["1019", "1020", "1021", "1022", "1023", "1024"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\reviews\\route.ts", ["1025", "1026", "1027", "1028", "1029", "1030"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\admin\\users\\route.ts", ["1031"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\auth\\forgot-password\\route.ts", ["1032", "1033", "1034"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\auth\\register\\route.ts", ["1035", "1036"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\auth\\reset-password\\route.ts", ["1037"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\auth\\session-debug\\route.ts", ["1038"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\categories\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\categories\\[id]\\route.ts", ["1039", "1040", "1041", "1042"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\coupons\\route.ts", ["1043", "1044", "1045", "1046", "1047", "1048", "1049", "1050", "1051"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\coupons\\validate\\route.ts", ["1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059", "1060", "1061", "1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\coupons\\[id]\\route.ts", ["1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\dashboard\\stats\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\debug\\r2-check\\route.ts", ["1086"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\enquiries\\route.ts", ["1087", "1088"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\enquiries\\[id]\\route.ts", ["1089"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\homepage-settings\\route.ts", ["1090", "1091", "1092"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\media\\config\\route.ts", ["1093"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\media\\delete\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\media\\list\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\media\\upload\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\newsletter\\export\\route.ts", ["1094"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\newsletter\\route.ts", ["1095", "1096", "1097", "1098", "1099", "1100"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\notifications\\mark-all-read\\route.ts", ["1101"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\notifications\\price-drop-check\\route.ts", ["1102"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\notifications\\review-requests\\route.ts", ["1103", "1104", "1105", "1106"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\notifications\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\notifications\\unread-count\\route.ts", ["1107"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\notifications\\[id]\\read\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\orders\\bulk-update\\route.ts", ["1108", "1109", "1110", "1111", "1112", "1113", "1114"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\orders\\route.ts", ["1115", "1116", "1117", "1118", "1119", "1120", "1121", "1122", "1123", "1124", "1125", "1126", "1127", "1128", "1129", "1130"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\orders\\[orderId]\\route.ts", ["1131", "1132", "1133", "1134", "1135", "1136", "1137", "1138", "1139", "1140", "1141", "1142", "1143", "1144", "1145", "1146", "1147", "1148", "1149", "1150", "1151", "1152", "1153"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\payments\\config\\route.ts", ["1154"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\payments\\create-order\\route.ts", ["1155", "1156"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\payments\\test\\route.ts", ["1157", "1158"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\payments\\verify\\route.ts", ["1159", "1160", "1161", "1162", "1163", "1164", "1165", "1166", "1167", "1168", "1169", "1170", "1171", "1172", "1173", "1174", "1175"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\bulk\\route.ts", ["1176", "1177", "1178", "1179"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\filters\\route.ts", ["1180", "1181", "1182"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\import\\route.ts", ["1183", "1184", "1185", "1186", "1187", "1188", "1189", "1190", "1191", "1192", "1193"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\optimized\\route.ts", ["1194", "1195", "1196", "1197", "1198", "1199", "1200", "1201"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\route.ts", ["1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209", "1210", "1211"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\faqs\\route.ts", ["1212"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\faqs\\[faqId]\\route.ts", ["1213", "1214", "1215", "1216", "1217", "1218"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\reviews\\route.ts", ["1219", "1220", "1221", "1222", "1223", "1224", "1225", "1226"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\route.ts", ["1227", "1228", "1229", "1230", "1231", "1232", "1233", "1234", "1235", "1236", "1237", "1238", "1239"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\variations\\route.ts", ["1240"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\products\\[id]\\variations\\[variationId]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\test-email\\route.ts", ["1241"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\testimonials\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\testimonials\\[id]\\route.ts", ["1242", "1243"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\users\\route.ts", ["1244", "1245"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\users\\[id]\\preferences\\route.ts", ["1246", "1247", "1248", "1249", "1250", "1251"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\users\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\users\\[id]\\stats\\route.ts", ["1252", "1253", "1254"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\api\\wishlist\\route.ts", ["1255", "1256", "1257", "1258", "1259", "1260", "1261"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\cart\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\categories\\page.tsx", ["1262", "1263", "1264", "1265", "1266", "1267", "1268"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\checkout\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\CategoryMultiSelect.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\ColorPicker.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\FAQManagementModal.tsx", ["1269"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\ImageGalleryManager.tsx", ["1270", "1271", "1272"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\ImageSelector.tsx", ["1273", "1274"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\MediaPicker.tsx", ["1275", "1276", "1277", "1278", "1279"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\MediaPickerSimple.tsx", ["1280", "1281", "1282"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\OrderManagement.tsx", ["1283", "1284"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\ReviewManagement.tsx", ["1285"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\SearchableProductDropdown.tsx", ["1286", "1287", "1288", "1289"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\TestimonialsModal.tsx", ["1290"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\admin\\VariationsManagementModal.tsx", ["1291"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\CountdownBanner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\CouponModule.tsx", ["1292", "1293"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\FlashSaleBanner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\InfiniteScroll.tsx", ["1294"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\loaders\\AdminLoaders.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\loaders\\SkeletonLoaders.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\MobileMenu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\NewsletterSignup.tsx", ["1295"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\NotificationDropdown.tsx", ["1296", "1297", "1298"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\About.tsx", ["1299", "1300", "1301"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Cart.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Checkout.tsx", ["1302", "1303", "1304", "1305"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Contact.tsx", ["1306", "1307"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Home.tsx", ["1308", "1309", "1310", "1311", "1312", "1313", "1314", "1315", "1316", "1317", "1318", "1319", "1320", "1321", "1322"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Login.tsx", ["1323"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\OrderConfirmation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\OrderHistory.tsx", ["1324"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\ProductDetail.tsx", ["1325", "1326", "1327", "1328", "1329", "1330", "1331", "1332", "1333"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Profile.tsx", ["1334", "1335", "1336", "1337", "1338", "1339", "1340"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Settings.tsx", ["1341", "1342", "1343", "1344"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Shop.tsx", ["1345", "1346", "1347", "1348", "1349", "1350", "1351", "1352"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Signup.tsx", ["1353"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Wishlist.tsx", ["1354", "1355", "1356", "1357", "1358", "1359", "1360", "1361"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\PaymentButton.tsx", ["1362", "1363"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\ProductCard.tsx", ["1364", "1365", "1366", "1367", "1368", "1369", "1370"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\ProductCategories.tsx", ["1371"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\ProductFAQs.tsx", ["1372", "1373"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\ProductImageGallery.tsx", ["1374", "1375", "1376", "1377", "1378"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\ProductReviews.tsx", ["1379", "1380", "1381", "1382", "1383", "1384"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\ProductTabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\ProductVariationSelector.tsx", ["1385", "1386", "1387", "1388", "1389", "1390"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\ReviewForm.tsx", ["1391"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\TestimonialsSection.tsx", ["1392", "1393"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\Toast.tsx", ["1394"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\components\\ToastContainer.tsx", ["1395"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\context\\CartContext.tsx", ["1396", "1397", "1398"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\context\\FlashSaleContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\context\\NotificationContext.tsx", ["1399", "1400"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\context\\SessionProvider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\context\\ToastContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\data\\products.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\edit-profile\\page.tsx", ["1401"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\faq\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\hooks\\useToast.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\auth.ts", ["1402", "1403", "1404", "1405", "1406"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\cors.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\currency.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\db.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\email.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\error-handler.ts", ["1407", "1408", "1409", "1410", "1411", "1412", "1413"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\errors.ts", ["1414", "1415", "1416", "1417", "1418", "1419", "1420", "1421", "1422", "1423", "1424", "1425"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\flash-sale.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\images.ts", ["1426"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\logger.ts", ["1427", "1428", "1429", "1430", "1431", "1432", "1433", "1434", "1435", "1436", "1437", "1438", "1439", "1440", "1441", "1442", "1443", "1444", "1445", "1446", "1447", "1448", "1449", "1450"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\models.ts", ["1451"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\mongoose-utils.ts", ["1452", "1453", "1454", "1455", "1456", "1457", "1458", "1459", "1460", "1461", "1462", "1463", "1464", "1465", "1466", "1467", "1468", "1469", "1470", "1471", "1472", "1473", "1474"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\mongoose.ts", ["1475", "1476"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\notification-helpers.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\notifications.ts", ["1477", "1478", "1479", "1480", "1481", "1482", "1483", "1484", "1485", "1486", "1487", "1488", "1489"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\payment.ts", ["1490", "1491", "1492"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\productUtils.ts", ["1493", "1494", "1495", "1496", "1497"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\r2.ts", ["1498"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\rate-limit.ts", ["1499"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\lib\\sanitize.ts", ["1500", "1501"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\notifications\\page.tsx", ["1502", "1503", "1504", "1505", "1506", "1507", "1508", "1509"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\order-confirmation\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\order-history\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\privacy\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\product\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\profile\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\shipping\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\shop\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\signup\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\sw.js\\route.ts", ["1510", "1511"], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\terms\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\project\\app\\wishlist\\page.tsx", [], [], {"ruleId": "1512", "severity": 2, "message": "1513", "line": 9, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 9, "endColumn": 15}, {"ruleId": "1512", "severity": 2, "message": "1515", "line": 11, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 11, "endColumn": 13}, {"ruleId": "1512", "severity": 2, "message": "1516", "line": 12, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 12, "endColumn": 15}, {"ruleId": "1512", "severity": 2, "message": "1517", "line": 13, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 13, "endColumn": 14}, {"ruleId": "1518", "severity": 1, "message": "1519", "line": 247, "column": 8, "nodeType": "1520", "endLine": 247, "endColumn": 25, "suggestions": "1521"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 119, "column": 38, "nodeType": "1524", "messageId": "1525", "endLine": 119, "endColumn": 41, "suggestions": "1526"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 360, "column": 36, "nodeType": "1524", "messageId": "1525", "endLine": 360, "endColumn": 39, "suggestions": "1527"}, {"ruleId": "1528", "severity": 2, "message": "1529", "line": 368, "column": 24, "nodeType": "1530", "messageId": "1531", "suggestions": "1532"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 654, "column": 63, "nodeType": "1524", "messageId": "1525", "endLine": 654, "endColumn": 66, "suggestions": "1533"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 655, "column": 44, "nodeType": "1524", "messageId": "1525", "endLine": 655, "endColumn": 47, "suggestions": "1534"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 703, "column": 63, "nodeType": "1524", "messageId": "1525", "endLine": 703, "endColumn": 66, "suggestions": "1535"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 794, "column": 74, "nodeType": "1524", "messageId": "1525", "endLine": 794, "endColumn": 77, "suggestions": "1536"}, {"ruleId": "1512", "severity": 2, "message": "1537", "line": 9, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 9, "endColumn": 8}, {"ruleId": "1512", "severity": 2, "message": "1538", "line": 10, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 10, "endColumn": 9}, {"ruleId": "1512", "severity": 2, "message": "1539", "line": 11, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 11, "endColumn": 11}, {"ruleId": "1512", "severity": 2, "message": "1513", "line": 14, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 14, "endColumn": 15}, {"ruleId": "1512", "severity": 2, "message": "1540", "line": 15, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 15, "endColumn": 7}, {"ruleId": "1512", "severity": 2, "message": "1541", "line": 18, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 18, "endColumn": 7}, {"ruleId": "1512", "severity": 2, "message": "1542", "line": 42, "column": 10, "nodeType": null, "messageId": "1514", "endLine": 42, "endColumn": 26}, {"ruleId": "1512", "severity": 2, "message": "1543", "line": 42, "column": 28, "nodeType": null, "messageId": "1514", "endLine": 42, "endColumn": 47}, {"ruleId": "1512", "severity": 2, "message": "1544", "line": 45, "column": 10, "nodeType": null, "messageId": "1514", "endLine": 45, "endColumn": 29}, {"ruleId": "1512", "severity": 2, "message": "1545", "line": 45, "column": 31, "nodeType": null, "messageId": "1514", "endLine": 45, "endColumn": 53}, {"ruleId": "1512", "severity": 2, "message": "1546", "line": 46, "column": 10, "nodeType": null, "messageId": "1514", "endLine": 46, "endColumn": 22}, {"ruleId": "1518", "severity": 1, "message": "1547", "line": 71, "column": 6, "nodeType": "1520", "endLine": 71, "endColumn": 70, "suggestions": "1548"}, {"ruleId": "1512", "severity": 2, "message": "1549", "line": 3, "column": 31, "nodeType": null, "messageId": "1514", "endLine": 3, "endColumn": 38}, {"ruleId": "1512", "severity": 2, "message": "1550", "line": 11, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 11, "endColumn": 10}, {"ruleId": "1512", "severity": 2, "message": "1551", "line": 12, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 12, "endColumn": 8}, {"ruleId": "1512", "severity": 2, "message": "1552", "line": 13, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 13, "endColumn": 11}, {"ruleId": "1518", "severity": 1, "message": "1553", "line": 186, "column": 6, "nodeType": "1520", "endLine": 186, "endColumn": 31, "suggestions": "1554"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 317, "column": 67, "nodeType": "1524", "messageId": "1525", "endLine": 317, "endColumn": 70, "suggestions": "1555"}, {"ruleId": "1528", "severity": 2, "message": "1529", "line": 357, "column": 36, "nodeType": "1530", "messageId": "1531", "suggestions": "1556"}, {"ruleId": "1528", "severity": 2, "message": "1557", "line": 667, "column": 43, "nodeType": "1530", "messageId": "1531", "suggestions": "1558"}, {"ruleId": "1528", "severity": 2, "message": "1557", "line": 667, "column": 52, "nodeType": "1530", "messageId": "1531", "suggestions": "1559"}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 683, "column": 23, "nodeType": "1562", "endLine": 687, "endColumn": 25}, {"ruleId": "1528", "severity": 2, "message": "1557", "line": 770, "column": 41, "nodeType": "1530", "messageId": "1531", "suggestions": "1563"}, {"ruleId": "1528", "severity": 2, "message": "1557", "line": 770, "column": 50, "nodeType": "1530", "messageId": "1531", "suggestions": "1564"}, {"ruleId": "1528", "severity": 2, "message": "1557", "line": 770, "column": 53, "nodeType": "1530", "messageId": "1531", "suggestions": "1565"}, {"ruleId": "1528", "severity": 2, "message": "1557", "line": 770, "column": 64, "nodeType": "1530", "messageId": "1531", "suggestions": "1566"}, {"ruleId": "1528", "severity": 2, "message": "1557", "line": 770, "column": 70, "nodeType": "1530", "messageId": "1531", "suggestions": "1567"}, {"ruleId": "1528", "severity": 2, "message": "1557", "line": 770, "column": 80, "nodeType": "1530", "messageId": "1531", "suggestions": "1568"}, {"ruleId": "1512", "severity": 2, "message": "1569", "line": 856, "column": 57, "nodeType": null, "messageId": "1514", "endLine": 856, "endColumn": 62}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 865, "column": 27, "nodeType": "1562", "endLine": 869, "endColumn": 29}, {"ruleId": "1512", "severity": 2, "message": "1570", "line": 7, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 7, "endColumn": 9}, {"ruleId": "1512", "severity": 2, "message": "1571", "line": 12, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 12, "endColumn": 13}, {"ruleId": "1512", "severity": 2, "message": "1572", "line": 38, "column": 10, "nodeType": null, "messageId": "1514", "endLine": 38, "endColumn": 19}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 346, "column": 15, "nodeType": "1562", "endLine": 350, "endColumn": 17}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 493, "column": 64, "nodeType": "1524", "messageId": "1525", "endLine": 493, "endColumn": 67, "suggestions": "1573"}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 569, "column": 21, "nodeType": "1562", "endLine": 573, "endColumn": 23}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 690, "column": 29, "nodeType": "1562", "endLine": 694, "endColumn": 31}, {"ruleId": "1512", "severity": 2, "message": "1574", "line": 10, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 10, "endColumn": 7}, {"ruleId": "1512", "severity": 2, "message": "1539", "line": 11, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 11, "endColumn": 11}, {"ruleId": "1512", "severity": 2, "message": "1570", "line": 12, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 12, "endColumn": 9}, {"ruleId": "1512", "severity": 2, "message": "1575", "line": 13, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 13, "endColumn": 9}, {"ruleId": "1518", "severity": 1, "message": "1576", "line": 74, "column": 6, "nodeType": "1520", "endLine": 74, "endColumn": 57, "suggestions": "1577"}, {"ruleId": "1512", "severity": 2, "message": "1551", "line": 10, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 10, "endColumn": 8}, {"ruleId": "1512", "severity": 2, "message": "1570", "line": 8, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 8, "endColumn": 9}, {"ruleId": "1512", "severity": 2, "message": "1539", "line": 9, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 9, "endColumn": 11}, {"ruleId": "1512", "severity": 2, "message": "1574", "line": 11, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 11, "endColumn": 7}, {"ruleId": "1518", "severity": 1, "message": "1578", "line": 48, "column": 6, "nodeType": "1520", "endLine": 48, "endColumn": 38, "suggestions": "1579"}, {"ruleId": "1512", "severity": 2, "message": "1580", "line": 6, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 6, "endColumn": 7}, {"ruleId": "1512", "severity": 2, "message": "1551", "line": 10, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 10, "endColumn": 8}, {"ruleId": "1512", "severity": 2, "message": "1570", "line": 16, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 16, "endColumn": 9}, {"ruleId": "1512", "severity": 2, "message": "1581", "line": 34, "column": 10, "nodeType": null, "messageId": "1514", "endLine": 34, "endColumn": 23}, {"ruleId": "1512", "severity": 2, "message": "1582", "line": 11, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 11, "endColumn": 11}, {"ruleId": "1512", "severity": 2, "message": "1583", "line": 35, "column": 10, "nodeType": null, "messageId": "1514", "endLine": 35, "endColumn": 24}, {"ruleId": "1512", "severity": 2, "message": "1570", "line": 6, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 6, "endColumn": 9}, {"ruleId": "1512", "severity": 2, "message": "1539", "line": 14, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 14, "endColumn": 11}, {"ruleId": "1512", "severity": 2, "message": "1584", "line": 15, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 15, "endColumn": 13}, {"ruleId": "1512", "severity": 2, "message": "1585", "line": 16, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 16, "endColumn": 7}, {"ruleId": "1512", "severity": 2, "message": "1538", "line": 17, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 17, "endColumn": 9}, {"ruleId": "1512", "severity": 2, "message": "1586", "line": 18, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 18, "endColumn": 13}, {"ruleId": "1512", "severity": 2, "message": "1513", "line": 27, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 27, "endColumn": 15}, {"ruleId": "1518", "severity": 1, "message": "1587", "line": 148, "column": 6, "nodeType": "1520", "endLine": 148, "endColumn": 50, "suggestions": "1588"}, {"ruleId": "1512", "severity": 2, "message": "1589", "line": 231, "column": 9, "nodeType": null, "messageId": "1514", "endLine": 231, "endColumn": 24}, {"ruleId": "1512", "severity": 2, "message": "1590", "line": 23, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 23, "endColumn": 11}, {"ruleId": "1518", "severity": 1, "message": "1591", "line": 153, "column": 6, "nodeType": "1520", "endLine": 153, "endColumn": 26, "suggestions": "1592"}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 522, "column": 21, "nodeType": "1562", "endLine": 526, "endColumn": 23}, {"ruleId": "1512", "severity": 2, "message": "1584", "line": 8, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 8, "endColumn": 13}, {"ruleId": "1512", "severity": 2, "message": "1593", "line": 11, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 11, "endColumn": 6}, {"ruleId": "1512", "severity": 2, "message": "1539", "line": 14, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 14, "endColumn": 11}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 21, "column": 54, "nodeType": "1524", "messageId": "1525", "endLine": 21, "endColumn": 57, "suggestions": "1594"}, {"ruleId": "1528", "severity": 2, "message": "1529", "line": 121, "column": 31, "nodeType": "1530", "messageId": "1531", "suggestions": "1595"}, {"ruleId": "1528", "severity": 2, "message": "1529", "line": 121, "column": 38, "nodeType": "1530", "messageId": "1531", "suggestions": "1596"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 204, "column": 48, "nodeType": "1524", "messageId": "1525", "endLine": 204, "endColumn": 51, "suggestions": "1597"}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 210, "column": 25, "nodeType": "1562", "endLine": 217, "endColumn": 27}, {"ruleId": "1512", "severity": 2, "message": "1513", "line": 9, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 9, "endColumn": 15}, {"ruleId": "1512", "severity": 2, "message": "1541", "line": 11, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 11, "endColumn": 7}, {"ruleId": "1512", "severity": 2, "message": "1593", "line": 12, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 12, "endColumn": 6}, {"ruleId": "1512", "severity": 2, "message": "1598", "line": 13, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 13, "endColumn": 10}, {"ruleId": "1512", "severity": 2, "message": "1599", "line": 20, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 20, "endColumn": 14}, {"ruleId": "1512", "severity": 2, "message": "1600", "line": 21, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 21, "endColumn": 9}, {"ruleId": "1512", "severity": 2, "message": "1601", "line": 22, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 22, "endColumn": 4}, {"ruleId": "1512", "severity": 2, "message": "1602", "line": 26, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 26, "endColumn": 14}, {"ruleId": "1512", "severity": 2, "message": "1603", "line": 27, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 27, "endColumn": 16}, {"ruleId": "1512", "severity": 2, "message": "1604", "line": 31, "column": 8, "nodeType": null, "messageId": "1514", "endLine": 31, "endColumn": 21}, {"ruleId": "1512", "severity": 2, "message": "1605", "line": 307, "column": 20, "nodeType": null, "messageId": "1514", "endLine": 307, "endColumn": 29}, {"ruleId": "1512", "severity": 2, "message": "1606", "line": 378, "column": 24, "nodeType": null, "messageId": "1514", "endLine": 378, "endColumn": 25}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 607, "column": 74, "nodeType": "1524", "messageId": "1525", "endLine": 607, "endColumn": 77, "suggestions": "1607"}, {"ruleId": "1512", "severity": 2, "message": "1608", "line": 723, "column": 9, "nodeType": null, "messageId": "1514", "endLine": 723, "endColumn": 23}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 1004, "column": 27, "nodeType": "1562", "endLine": 1011, "endColumn": 29}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 1027, "column": 66, "nodeType": "1524", "messageId": "1525", "endLine": 1027, "endColumn": 69, "suggestions": "1609"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 1227, "column": 51, "nodeType": "1524", "messageId": "1525", "endLine": 1227, "endColumn": 54, "suggestions": "1610"}, {"ruleId": "1512", "severity": 2, "message": "1582", "line": 6, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 6, "endColumn": 11}, {"ruleId": "1512", "severity": 2, "message": "1550", "line": 10, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 10, "endColumn": 10}, {"ruleId": "1512", "severity": 2, "message": "1611", "line": 14, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 14, "endColumn": 8}, {"ruleId": "1512", "severity": 2, "message": "1551", "line": 15, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 15, "endColumn": 8}, {"ruleId": "1512", "severity": 2, "message": "1612", "line": 16, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 16, "endColumn": 11}, {"ruleId": "1512", "severity": 2, "message": "1613", "line": 12, "column": 28, "nodeType": null, "messageId": "1514", "endLine": 12, "endColumn": 35}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 35, "column": 18, "nodeType": "1524", "messageId": "1525", "endLine": 35, "endColumn": 21, "suggestions": "1614"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 80, "column": 26, "nodeType": "1524", "messageId": "1525", "endLine": 80, "endColumn": 29, "suggestions": "1615"}, {"ruleId": "1512", "severity": 2, "message": "1613", "line": 12, "column": 27, "nodeType": null, "messageId": "1514", "endLine": 12, "endColumn": 34}, {"ruleId": "1512", "severity": 2, "message": "1613", "line": 12, "column": 27, "nodeType": null, "messageId": "1514", "endLine": 12, "endColumn": 34}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 47, "column": 40, "nodeType": "1524", "messageId": "1525", "endLine": 47, "endColumn": 43, "suggestions": "1616"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 61, "column": 42, "nodeType": "1524", "messageId": "1525", "endLine": 61, "endColumn": 45, "suggestions": "1617"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 68, "column": 46, "nodeType": "1524", "messageId": "1525", "endLine": 68, "endColumn": 49, "suggestions": "1618"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 102, "column": 52, "nodeType": "1524", "messageId": "1525", "endLine": 102, "endColumn": 55, "suggestions": "1619"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 104, "column": 38, "nodeType": "1524", "messageId": "1525", "endLine": 104, "endColumn": 41, "suggestions": "1620"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 108, "column": 40, "nodeType": "1524", "messageId": "1525", "endLine": 108, "endColumn": 43, "suggestions": "1621"}, {"ruleId": "1512", "severity": 2, "message": "1622", "line": 5, "column": 16, "nodeType": null, "messageId": "1514", "endLine": 5, "endColumn": 22}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 40, "column": 27, "nodeType": "1524", "messageId": "1525", "endLine": 40, "endColumn": 30, "suggestions": "1623"}, {"ruleId": "1512", "severity": 2, "message": "1624", "line": 53, "column": 11, "nodeType": null, "messageId": "1514", "endLine": 53, "endColumn": 17}, {"ruleId": "1512", "severity": 2, "message": "1625", "line": 57, "column": 11, "nodeType": null, "messageId": "1514", "endLine": 57, "endColumn": 15}, {"ruleId": "1512", "severity": 2, "message": "1613", "line": 81, "column": 29, "nodeType": null, "messageId": "1514", "endLine": 81, "endColumn": 36}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 105, "column": 27, "nodeType": "1524", "messageId": "1525", "endLine": 105, "endColumn": 30, "suggestions": "1626"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 35, "column": 18, "nodeType": "1524", "messageId": "1525", "endLine": 35, "endColumn": 21, "suggestions": "1627"}, {"ruleId": "1512", "severity": 2, "message": "1628", "line": 8, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 8, "endColumn": 17}, {"ruleId": "1512", "severity": 2, "message": "1629", "line": 9, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 9, "endColumn": 18}, {"ruleId": "1512", "severity": 2, "message": "1630", "line": 10, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 10, "endColumn": 17}, {"ruleId": "1512", "severity": 2, "message": "1628", "line": 7, "column": 10, "nodeType": null, "messageId": "1514", "endLine": 7, "endColumn": 24}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 62, "column": 23, "nodeType": "1524", "messageId": "1525", "endLine": 62, "endColumn": 26, "suggestions": "1631"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 51, "column": 23, "nodeType": "1524", "messageId": "1525", "endLine": 51, "endColumn": 26, "suggestions": "1632"}, {"ruleId": "1512", "severity": 2, "message": "1633", "line": 56, "column": 16, "nodeType": null, "messageId": "1514", "endLine": 56, "endColumn": 21}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 7, "column": 8, "nodeType": "1524", "messageId": "1525", "endLine": 7, "endColumn": 11, "suggestions": "1634"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 16, "column": 20, "nodeType": "1524", "messageId": "1525", "endLine": 16, "endColumn": 23, "suggestions": "1635"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 83, "column": 19, "nodeType": "1524", "messageId": "1525", "endLine": 83, "endColumn": 22, "suggestions": "1636"}, {"ruleId": "1512", "severity": 2, "message": "1637", "line": 147, "column": 14, "nodeType": null, "messageId": "1514", "endLine": 147, "endColumn": 18}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 47, "column": 19, "nodeType": "1524", "messageId": "1525", "endLine": 47, "endColumn": 22, "suggestions": "1638"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 66, "column": 44, "nodeType": "1524", "messageId": "1525", "endLine": 66, "endColumn": 47, "suggestions": "1639"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 90, "column": 13, "nodeType": "1524", "messageId": "1525", "endLine": 90, "endColumn": 16, "suggestions": "1640"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 99, "column": 24, "nodeType": "1524", "messageId": "1525", "endLine": 99, "endColumn": 27, "suggestions": "1641"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 103, "column": 43, "nodeType": "1524", "messageId": "1525", "endLine": 103, "endColumn": 46, "suggestions": "1642"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 107, "column": 50, "nodeType": "1524", "messageId": "1525", "endLine": 107, "endColumn": 53, "suggestions": "1643"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 114, "column": 36, "nodeType": "1524", "messageId": "1525", "endLine": 114, "endColumn": 39, "suggestions": "1644"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 141, "column": 44, "nodeType": "1524", "messageId": "1525", "endLine": 141, "endColumn": 47, "suggestions": "1645"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 203, "column": 19, "nodeType": "1524", "messageId": "1525", "endLine": 203, "endColumn": 22, "suggestions": "1646"}, {"ruleId": "1512", "severity": 2, "message": "1647", "line": 21, "column": 11, "nodeType": null, "messageId": "1514", "endLine": 21, "endColumn": 18}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 59, "column": 20, "nodeType": "1524", "messageId": "1525", "endLine": 59, "endColumn": 23, "suggestions": "1648"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 98, "column": 10, "nodeType": "1524", "messageId": "1525", "endLine": 98, "endColumn": 13, "suggestions": "1649"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 172, "column": 39, "nodeType": "1524", "messageId": "1525", "endLine": 172, "endColumn": 42, "suggestions": "1650"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 209, "column": 59, "nodeType": "1524", "messageId": "1525", "endLine": 209, "endColumn": 62, "suggestions": "1651"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 220, "column": 15, "nodeType": "1524", "messageId": "1525", "endLine": 220, "endColumn": 18, "suggestions": "1652"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 227, "column": 27, "nodeType": "1524", "messageId": "1525", "endLine": 227, "endColumn": 30, "suggestions": "1653"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 238, "column": 28, "nodeType": "1524", "messageId": "1525", "endLine": 238, "endColumn": 31, "suggestions": "1654"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 253, "column": 30, "nodeType": "1524", "messageId": "1525", "endLine": 253, "endColumn": 33, "suggestions": "1655"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 254, "column": 31, "nodeType": "1524", "messageId": "1525", "endLine": 254, "endColumn": 34, "suggestions": "1656"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 255, "column": 29, "nodeType": "1524", "messageId": "1525", "endLine": 255, "endColumn": 32, "suggestions": "1657"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 256, "column": 29, "nodeType": "1524", "messageId": "1525", "endLine": 256, "endColumn": 32, "suggestions": "1658"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 257, "column": 29, "nodeType": "1524", "messageId": "1525", "endLine": 257, "endColumn": 32, "suggestions": "1659"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 258, "column": 31, "nodeType": "1524", "messageId": "1525", "endLine": 258, "endColumn": 34, "suggestions": "1660"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 259, "column": 29, "nodeType": "1524", "messageId": "1525", "endLine": 259, "endColumn": 32, "suggestions": "1661"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 260, "column": 31, "nodeType": "1524", "messageId": "1525", "endLine": 260, "endColumn": 34, "suggestions": "1662"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 261, "column": 33, "nodeType": "1524", "messageId": "1525", "endLine": 261, "endColumn": 36, "suggestions": "1663"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 262, "column": 37, "nodeType": "1524", "messageId": "1525", "endLine": 262, "endColumn": 40, "suggestions": "1664"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 263, "column": 34, "nodeType": "1524", "messageId": "1525", "endLine": 263, "endColumn": 37, "suggestions": "1665"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 273, "column": 37, "nodeType": "1524", "messageId": "1525", "endLine": 273, "endColumn": 40, "suggestions": "1666"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 425, "column": 11, "nodeType": "1524", "messageId": "1525", "endLine": 425, "endColumn": 14, "suggestions": "1667"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 38, "column": 44, "nodeType": "1524", "messageId": "1525", "endLine": 38, "endColumn": 47, "suggestions": "1668"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 46, "column": 12, "nodeType": "1524", "messageId": "1525", "endLine": 46, "endColumn": 15, "suggestions": "1669"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 61, "column": 55, "nodeType": "1524", "messageId": "1525", "endLine": 61, "endColumn": 58, "suggestions": "1670"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 62, "column": 56, "nodeType": "1524", "messageId": "1525", "endLine": 62, "endColumn": 59, "suggestions": "1671"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 73, "column": 44, "nodeType": "1524", "messageId": "1525", "endLine": 73, "endColumn": 47, "suggestions": "1672"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 74, "column": 46, "nodeType": "1524", "messageId": "1525", "endLine": 74, "endColumn": 49, "suggestions": "1673"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 76, "column": 37, "nodeType": "1524", "messageId": "1525", "endLine": 76, "endColumn": 40, "suggestions": "1674"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 112, "column": 44, "nodeType": "1524", "messageId": "1525", "endLine": 112, "endColumn": 47, "suggestions": "1675"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 135, "column": 12, "nodeType": "1524", "messageId": "1525", "endLine": 135, "endColumn": 15, "suggestions": "1676"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 177, "column": 22, "nodeType": "1524", "messageId": "1525", "endLine": 177, "endColumn": 25, "suggestions": "1677"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 206, "column": 19, "nodeType": "1524", "messageId": "1525", "endLine": 206, "endColumn": 22, "suggestions": "1678"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 228, "column": 44, "nodeType": "1524", "messageId": "1525", "endLine": 228, "endColumn": 47, "suggestions": "1679"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 235, "column": 39, "nodeType": "1524", "messageId": "1525", "endLine": 235, "endColumn": 42, "suggestions": "1680"}, {"ruleId": "1512", "severity": 2, "message": "1613", "line": 3, "column": 27, "nodeType": null, "messageId": "1514", "endLine": 3, "endColumn": 34}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 72, "column": 50, "nodeType": "1524", "messageId": "1525", "endLine": 72, "endColumn": 53, "suggestions": "1681"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 80, "column": 18, "nodeType": "1524", "messageId": "1525", "endLine": 80, "endColumn": 21, "suggestions": "1682"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 60, "column": 23, "nodeType": "1524", "messageId": "1525", "endLine": 60, "endColumn": 26, "suggestions": "1683"}, {"ruleId": "1512", "severity": 2, "message": "1684", "line": 3, "column": 27, "nodeType": null, "messageId": "1514", "endLine": 3, "endColumn": 34}, {"ruleId": "1512", "severity": 2, "message": "1685", "line": 3, "column": 36, "nodeType": null, "messageId": "1514", "endLine": 3, "endColumn": 44}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 41, "column": 38, "nodeType": "1524", "messageId": "1525", "endLine": 41, "endColumn": 41, "suggestions": "1686"}, {"ruleId": "1512", "severity": 2, "message": "1613", "line": 4, "column": 27, "nodeType": null, "messageId": "1514", "endLine": 4, "endColumn": 34}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 28, "column": 18, "nodeType": "1524", "messageId": "1525", "endLine": 28, "endColumn": 21, "suggestions": "1687"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 26, "column": 34, "nodeType": "1524", "messageId": "1525", "endLine": 26, "endColumn": 37, "suggestions": "1688"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 38, "column": 52, "nodeType": "1524", "messageId": "1525", "endLine": 38, "endColumn": 55, "suggestions": "1689"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 39, "column": 60, "nodeType": "1524", "messageId": "1525", "endLine": 39, "endColumn": 63, "suggestions": "1690"}, {"ruleId": "1512", "severity": 2, "message": "1691", "line": 65, "column": 11, "nodeType": null, "messageId": "1514", "endLine": 65, "endColumn": 25}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 87, "column": 38, "nodeType": "1524", "messageId": "1525", "endLine": 87, "endColumn": 41, "suggestions": "1692"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 105, "column": 18, "nodeType": "1524", "messageId": "1525", "endLine": 105, "endColumn": 21, "suggestions": "1693"}, {"ruleId": "1512", "severity": 2, "message": "1613", "line": 12, "column": 28, "nodeType": null, "messageId": "1514", "endLine": 12, "endColumn": 35}, {"ruleId": "1512", "severity": 2, "message": "1613", "line": 13, "column": 28, "nodeType": null, "messageId": "1514", "endLine": 13, "endColumn": 35}, {"ruleId": "1512", "severity": 2, "message": "1613", "line": 14, "column": 28, "nodeType": null, "messageId": "1514", "endLine": 14, "endColumn": 35}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 68, "column": 18, "nodeType": "1524", "messageId": "1525", "endLine": 68, "endColumn": 21, "suggestions": "1694"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 80, "column": 20, "nodeType": "1524", "messageId": "1525", "endLine": 80, "endColumn": 23, "suggestions": "1695"}, {"ruleId": "1512", "severity": 2, "message": "1613", "line": 137, "column": 27, "nodeType": null, "messageId": "1514", "endLine": 137, "endColumn": 34}, {"ruleId": "1512", "severity": 2, "message": "1613", "line": 12, "column": 27, "nodeType": null, "messageId": "1514", "endLine": 12, "endColumn": 34}, {"ruleId": "1512", "severity": 2, "message": "1585", "line": 6, "column": 17, "nodeType": null, "messageId": "1514", "endLine": 6, "endColumn": 21}, {"ruleId": "1512", "severity": 2, "message": "1628", "line": 8, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 8, "endColumn": 17}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 65, "column": 20, "nodeType": "1524", "messageId": "1525", "endLine": 65, "endColumn": 23, "suggestions": "1696"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 74, "column": 55, "nodeType": "1524", "messageId": "1525", "endLine": 74, "endColumn": 58, "suggestions": "1697"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 74, "column": 97, "nodeType": "1524", "messageId": "1525", "endLine": 74, "endColumn": 100, "suggestions": "1698"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 79, "column": 25, "nodeType": "1524", "messageId": "1525", "endLine": 79, "endColumn": 28, "suggestions": "1699"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 80, "column": 28, "nodeType": "1524", "messageId": "1525", "endLine": 80, "endColumn": 31, "suggestions": "1700"}, {"ruleId": "1512", "severity": 2, "message": "1628", "line": 8, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 8, "endColumn": 17}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 100, "column": 24, "nodeType": "1524", "messageId": "1525", "endLine": 100, "endColumn": 27, "suggestions": "1701"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 141, "column": 46, "nodeType": "1524", "messageId": "1525", "endLine": 141, "endColumn": 49, "suggestions": "1702"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 142, "column": 41, "nodeType": "1524", "messageId": "1525", "endLine": 142, "endColumn": 44, "suggestions": "1703"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 143, "column": 41, "nodeType": "1524", "messageId": "1525", "endLine": 143, "endColumn": 44, "suggestions": "1704"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 144, "column": 42, "nodeType": "1524", "messageId": "1525", "endLine": 144, "endColumn": 45, "suggestions": "1705"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 167, "column": 37, "nodeType": "1524", "messageId": "1525", "endLine": 167, "endColumn": 40, "suggestions": "1706"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 168, "column": 32, "nodeType": "1524", "messageId": "1525", "endLine": 168, "endColumn": 35, "suggestions": "1707"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 169, "column": 33, "nodeType": "1524", "messageId": "1525", "endLine": 169, "endColumn": 36, "suggestions": "1708"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 170, "column": 33, "nodeType": "1524", "messageId": "1525", "endLine": 170, "endColumn": 36, "suggestions": "1709"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 337, "column": 55, "nodeType": "1524", "messageId": "1525", "endLine": 337, "endColumn": 58, "suggestions": "1710"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 343, "column": 35, "nodeType": "1524", "messageId": "1525", "endLine": 343, "endColumn": 38, "suggestions": "1711"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 358, "column": 33, "nodeType": "1524", "messageId": "1525", "endLine": 358, "endColumn": 36, "suggestions": "1712"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 368, "column": 35, "nodeType": "1524", "messageId": "1525", "endLine": 368, "endColumn": 38, "suggestions": "1713"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 376, "column": 35, "nodeType": "1524", "messageId": "1525", "endLine": 376, "endColumn": 38, "suggestions": "1714"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 390, "column": 30, "nodeType": "1524", "messageId": "1525", "endLine": 390, "endColumn": 33, "suggestions": "1715"}, {"ruleId": "1512", "severity": 2, "message": "1628", "line": 8, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 8, "endColumn": 17}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 52, "column": 14, "nodeType": "1524", "messageId": "1525", "endLine": 52, "endColumn": 17, "suggestions": "1716"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 63, "column": 14, "nodeType": "1524", "messageId": "1525", "endLine": 63, "endColumn": 17, "suggestions": "1717"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 72, "column": 14, "nodeType": "1524", "messageId": "1525", "endLine": 72, "endColumn": 17, "suggestions": "1718"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 79, "column": 14, "nodeType": "1524", "messageId": "1525", "endLine": 79, "endColumn": 17, "suggestions": "1719"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 81, "column": 26, "nodeType": "1524", "messageId": "1525", "endLine": 81, "endColumn": 29, "suggestions": "1720"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 84, "column": 14, "nodeType": "1524", "messageId": "1525", "endLine": 84, "endColumn": 17, "suggestions": "1721"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 99, "column": 50, "nodeType": "1524", "messageId": "1525", "endLine": 99, "endColumn": 53, "suggestions": "1722"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 132, "column": 28, "nodeType": "1524", "messageId": "1525", "endLine": 132, "endColumn": 31, "suggestions": "1723"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 190, "column": 14, "nodeType": "1524", "messageId": "1525", "endLine": 190, "endColumn": 17, "suggestions": "1724"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 209, "column": 24, "nodeType": "1524", "messageId": "1525", "endLine": 209, "endColumn": 27, "suggestions": "1725"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 229, "column": 50, "nodeType": "1524", "messageId": "1525", "endLine": 229, "endColumn": 53, "suggestions": "1726"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 235, "column": 50, "nodeType": "1524", "messageId": "1525", "endLine": 235, "endColumn": 53, "suggestions": "1727"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 241, "column": 50, "nodeType": "1524", "messageId": "1525", "endLine": 241, "endColumn": 53, "suggestions": "1728"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 242, "column": 72, "nodeType": "1524", "messageId": "1525", "endLine": 242, "endColumn": 75, "suggestions": "1729"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 248, "column": 50, "nodeType": "1524", "messageId": "1525", "endLine": 248, "endColumn": 53, "suggestions": "1730"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 261, "column": 50, "nodeType": "1524", "messageId": "1525", "endLine": 261, "endColumn": 53, "suggestions": "1731"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 268, "column": 50, "nodeType": "1524", "messageId": "1525", "endLine": 268, "endColumn": 53, "suggestions": "1732"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 270, "column": 55, "nodeType": "1524", "messageId": "1525", "endLine": 270, "endColumn": 58, "suggestions": "1733"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 271, "column": 54, "nodeType": "1524", "messageId": "1525", "endLine": 271, "endColumn": 57, "suggestions": "1734"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 319, "column": 14, "nodeType": "1524", "messageId": "1525", "endLine": 319, "endColumn": 17, "suggestions": "1735"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 339, "column": 62, "nodeType": "1524", "messageId": "1525", "endLine": 339, "endColumn": 65, "suggestions": "1736"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 355, "column": 45, "nodeType": "1524", "messageId": "1525", "endLine": 355, "endColumn": 48, "suggestions": "1737"}, {"ruleId": "1512", "severity": 2, "message": "1613", "line": 5, "column": 40, "nodeType": null, "messageId": "1514", "endLine": 5, "endColumn": 47}, {"ruleId": "1512", "severity": 2, "message": "1628", "line": 12, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 12, "endColumn": 17}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 98, "column": 20, "nodeType": "1524", "messageId": "1525", "endLine": 98, "endColumn": 23, "suggestions": "1738"}, {"ruleId": "1512", "severity": 2, "message": "1739", "line": 6, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 6, "endColumn": 17}, {"ruleId": "1512", "severity": 2, "message": "1613", "line": 12, "column": 40, "nodeType": null, "messageId": "1514", "endLine": 12, "endColumn": 47}, {"ruleId": "1512", "severity": 2, "message": "1628", "line": 10, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 10, "endColumn": 17}, {"ruleId": "1512", "severity": 2, "message": "1740", "line": 13, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 13, "endColumn": 11}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 65, "column": 61, "nodeType": "1524", "messageId": "1525", "endLine": 65, "endColumn": 64, "suggestions": "1741"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 65, "column": 78, "nodeType": "1524", "messageId": "1525", "endLine": 65, "endColumn": 81, "suggestions": "1742"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 141, "column": 30, "nodeType": "1524", "messageId": "1525", "endLine": 141, "endColumn": 33, "suggestions": "1743"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 142, "column": 59, "nodeType": "1524", "messageId": "1525", "endLine": 142, "endColumn": 62, "suggestions": "1744"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 148, "column": 46, "nodeType": "1524", "messageId": "1525", "endLine": 148, "endColumn": 49, "suggestions": "1745"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 148, "column": 82, "nodeType": "1524", "messageId": "1525", "endLine": 148, "endColumn": 85, "suggestions": "1746"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 149, "column": 13, "nodeType": "1524", "messageId": "1525", "endLine": 149, "endColumn": 16, "suggestions": "1747"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 150, "column": 13, "nodeType": "1524", "messageId": "1525", "endLine": 150, "endColumn": 16, "suggestions": "1748"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 150, "column": 47, "nodeType": "1524", "messageId": "1525", "endLine": 150, "endColumn": 50, "suggestions": "1749"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 150, "column": 93, "nodeType": "1524", "messageId": "1525", "endLine": 150, "endColumn": 96, "suggestions": "1750"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 150, "column": 125, "nodeType": "1524", "messageId": "1525", "endLine": 150, "endColumn": 128, "suggestions": "1751"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 150, "column": 157, "nodeType": "1524", "messageId": "1525", "endLine": 150, "endColumn": 160, "suggestions": "1752"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 151, "column": 13, "nodeType": "1524", "messageId": "1525", "endLine": 151, "endColumn": 16, "suggestions": "1753"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 154, "column": 23, "nodeType": "1524", "messageId": "1525", "endLine": 154, "endColumn": 26, "suggestions": "1754"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 155, "column": 54, "nodeType": "1524", "messageId": "1525", "endLine": 155, "endColumn": 57, "suggestions": "1755"}, {"ruleId": "1512", "severity": 2, "message": "1756", "line": 6, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 6, "endColumn": 8}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 14, "column": 43, "nodeType": "1524", "messageId": "1525", "endLine": 14, "endColumn": 46, "suggestions": "1757"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 80, "column": 17, "nodeType": "1524", "messageId": "1525", "endLine": 80, "endColumn": 20, "suggestions": "1758"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 109, "column": 21, "nodeType": "1524", "messageId": "1525", "endLine": 109, "endColumn": 24, "suggestions": "1759"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 5, "column": 43, "nodeType": "1524", "messageId": "1525", "endLine": 5, "endColumn": 46, "suggestions": "1760"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 23, "column": 32, "nodeType": "1524", "messageId": "1525", "endLine": 23, "endColumn": 35, "suggestions": "1761"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 24, "column": 16, "nodeType": "1524", "messageId": "1525", "endLine": 24, "endColumn": 19, "suggestions": "1762"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 11, "column": 43, "nodeType": "1524", "messageId": "1525", "endLine": 11, "endColumn": 46, "suggestions": "1763"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 45, "column": 26, "nodeType": "1524", "messageId": "1525", "endLine": 45, "endColumn": 29, "suggestions": "1764"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 49, "column": 28, "nodeType": "1524", "messageId": "1525", "endLine": 49, "endColumn": 31, "suggestions": "1765"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 86, "column": 23, "nodeType": "1524", "messageId": "1525", "endLine": 86, "endColumn": 26, "suggestions": "1766"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 116, "column": 56, "nodeType": "1524", "messageId": "1525", "endLine": 116, "endColumn": 59, "suggestions": "1767"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 182, "column": 21, "nodeType": "1524", "messageId": "1525", "endLine": 182, "endColumn": 24, "suggestions": "1768"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 184, "column": 60, "nodeType": "1524", "messageId": "1525", "endLine": 184, "endColumn": 63, "suggestions": "1769"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 193, "column": 23, "nodeType": "1524", "messageId": "1525", "endLine": 193, "endColumn": 26, "suggestions": "1770"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 194, "column": 20, "nodeType": "1524", "messageId": "1525", "endLine": 194, "endColumn": 23, "suggestions": "1771"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 212, "column": 23, "nodeType": "1524", "messageId": "1525", "endLine": 212, "endColumn": 26, "suggestions": "1772"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 226, "column": 19, "nodeType": "1524", "messageId": "1525", "endLine": 226, "endColumn": 22, "suggestions": "1773"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 11, "column": 43, "nodeType": "1524", "messageId": "1525", "endLine": 11, "endColumn": 46, "suggestions": "1774"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 37, "column": 16, "nodeType": "1524", "messageId": "1525", "endLine": 37, "endColumn": 19, "suggestions": "1775"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 40, "column": 32, "nodeType": "1524", "messageId": "1525", "endLine": 40, "endColumn": 35, "suggestions": "1776"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 66, "column": 18, "nodeType": "1524", "messageId": "1525", "endLine": 66, "endColumn": 21, "suggestions": "1777"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 90, "column": 10, "nodeType": "1524", "messageId": "1525", "endLine": 90, "endColumn": 13, "suggestions": "1778"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 100, "column": 27, "nodeType": "1524", "messageId": "1525", "endLine": 100, "endColumn": 30, "suggestions": "1779"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 101, "column": 29, "nodeType": "1524", "messageId": "1525", "endLine": 101, "endColumn": 32, "suggestions": "1780"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 102, "column": 33, "nodeType": "1524", "messageId": "1525", "endLine": 102, "endColumn": 36, "suggestions": "1781"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 29, "column": 18, "nodeType": "1524", "messageId": "1525", "endLine": 29, "endColumn": 21, "suggestions": "1782"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 37, "column": 26, "nodeType": "1524", "messageId": "1525", "endLine": 37, "endColumn": 29, "suggestions": "1783"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 88, "column": 20, "nodeType": "1524", "messageId": "1525", "endLine": 88, "endColumn": 23, "suggestions": "1784"}, {"ruleId": "1785", "severity": 2, "message": "1786", "line": 119, "column": 5, "nodeType": "1787", "messageId": "1788", "endLine": 119, "endColumn": 10}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 147, "column": 41, "nodeType": "1524", "messageId": "1525", "endLine": 147, "endColumn": 44, "suggestions": "1789"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 148, "column": 49, "nodeType": "1524", "messageId": "1525", "endLine": 148, "endColumn": 52, "suggestions": "1790"}, {"ruleId": "1512", "severity": 2, "message": "1791", "line": 207, "column": 7, "nodeType": null, "messageId": "1514", "endLine": 207, "endColumn": 18}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 249, "column": 13, "nodeType": "1524", "messageId": "1525", "endLine": 249, "endColumn": 16, "suggestions": "1792"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 279, "column": 42, "nodeType": "1524", "messageId": "1525", "endLine": 279, "endColumn": 45, "suggestions": "1793"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 289, "column": 56, "nodeType": "1524", "messageId": "1525", "endLine": 289, "endColumn": 59, "suggestions": "1794"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 80, "column": 27, "nodeType": "1524", "messageId": "1525", "endLine": 80, "endColumn": 30, "suggestions": "1795"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 5, "column": 43, "nodeType": "1524", "messageId": "1525", "endLine": 5, "endColumn": 46, "suggestions": "1796"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 17, "column": 8, "nodeType": "1524", "messageId": "1525", "endLine": 17, "endColumn": 11, "suggestions": "1797"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 19, "column": 10, "nodeType": "1524", "messageId": "1525", "endLine": 19, "endColumn": 13, "suggestions": "1798"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 65, "column": 17, "nodeType": "1524", "messageId": "1525", "endLine": 65, "endColumn": 20, "suggestions": "1799"}, {"ruleId": "1800", "severity": 2, "message": "1801", "line": 113, "column": 3, "nodeType": "1802", "messageId": "1803", "endLine": 113, "endColumn": 86, "suggestions": "1804"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 114, "column": 23, "nodeType": "1524", "messageId": "1525", "endLine": 114, "endColumn": 26, "suggestions": "1805"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 28, "column": 22, "nodeType": "1524", "messageId": "1525", "endLine": 28, "endColumn": 25, "suggestions": "1806"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 35, "column": 12, "nodeType": "1524", "messageId": "1525", "endLine": 35, "endColumn": 15, "suggestions": "1807"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 40, "column": 12, "nodeType": "1524", "messageId": "1525", "endLine": 40, "endColumn": 15, "suggestions": "1808"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 41, "column": 15, "nodeType": "1524", "messageId": "1525", "endLine": 41, "endColumn": 18, "suggestions": "1809"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 42, "column": 18, "nodeType": "1524", "messageId": "1525", "endLine": 42, "endColumn": 21, "suggestions": "1810"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 120, "column": 27, "nodeType": "1524", "messageId": "1525", "endLine": 120, "endColumn": 30, "suggestions": "1811"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 139, "column": 12, "nodeType": "1524", "messageId": "1525", "endLine": 139, "endColumn": 15, "suggestions": "1812"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 194, "column": 29, "nodeType": "1524", "messageId": "1525", "endLine": 194, "endColumn": 32, "suggestions": "1813"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 82, "column": 71, "nodeType": "1524", "messageId": "1525", "endLine": 82, "endColumn": 74, "suggestions": "1814"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 149, "column": 23, "nodeType": "1524", "messageId": "1525", "endLine": 149, "endColumn": 26, "suggestions": "1815"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 182, "column": 25, "nodeType": "1524", "messageId": "1525", "endLine": 182, "endColumn": 28, "suggestions": "1816"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 187, "column": 44, "nodeType": "1524", "messageId": "1525", "endLine": 187, "endColumn": 47, "suggestions": "1817"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 202, "column": 25, "nodeType": "1524", "messageId": "1525", "endLine": 202, "endColumn": 28, "suggestions": "1818"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 207, "column": 58, "nodeType": "1524", "messageId": "1525", "endLine": 207, "endColumn": 61, "suggestions": "1819"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 220, "column": 34, "nodeType": "1524", "messageId": "1525", "endLine": 220, "endColumn": 37, "suggestions": "1820"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 222, "column": 12, "nodeType": "1524", "messageId": "1525", "endLine": 222, "endColumn": 15, "suggestions": "1821"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 232, "column": 23, "nodeType": "1524", "messageId": "1525", "endLine": 232, "endColumn": 26, "suggestions": "1822"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 235, "column": 19, "nodeType": "1524", "messageId": "1525", "endLine": 235, "endColumn": 22, "suggestions": "1823"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 235, "column": 67, "nodeType": "1524", "messageId": "1525", "endLine": 235, "endColumn": 70, "suggestions": "1824"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 235, "column": 89, "nodeType": "1524", "messageId": "1525", "endLine": 235, "endColumn": 92, "suggestions": "1825"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 242, "column": 24, "nodeType": "1524", "messageId": "1525", "endLine": 242, "endColumn": 27, "suggestions": "1826"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 131, "column": 32, "nodeType": "1524", "messageId": "1525", "endLine": 131, "endColumn": 35, "suggestions": "1827"}, {"ruleId": "1512", "severity": 2, "message": "1628", "line": 6, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 6, "endColumn": 17}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 25, "column": 27, "nodeType": "1524", "messageId": "1525", "endLine": 25, "endColumn": 30, "suggestions": "1828"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 83, "column": 27, "nodeType": "1524", "messageId": "1525", "endLine": 83, "endColumn": 30, "suggestions": "1829"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 18, "column": 18, "nodeType": "1524", "messageId": "1525", "endLine": 18, "endColumn": 21, "suggestions": "1830"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 40, "column": 35, "nodeType": "1524", "messageId": "1525", "endLine": 40, "endColumn": 38, "suggestions": "1831"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 21, "column": 38, "nodeType": "1524", "messageId": "1525", "endLine": 21, "endColumn": 41, "suggestions": "1832"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 35, "column": 32, "nodeType": "1524", "messageId": "1525", "endLine": 35, "endColumn": 35, "suggestions": "1833"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 87, "column": 38, "nodeType": "1524", "messageId": "1525", "endLine": 87, "endColumn": 41, "suggestions": "1834"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 117, "column": 22, "nodeType": "1524", "messageId": "1525", "endLine": 117, "endColumn": 25, "suggestions": "1835"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 160, "column": 36, "nodeType": "1524", "messageId": "1525", "endLine": 160, "endColumn": 39, "suggestions": "1836"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 167, "column": 76, "nodeType": "1524", "messageId": "1525", "endLine": 167, "endColumn": 79, "suggestions": "1837"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 24, "column": 38, "nodeType": "1524", "messageId": "1525", "endLine": 24, "endColumn": 41, "suggestions": "1838"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 38, "column": 12, "nodeType": "1524", "messageId": "1525", "endLine": 38, "endColumn": 15, "suggestions": "1839"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 46, "column": 12, "nodeType": "1524", "messageId": "1525", "endLine": 46, "endColumn": 15, "suggestions": "1840"}, {"ruleId": "1512", "severity": 2, "message": "1613", "line": 10, "column": 27, "nodeType": null, "messageId": "1514", "endLine": 10, "endColumn": 34}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 30, "column": 63, "nodeType": "1524", "messageId": "1525", "endLine": 30, "endColumn": 66, "suggestions": "1841"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 192, "column": 58, "nodeType": "1524", "messageId": "1525", "endLine": 192, "endColumn": 61, "suggestions": "1842"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 196, "column": 28, "nodeType": "1524", "messageId": "1525", "endLine": 196, "endColumn": 31, "suggestions": "1843"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 199, "column": 30, "nodeType": "1524", "messageId": "1525", "endLine": 199, "endColumn": 33, "suggestions": "1844"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 200, "column": 30, "nodeType": "1524", "messageId": "1525", "endLine": 200, "endColumn": 33, "suggestions": "1845"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 203, "column": 41, "nodeType": "1524", "messageId": "1525", "endLine": 203, "endColumn": 44, "suggestions": "1846"}, {"ruleId": "1512", "severity": 2, "message": "1570", "line": 7, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 7, "endColumn": 9}, {"ruleId": "1528", "severity": 2, "message": "1557", "line": 124, "column": 59, "nodeType": "1530", "messageId": "1531", "suggestions": "1847"}, {"ruleId": "1528", "severity": 2, "message": "1557", "line": 124, "column": 72, "nodeType": "1530", "messageId": "1531", "suggestions": "1848"}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 211, "column": 25, "nodeType": "1562", "endLine": 215, "endColumn": 27}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 256, "column": 25, "nodeType": "1562", "endLine": 260, "endColumn": 27}, {"ruleId": "1528", "severity": 2, "message": "1529", "line": 305, "column": 22, "nodeType": "1530", "messageId": "1531", "suggestions": "1849"}, {"ruleId": "1528", "severity": 2, "message": "1529", "line": 305, "column": 38, "nodeType": "1530", "messageId": "1531", "suggestions": "1850"}, {"ruleId": "1518", "severity": 1, "message": "1851", "line": 43, "column": 6, "nodeType": "1520", "endLine": 43, "endColumn": 23, "suggestions": "1852"}, {"ruleId": "1512", "severity": 2, "message": "1853", "line": 26, "column": 10, "nodeType": null, "messageId": "1514", "endLine": 26, "endColumn": 22}, {"ruleId": "1512", "severity": 2, "message": "1854", "line": 26, "column": 24, "nodeType": null, "messageId": "1514", "endLine": 26, "endColumn": 39}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 140, "column": 17, "nodeType": "1562", "endLine": 144, "endColumn": 19}, {"ruleId": "1512", "severity": 2, "message": "1855", "line": 4, "column": 33, "nodeType": null, "messageId": "1514", "endLine": 4, "endColumn": 39}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 54, "column": 13, "nodeType": "1562", "endLine": 58, "endColumn": 15}, {"ruleId": "1512", "severity": 2, "message": "1855", "line": 7, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 7, "endColumn": 9}, {"ruleId": "1512", "severity": 2, "message": "1856", "line": 45, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 45, "endColumn": 19}, {"ruleId": "1518", "severity": 1, "message": "1857", "line": 61, "column": 6, "nodeType": "1520", "endLine": 61, "endColumn": 14, "suggestions": "1858"}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 302, "column": 27, "nodeType": "1562", "endLine": 306, "endColumn": 29}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 349, "column": 25, "nodeType": "1562", "endLine": 353, "endColumn": 27}, {"ruleId": "1512", "severity": 2, "message": "1855", "line": 13, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 13, "endColumn": 9}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 267, "column": 25, "nodeType": "1562", "endLine": 271, "endColumn": 27}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 303, "column": 23, "nodeType": "1562", "endLine": 307, "endColumn": 25}, {"ruleId": "1859", "severity": 2, "message": "1860", "line": 215, "column": 3, "nodeType": "1787", "endLine": 215, "endColumn": 12}, {"ruleId": "1518", "severity": 1, "message": "1587", "line": 217, "column": 6, "nodeType": "1520", "endLine": 217, "endColumn": 15, "suggestions": "1861"}, {"ruleId": "1518", "severity": 1, "message": "1862", "line": 15, "column": 6, "nodeType": "1520", "endLine": 15, "endColumn": 14, "suggestions": "1863"}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 98, "column": 19, "nodeType": "1562", "endLine": 102, "endColumn": 21}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 176, "column": 23, "nodeType": "1562", "endLine": 180, "endColumn": 25}, {"ruleId": "1528", "severity": 2, "message": "1557", "line": 200, "column": 44, "nodeType": "1530", "messageId": "1531", "suggestions": "1864"}, {"ruleId": "1528", "severity": 2, "message": "1557", "line": 200, "column": 57, "nodeType": "1530", "messageId": "1531", "suggestions": "1865"}, {"ruleId": "1518", "severity": 1, "message": "1866", "line": 54, "column": 6, "nodeType": "1520", "endLine": 54, "endColumn": 14, "suggestions": "1867"}, {"ruleId": "1518", "severity": 1, "message": "1868", "line": 47, "column": 6, "nodeType": "1520", "endLine": 47, "endColumn": 23, "suggestions": "1869"}, {"ruleId": "1518", "severity": 1, "message": "1870", "line": 67, "column": 6, "nodeType": "1520", "endLine": 67, "endColumn": 42, "suggestions": "1871"}, {"ruleId": "1512", "severity": 2, "message": "1633", "line": 194, "column": 14, "nodeType": null, "messageId": "1514", "endLine": 194, "endColumn": 19}, {"ruleId": "1528", "severity": 2, "message": "1529", "line": 77, "column": 20, "nodeType": "1530", "messageId": "1531", "suggestions": "1872"}, {"ruleId": "1512", "severity": 2, "message": "1633", "line": 65, "column": 14, "nodeType": null, "messageId": "1514", "endLine": 65, "endColumn": 19}, {"ruleId": "1512", "severity": 2, "message": "1873", "line": 3, "column": 17, "nodeType": null, "messageId": "1514", "endLine": 3, "endColumn": 25}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 114, "column": 56, "nodeType": "1524", "messageId": "1525", "endLine": 114, "endColumn": 59, "suggestions": "1874"}, {"ruleId": "1528", "severity": 2, "message": "1529", "line": 180, "column": 17, "nodeType": "1530", "messageId": "1531", "suggestions": "1875"}, {"ruleId": "1512", "severity": 2, "message": "1551", "line": 4, "column": 38, "nodeType": null, "messageId": "1514", "endLine": 4, "endColumn": 43}, {"ruleId": "1528", "severity": 2, "message": "1529", "line": 151, "column": 17, "nodeType": "1530", "messageId": "1531", "suggestions": "1876"}, {"ruleId": "1528", "severity": 2, "message": "1529", "line": 338, "column": 19, "nodeType": "1530", "messageId": "1531", "suggestions": "1877"}, {"ruleId": "1512", "severity": 2, "message": "1878", "line": 18, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 18, "endColumn": 6}, {"ruleId": "1512", "severity": 2, "message": "1633", "line": 192, "column": 14, "nodeType": null, "messageId": "1514", "endLine": 192, "endColumn": 19}, {"ruleId": "1512", "severity": 2, "message": "1879", "line": 297, "column": 9, "nodeType": null, "messageId": "1514", "endLine": 297, "endColumn": 17}, {"ruleId": "1512", "severity": 2, "message": "1880", "line": 298, "column": 9, "nodeType": null, "messageId": "1514", "endLine": 298, "endColumn": 17}, {"ruleId": "1528", "severity": 2, "message": "1529", "line": 72, "column": 50, "nodeType": "1530", "messageId": "1531", "suggestions": "1881"}, {"ruleId": "1528", "severity": 2, "message": "1529", "line": 252, "column": 53, "nodeType": "1530", "messageId": "1531", "suggestions": "1882"}, {"ruleId": "1512", "severity": 2, "message": "1883", "line": 7, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 7, "endColumn": 8}, {"ruleId": "1512", "severity": 2, "message": "1884", "line": 8, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 8, "endColumn": 9}, {"ruleId": "1512", "severity": 2, "message": "1885", "line": 12, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 12, "endColumn": 7}, {"ruleId": "1512", "severity": 2, "message": "1611", "line": 13, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 13, "endColumn": 8}, {"ruleId": "1512", "severity": 2, "message": "1886", "line": 14, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 14, "endColumn": 12}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 22, "column": 44, "nodeType": "1524", "messageId": "1525", "endLine": 22, "endColumn": 47, "suggestions": "1887"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 23, "column": 48, "nodeType": "1524", "messageId": "1525", "endLine": 23, "endColumn": 51, "suggestions": "1888"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 24, "column": 44, "nodeType": "1524", "messageId": "1525", "endLine": 24, "endColumn": 47, "suggestions": "1889"}, {"ruleId": "1518", "severity": 1, "message": "1890", "line": 31, "column": 6, "nodeType": "1520", "endLine": 31, "endColumn": 8, "suggestions": "1891"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 71, "column": 15, "nodeType": "1524", "messageId": "1525", "endLine": 71, "endColumn": 18, "suggestions": "1892"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 73, "column": 6, "nodeType": "1524", "messageId": "1525", "endLine": 73, "endColumn": 9, "suggestions": "1893"}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 330, "column": 19, "nodeType": "1562", "endLine": 334, "endColumn": 21}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 408, "column": 40, "nodeType": "1524", "messageId": "1525", "endLine": 408, "endColumn": 43, "suggestions": "1894"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 444, "column": 40, "nodeType": "1524", "messageId": "1525", "endLine": 444, "endColumn": 43, "suggestions": "1895"}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 452, "column": 21, "nodeType": "1562", "endLine": 456, "endColumn": 23}, {"ruleId": "1528", "severity": 2, "message": "1529", "line": 246, "column": 18, "nodeType": "1530", "messageId": "1531", "suggestions": "1896"}, {"ruleId": "1518", "severity": 1, "message": "1587", "line": 120, "column": 6, "nodeType": "1520", "endLine": 120, "endColumn": 37, "suggestions": "1897"}, {"ruleId": "1512", "severity": 2, "message": "1898", "line": 6, "column": 8, "nodeType": null, "messageId": "1514", "endLine": 6, "endColumn": 13}, {"ruleId": "1512", "severity": 2, "message": "1899", "line": 14, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 14, "endColumn": 10}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 114, "column": 38, "nodeType": "1524", "messageId": "1525", "endLine": 114, "endColumn": 41, "suggestions": "1900"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 178, "column": 62, "nodeType": "1524", "messageId": "1525", "endLine": 178, "endColumn": 65, "suggestions": "1901"}, {"ruleId": "1512", "severity": 2, "message": "1902", "line": 186, "column": 10, "nodeType": null, "messageId": "1514", "endLine": 186, "endColumn": 21}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 236, "column": 20, "nodeType": "1524", "messageId": "1525", "endLine": 236, "endColumn": 23, "suggestions": "1903"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 248, "column": 45, "nodeType": "1524", "messageId": "1525", "endLine": 248, "endColumn": 48, "suggestions": "1904"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 533, "column": 57, "nodeType": "1524", "messageId": "1525", "endLine": 533, "endColumn": 60, "suggestions": "1905"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 825, "column": 61, "nodeType": "1524", "messageId": "1525", "endLine": 825, "endColumn": 64, "suggestions": "1906"}, {"ruleId": "1512", "severity": 2, "message": "1582", "line": 11, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 11, "endColumn": 11}, {"ruleId": "1512", "severity": 2, "message": "1586", "line": 12, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 12, "endColumn": 13}, {"ruleId": "1512", "severity": 2, "message": "1538", "line": 13, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 13, "endColumn": 9}, {"ruleId": "1512", "severity": 2, "message": "1580", "line": 14, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 14, "endColumn": 7}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 30, "column": 13, "nodeType": "1524", "messageId": "1525", "endLine": 30, "endColumn": 16, "suggestions": "1907"}, {"ruleId": "1512", "severity": 2, "message": "1908", "line": 44, "column": 10, "nodeType": null, "messageId": "1514", "endLine": 44, "endColumn": 17}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 84, "column": 32, "nodeType": "1524", "messageId": "1525", "endLine": 84, "endColumn": 35, "suggestions": "1909"}, {"ruleId": "1512", "severity": 2, "message": "1910", "line": 15, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 15, "endColumn": 13}, {"ruleId": "1512", "severity": 2, "message": "1908", "line": 42, "column": 10, "nodeType": null, "messageId": "1514", "endLine": 42, "endColumn": 17}, {"ruleId": "1512", "severity": 2, "message": "1911", "line": 43, "column": 10, "nodeType": null, "messageId": "1514", "endLine": 43, "endColumn": 16}, {"ruleId": "1512", "severity": 2, "message": "1912", "line": 128, "column": 9, "nodeType": null, "messageId": "1514", "endLine": 128, "endColumn": 29}, {"ruleId": "1512", "severity": 2, "message": "1570", "line": 6, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 6, "endColumn": 9}, {"ruleId": "1512", "severity": 2, "message": "1899", "line": 7, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 7, "endColumn": 10}, {"ruleId": "1512", "severity": 2, "message": "1913", "line": 17, "column": 10, "nodeType": null, "messageId": "1514", "endLine": 17, "endColumn": 34}, {"ruleId": "1512", "severity": 2, "message": "1914", "line": 106, "column": 7, "nodeType": null, "messageId": "1514", "endLine": 106, "endColumn": 19}, {"ruleId": "1512", "severity": 2, "message": "1915", "line": 141, "column": 5, "nodeType": null, "messageId": "1514", "endLine": 141, "endColumn": 23}, {"ruleId": "1785", "severity": 2, "message": "1916", "line": 141, "column": 5, "nodeType": "1787", "messageId": "1788", "endLine": 141, "endColumn": 36, "fix": "1917"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 219, "column": 48, "nodeType": "1524", "messageId": "1525", "endLine": 219, "endColumn": 51, "suggestions": "1918"}, {"ruleId": "1518", "severity": 1, "message": "1919", "line": 238, "column": 6, "nodeType": "1520", "endLine": 238, "endColumn": 14, "suggestions": "1920"}, {"ruleId": "1512", "severity": 2, "message": "1633", "line": 98, "column": 14, "nodeType": null, "messageId": "1514", "endLine": 98, "endColumn": 19}, {"ruleId": "1512", "severity": 2, "message": "1921", "line": 8, "column": 10, "nodeType": null, "messageId": "1514", "endLine": 8, "endColumn": 19}, {"ruleId": "1518", "severity": 1, "message": "1922", "line": 44, "column": 6, "nodeType": "1520", "endLine": 44, "endColumn": 22, "suggestions": "1923"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 82, "column": 49, "nodeType": "1524", "messageId": "1525", "endLine": 82, "endColumn": 52, "suggestions": "1924"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 118, "column": 19, "nodeType": "1524", "messageId": "1525", "endLine": 118, "endColumn": 22, "suggestions": "1925"}, {"ruleId": "1512", "severity": 2, "message": "1926", "line": 125, "column": 13, "nodeType": null, "messageId": "1514", "endLine": 125, "endColumn": 27}, {"ruleId": "1512", "severity": 2, "message": "1926", "line": 132, "column": 15, "nodeType": null, "messageId": "1514", "endLine": 132, "endColumn": 29}, {"ruleId": "1528", "severity": 2, "message": "1529", "line": 298, "column": 35, "nodeType": "1530", "messageId": "1531", "suggestions": "1927"}, {"ruleId": "1528", "severity": 2, "message": "1529", "line": 298, "column": 61, "nodeType": "1530", "messageId": "1531", "suggestions": "1928"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 46, "column": 15, "nodeType": "1524", "messageId": "1525", "endLine": 46, "endColumn": 18, "suggestions": "1929"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 128, "column": 35, "nodeType": "1524", "messageId": "1525", "endLine": 128, "endColumn": 38, "suggestions": "1930"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 19, "column": 31, "nodeType": "1524", "messageId": "1525", "endLine": 19, "endColumn": 34, "suggestions": "1931"}, {"ruleId": "1512", "severity": 2, "message": "1932", "line": 36, "column": 10, "nodeType": null, "messageId": "1514", "endLine": 36, "endColumn": 25}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 48, "column": 20, "nodeType": "1524", "messageId": "1525", "endLine": 48, "endColumn": 23, "suggestions": "1933"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 66, "column": 34, "nodeType": "1524", "messageId": "1525", "endLine": 66, "endColumn": 37, "suggestions": "1934"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 69, "column": 18, "nodeType": "1524", "messageId": "1525", "endLine": 69, "endColumn": 21, "suggestions": "1935"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 139, "column": 32, "nodeType": "1524", "messageId": "1525", "endLine": 139, "endColumn": 35, "suggestions": "1936"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 148, "column": 27, "nodeType": "1524", "messageId": "1525", "endLine": 148, "endColumn": 30, "suggestions": "1937"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 26, "column": 12, "nodeType": "1524", "messageId": "1525", "endLine": 26, "endColumn": 15, "suggestions": "1938"}, {"ruleId": "1518", "severity": 1, "message": "1851", "line": 25, "column": 6, "nodeType": "1520", "endLine": 25, "endColumn": 17, "suggestions": "1939"}, {"ruleId": "1528", "severity": 2, "message": "1529", "line": 113, "column": 39, "nodeType": "1530", "messageId": "1531", "suggestions": "1940"}, {"ruleId": "1518", "severity": 1, "message": "1941", "line": 97, "column": 6, "nodeType": "1520", "endLine": 97, "endColumn": 20, "suggestions": "1942"}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 128, "column": 11, "nodeType": "1562", "endLine": 138, "endColumn": 13}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 191, "column": 17, "nodeType": "1562", "endLine": 195, "endColumn": 19}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 212, "column": 17, "nodeType": "1562", "endLine": 216, "endColumn": 19}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 270, "column": 13, "nodeType": "1562", "endLine": 277, "endColumn": 15}, {"ruleId": "1512", "severity": 2, "message": "1539", "line": 4, "column": 22, "nodeType": null, "messageId": "1514", "endLine": 4, "endColumn": 30}, {"ruleId": "1512", "severity": 2, "message": "1943", "line": 17, "column": 10, "nodeType": null, "messageId": "1514", "endLine": 17, "endColumn": 15}, {"ruleId": "1518", "severity": 1, "message": "1862", "line": 23, "column": 6, "nodeType": "1520", "endLine": 23, "endColumn": 17, "suggestions": "1944"}, {"ruleId": "1512", "severity": 2, "message": "1633", "line": 36, "column": 14, "nodeType": null, "messageId": "1514", "endLine": 36, "endColumn": 19}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 162, "column": 21, "nodeType": "1562", "endLine": 166, "endColumn": 23}, {"ruleId": "1560", "severity": 1, "message": "1561", "line": 239, "column": 23, "nodeType": "1562", "endLine": 243, "endColumn": 25}, {"ruleId": "1512", "severity": 2, "message": "1945", "line": 4, "column": 10, "nodeType": null, "messageId": "1514", "endLine": 4, "endColumn": 21}, {"ruleId": "1512", "severity": 2, "message": "1946", "line": 25, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 25, "endColumn": 12}, {"ruleId": "1518", "severity": 1, "message": "1868", "line": 36, "column": 6, "nodeType": "1520", "endLine": 36, "endColumn": 17, "suggestions": "1947"}, {"ruleId": "1518", "severity": 1, "message": "1948", "line": 74, "column": 6, "nodeType": "1520", "endLine": 74, "endColumn": 18, "suggestions": "1949"}, {"ruleId": "1512", "severity": 2, "message": "1950", "line": 165, "column": 33, "nodeType": null, "messageId": "1514", "endLine": 165, "endColumn": 42}, {"ruleId": "1512", "severity": 2, "message": "1951", "line": 200, "column": 9, "nodeType": null, "messageId": "1514", "endLine": 200, "endColumn": 21}, {"ruleId": "1512", "severity": 2, "message": "1633", "line": 64, "column": 14, "nodeType": null, "messageId": "1514", "endLine": 64, "endColumn": 19}, {"ruleId": "1528", "severity": 2, "message": "1557", "line": 149, "column": 25, "nodeType": "1530", "messageId": "1531", "suggestions": "1952"}, {"ruleId": "1528", "severity": 2, "message": "1557", "line": 149, "column": 47, "nodeType": "1530", "messageId": "1531", "suggestions": "1953"}, {"ruleId": "1512", "severity": 2, "message": "1873", "line": 3, "column": 17, "nodeType": null, "messageId": "1514", "endLine": 3, "endColumn": 25}, {"ruleId": "1512", "severity": 2, "message": "1954", "line": 5, "column": 10, "nodeType": null, "messageId": "1514", "endLine": 5, "endColumn": 18}, {"ruleId": "1512", "severity": 2, "message": "1955", "line": 54, "column": 13, "nodeType": null, "messageId": "1514", "endLine": 54, "endColumn": 23}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 102, "column": 34, "nodeType": "1524", "messageId": "1525", "endLine": 102, "endColumn": 37, "suggestions": "1956"}, {"ruleId": "1518", "severity": 1, "message": "1957", "line": 480, "column": 6, "nodeType": "1520", "endLine": 480, "endColumn": 24, "suggestions": "1958"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 18, "column": 9, "nodeType": "1524", "messageId": "1525", "endLine": 18, "endColumn": 12, "suggestions": "1959"}, {"ruleId": "1518", "severity": 1, "message": "1960", "line": 122, "column": 6, "nodeType": "1520", "endLine": 122, "endColumn": 25, "suggestions": "1961"}, {"ruleId": "1512", "severity": 2, "message": "1633", "line": 146, "column": 14, "nodeType": null, "messageId": "1514", "endLine": 146, "endColumn": 19}, {"ruleId": "1512", "severity": 2, "message": "1962", "line": 6, "column": 21, "nodeType": null, "messageId": "1514", "endLine": 6, "endColumn": 26}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 86, "column": 53, "nodeType": "1524", "messageId": "1525", "endLine": 86, "endColumn": 56, "suggestions": "1963"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 100, "column": 44, "nodeType": "1524", "messageId": "1525", "endLine": 100, "endColumn": 47, "suggestions": "1964"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 155, "column": 14, "nodeType": "1524", "messageId": "1525", "endLine": 155, "endColumn": 17, "suggestions": "1965"}, {"ruleId": "1512", "severity": 2, "message": "1966", "line": 178, "column": 35, "nodeType": null, "messageId": "1514", "endLine": 178, "endColumn": 42}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 44, "column": 38, "nodeType": "1524", "messageId": "1525", "endLine": 44, "endColumn": 41, "suggestions": "1967"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 66, "column": 10, "nodeType": "1524", "messageId": "1525", "endLine": 66, "endColumn": 13, "suggestions": "1968"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 76, "column": 23, "nodeType": "1524", "messageId": "1525", "endLine": 76, "endColumn": 26, "suggestions": "1969"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 131, "column": 18, "nodeType": "1524", "messageId": "1525", "endLine": 131, "endColumn": 21, "suggestions": "1970"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 132, "column": 18, "nodeType": "1524", "messageId": "1525", "endLine": 132, "endColumn": 21, "suggestions": "1971"}, {"ruleId": "1972", "severity": 2, "message": "1973", "line": 197, "column": 34, "nodeType": "1787", "messageId": "1974", "endLine": 197, "endColumn": 42}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 198, "column": 40, "nodeType": "1524", "messageId": "1525", "endLine": 198, "endColumn": 43, "suggestions": "1975"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 8, "column": 13, "nodeType": "1524", "messageId": "1525", "endLine": 8, "endColumn": 16, "suggestions": "1976"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 14, "column": 29, "nodeType": "1524", "messageId": "1525", "endLine": 14, "endColumn": 32, "suggestions": "1977"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 20, "column": 15, "nodeType": "1524", "messageId": "1525", "endLine": 20, "endColumn": 18, "suggestions": "1978"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 36, "column": 42, "nodeType": "1524", "messageId": "1525", "endLine": 36, "endColumn": 45, "suggestions": "1979"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 78, "column": 42, "nodeType": "1524", "messageId": "1525", "endLine": 78, "endColumn": 45, "suggestions": "1980"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 85, "column": 59, "nodeType": "1524", "messageId": "1525", "endLine": 85, "endColumn": 62, "suggestions": "1981"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 107, "column": 42, "nodeType": "1524", "messageId": "1525", "endLine": 107, "endColumn": 45, "suggestions": "1982"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 154, "column": 41, "nodeType": "1524", "messageId": "1525", "endLine": 154, "endColumn": 44, "suggestions": "1983"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 175, "column": 23, "nodeType": "1524", "messageId": "1525", "endLine": 175, "endColumn": 26, "suggestions": "1984"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 302, "column": 43, "nodeType": "1524", "messageId": "1525", "endLine": 302, "endColumn": 46, "suggestions": "1985"}, {"ruleId": "1972", "severity": 2, "message": "1973", "line": 350, "column": 34, "nodeType": "1787", "messageId": "1974", "endLine": 350, "endColumn": 42}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 351, "column": 26, "nodeType": "1524", "messageId": "1525", "endLine": 351, "endColumn": 29, "suggestions": "1986"}, {"ruleId": "1512", "severity": 2, "message": "1987", "line": 104, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 104, "endColumn": 10}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 12, "column": 28, "nodeType": "1524", "messageId": "1525", "endLine": 12, "endColumn": 31, "suggestions": "1988"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 64, "column": 30, "nodeType": "1524", "messageId": "1525", "endLine": 64, "endColumn": 33, "suggestions": "1989"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 101, "column": 66, "nodeType": "1524", "messageId": "1525", "endLine": 101, "endColumn": 69, "suggestions": "1990"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 105, "column": 50, "nodeType": "1524", "messageId": "1525", "endLine": 105, "endColumn": 53, "suggestions": "1991"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 109, "column": 50, "nodeType": "1524", "messageId": "1525", "endLine": 109, "endColumn": 53, "suggestions": "1992"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 113, "column": 51, "nodeType": "1524", "messageId": "1525", "endLine": 113, "endColumn": 54, "suggestions": "1993"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 122, "column": 30, "nodeType": "1524", "messageId": "1525", "endLine": 122, "endColumn": 33, "suggestions": "1994"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 136, "column": 30, "nodeType": "1524", "messageId": "1525", "endLine": 136, "endColumn": 33, "suggestions": "1995"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 151, "column": 30, "nodeType": "1524", "messageId": "1525", "endLine": 151, "endColumn": 33, "suggestions": "1996"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 164, "column": 30, "nodeType": "1524", "messageId": "1525", "endLine": 164, "endColumn": 33, "suggestions": "1997"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 178, "column": 30, "nodeType": "1524", "messageId": "1525", "endLine": 178, "endColumn": 33, "suggestions": "1998"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 194, "column": 30, "nodeType": "1524", "messageId": "1525", "endLine": 194, "endColumn": 33, "suggestions": "1999"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 209, "column": 30, "nodeType": "1524", "messageId": "1525", "endLine": 209, "endColumn": 33, "suggestions": "2000"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 223, "column": 30, "nodeType": "1524", "messageId": "1525", "endLine": 223, "endColumn": 33, "suggestions": "2001"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 243, "column": 30, "nodeType": "1524", "messageId": "1525", "endLine": 243, "endColumn": 33, "suggestions": "2002"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 259, "column": 30, "nodeType": "1524", "messageId": "1525", "endLine": 259, "endColumn": 33, "suggestions": "2003"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 274, "column": 30, "nodeType": "1524", "messageId": "1525", "endLine": 274, "endColumn": 33, "suggestions": "2004"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 288, "column": 30, "nodeType": "1524", "messageId": "1525", "endLine": 288, "endColumn": 33, "suggestions": "2005"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 310, "column": 53, "nodeType": "1524", "messageId": "1525", "endLine": 310, "endColumn": 56, "suggestions": "2006"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 313, "column": 70, "nodeType": "1524", "messageId": "1525", "endLine": 313, "endColumn": 73, "suggestions": "2007"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 331, "column": 34, "nodeType": "1524", "messageId": "1525", "endLine": 331, "endColumn": 37, "suggestions": "2008"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 336, "column": 36, "nodeType": "1524", "messageId": "1525", "endLine": 336, "endColumn": 39, "suggestions": "2009"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 341, "column": 34, "nodeType": "1524", "messageId": "1525", "endLine": 341, "endColumn": 37, "suggestions": "2010"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 346, "column": 37, "nodeType": "1524", "messageId": "1525", "endLine": 346, "endColumn": 40, "suggestions": "2011"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 371, "column": 10, "nodeType": "1524", "messageId": "1525", "endLine": 371, "endColumn": 13, "suggestions": "2012"}, {"ruleId": "1785", "severity": 2, "message": "2013", "line": 30, "column": 9, "nodeType": "1787", "messageId": "1788", "endLine": 30, "endColumn": 16, "fix": "2014"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 37, "column": 21, "nodeType": "1524", "messageId": "1525", "endLine": 37, "endColumn": 24, "suggestions": "2015"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 38, "column": 43, "nodeType": "1524", "messageId": "1525", "endLine": 38, "endColumn": 46, "suggestions": "2016"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 42, "column": 59, "nodeType": "1524", "messageId": "1525", "endLine": 42, "endColumn": 62, "suggestions": "2017"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 47, "column": 61, "nodeType": "1524", "messageId": "1525", "endLine": 47, "endColumn": 64, "suggestions": "2018"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 50, "column": 53, "nodeType": "1524", "messageId": "1525", "endLine": 50, "endColumn": 56, "suggestions": "2019"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 67, "column": 62, "nodeType": "1524", "messageId": "1525", "endLine": 67, "endColumn": 65, "suggestions": "2020"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 132, "column": 18, "nodeType": "1524", "messageId": "1525", "endLine": 132, "endColumn": 21, "suggestions": "2021"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 147, "column": 50, "nodeType": "1524", "messageId": "1525", "endLine": 147, "endColumn": 53, "suggestions": "2022"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 174, "column": 17, "nodeType": "1524", "messageId": "1525", "endLine": 174, "endColumn": 20, "suggestions": "2023"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 174, "column": 61, "nodeType": "1524", "messageId": "1525", "endLine": 174, "endColumn": 64, "suggestions": "2024"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 177, "column": 28, "nodeType": "1524", "messageId": "1525", "endLine": 177, "endColumn": 31, "suggestions": "2025"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 178, "column": 35, "nodeType": "1524", "messageId": "1525", "endLine": 178, "endColumn": 38, "suggestions": "2026"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 249, "column": 12, "nodeType": "1524", "messageId": "1525", "endLine": 249, "endColumn": 15, "suggestions": "2027"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 251, "column": 18, "nodeType": "1524", "messageId": "1525", "endLine": 251, "endColumn": 21, "suggestions": "2028"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 261, "column": 31, "nodeType": "1524", "messageId": "1525", "endLine": 261, "endColumn": 34, "suggestions": "2029"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 276, "column": 18, "nodeType": "1524", "messageId": "1525", "endLine": 276, "endColumn": 21, "suggestions": "2030"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 277, "column": 16, "nodeType": "1524", "messageId": "1525", "endLine": 277, "endColumn": 19, "suggestions": "2031"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 278, "column": 18, "nodeType": "1524", "messageId": "1525", "endLine": 278, "endColumn": 21, "suggestions": "2032"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 294, "column": 47, "nodeType": "1524", "messageId": "1525", "endLine": 294, "endColumn": 50, "suggestions": "2033"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 303, "column": 28, "nodeType": "1524", "messageId": "1525", "endLine": 303, "endColumn": 31, "suggestions": "2034"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 307, "column": 33, "nodeType": "1524", "messageId": "1525", "endLine": 307, "endColumn": 36, "suggestions": "2035"}, {"ruleId": "2036", "severity": 1, "message": "2037", "line": 329, "column": 1, "nodeType": "2038", "endLine": 334, "endColumn": 3}, {"ruleId": "2039", "severity": 2, "message": "2040", "line": 4, "column": 3, "nodeType": "2041", "messageId": "2042", "endLine": 4, "endColumn": 21}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 4, "column": 17, "nodeType": "1524", "messageId": "1525", "endLine": 4, "endColumn": 20, "suggestions": "2043"}, {"ruleId": "1512", "severity": 2, "message": "2044", "line": 2, "column": 10, "nodeType": null, "messageId": "1514", "endLine": 2, "endColumn": 19}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 31, "column": 18, "nodeType": "1524", "messageId": "1525", "endLine": 31, "endColumn": 21, "suggestions": "2045"}, {"ruleId": "1512", "severity": 2, "message": "2046", "line": 70, "column": 23, "nodeType": null, "messageId": "1514", "endLine": 70, "endColumn": 30}, {"ruleId": "1512", "severity": 2, "message": "2047", "line": 75, "column": 20, "nodeType": null, "messageId": "1514", "endLine": 75, "endColumn": 34}, {"ruleId": "1512", "severity": 2, "message": "2048", "line": 75, "column": 44, "nodeType": null, "messageId": "1514", "endLine": 75, "endColumn": 50}, {"ruleId": "1512", "severity": 2, "message": "2048", "line": 80, "column": 23, "nodeType": null, "messageId": "1514", "endLine": 80, "endColumn": 29}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 85, "column": 55, "nodeType": "1524", "messageId": "1525", "endLine": 85, "endColumn": 58, "suggestions": "2049"}, {"ruleId": "1512", "severity": 2, "message": "2048", "line": 97, "column": 24, "nodeType": null, "messageId": "1514", "endLine": 97, "endColumn": 30}, {"ruleId": "1512", "severity": 2, "message": "2050", "line": 102, "column": 33, "nodeType": null, "messageId": "1514", "endLine": 102, "endColumn": 40}, {"ruleId": "1512", "severity": 2, "message": "2051", "line": 107, "column": 39, "nodeType": null, "messageId": "1514", "endLine": 107, "endColumn": 51}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 107, "column": 53, "nodeType": "1524", "messageId": "1525", "endLine": 107, "endColumn": 56, "suggestions": "2052"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 113, "column": 18, "nodeType": "1524", "messageId": "1525", "endLine": 113, "endColumn": 21, "suggestions": "2053"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 150, "column": 46, "nodeType": "1524", "messageId": "1525", "endLine": 150, "endColumn": 49, "suggestions": "2054"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 19, "column": 29, "nodeType": "1524", "messageId": "1525", "endLine": 19, "endColumn": 32, "suggestions": "2055"}, {"ruleId": "2056", "severity": 2, "message": "2057", "line": 61, "column": 20, "nodeType": "2058", "messageId": "2059", "endLine": 61, "endColumn": 37}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 107, "column": 23, "nodeType": "1524", "messageId": "1525", "endLine": 107, "endColumn": 26, "suggestions": "2060"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 4, "column": 47, "nodeType": "1524", "messageId": "1525", "endLine": 4, "endColumn": 50, "suggestions": "2061"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 9, "column": 44, "nodeType": "1524", "messageId": "1525", "endLine": 9, "endColumn": 47, "suggestions": "2062"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 37, "column": 12, "nodeType": "1524", "messageId": "1525", "endLine": 37, "endColumn": 15, "suggestions": "2063"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 46, "column": 12, "nodeType": "1524", "messageId": "1525", "endLine": 46, "endColumn": 15, "suggestions": "2064"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 54, "column": 45, "nodeType": "1524", "messageId": "1525", "endLine": 54, "endColumn": 48, "suggestions": "2065"}, {"ruleId": "1512", "severity": 2, "message": "2066", "line": 6, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 6, "endColumn": 19}, {"ruleId": "1512", "severity": 2, "message": "1633", "line": 77, "column": 12, "nodeType": null, "messageId": "1514", "endLine": 77, "endColumn": 17}, {"ruleId": "1785", "severity": 2, "message": "2067", "line": 28, "column": 7, "nodeType": "1787", "messageId": "1788", "endLine": 28, "endColumn": 19, "fix": "2068"}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 59, "column": 60, "nodeType": "1524", "messageId": "1525", "endLine": 59, "endColumn": 63, "suggestions": "2069"}, {"ruleId": "1512", "severity": 2, "message": "2070", "line": 23, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 23, "endColumn": 8}, {"ruleId": "1512", "severity": 2, "message": "2071", "line": 24, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 24, "endColumn": 14}, {"ruleId": "1512", "severity": 2, "message": "1516", "line": 25, "column": 3, "nodeType": null, "messageId": "1514", "endLine": 25, "endColumn": 15}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 35, "column": 54, "nodeType": "1524", "messageId": "1525", "endLine": 35, "endColumn": 57, "suggestions": "2072"}, {"ruleId": "1512", "severity": 2, "message": "2073", "line": 40, "column": 10, "nodeType": null, "messageId": "1514", "endLine": 40, "endColumn": 20}, {"ruleId": "1512", "severity": 2, "message": "2074", "line": 191, "column": 9, "nodeType": null, "messageId": "1514", "endLine": 191, "endColumn": 31}, {"ruleId": "1522", "severity": 2, "message": "1523", "line": 212, "column": 56, "nodeType": "1524", "messageId": "1525", "endLine": 212, "endColumn": 59, "suggestions": "2075"}, {"ruleId": "1528", "severity": 2, "message": "1529", "line": 316, "column": 21, "nodeType": "1530", "messageId": "1531", "suggestions": "2076"}, {"ruleId": "1512", "severity": 2, "message": "1613", "line": 5, "column": 27, "nodeType": null, "messageId": "1514", "endLine": 5, "endColumn": 34}, {"ruleId": "1512", "severity": 2, "message": "1633", "line": 19, "column": 12, "nodeType": null, "messageId": "1514", "endLine": 19, "endColumn": 17}, "@typescript-eslint/no-unused-vars", "'MoreVertical' is defined but never used.", "unusedVar", "'FolderOpen' is defined but never used.", "'ChevronRight' is defined but never used.", "'ChevronDown' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook React.useEffect has an unnecessary dependency: 'editingCategory'. Either exclude it or remove the dependency array. Outer scope values like 'editingCategory' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["2077"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["2078", "2079"], ["2080", "2081"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["2082", "2083", "2084", "2085"], ["2086", "2087"], ["2088", "2089"], ["2090", "2091"], ["2092", "2093"], "'Phone' is defined but never used.", "'MapPin' is defined but never used.", "'Calendar' is defined but never used.", "'Edit' is defined but never used.", "'Star' is defined but never used.", "'selectedCustomer' is assigned a value but never used.", "'setSelectedCustomer' is assigned a value but never used.", "'showCustomerDetails' is assigned a value but never used.", "'setShowCustomerDetails' is assigned a value but never used.", "'showAddModal' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchEnquiries'. Either include it or remove the dependency array.", ["2094"], "'useMemo' is defined but never used.", "'Palette' is defined but never used.", "'Users' is defined but never used.", "'Download' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchInitialData'. Either include it or remove the dependency array.", ["2095"], ["2096", "2097"], ["2098", "2099", "2100", "2101"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["2102", "2103", "2104", "2105"], ["2106", "2107", "2108", "2109"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["2110", "2111", "2112", "2113"], ["2114", "2115", "2116", "2117"], ["2118", "2119", "2120", "2121"], ["2122", "2123", "2124", "2125"], ["2126", "2127", "2128", "2129"], ["2130", "2131", "2132", "2133"], "'index' is defined but never used.", "'Filter' is defined but never used.", "'FolderPlus' is defined but never used.", "'uploading' is assigned a value but never used.", ["2134", "2135"], "'Mail' is defined but never used.", "'Search' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSubscribers'. Either include it or remove the dependency array.", ["2136"], "React Hook useEffect has a missing dependency: 'fetchNotifications'. Either include it or remove the dependency array.", ["2137"], "'Bell' is defined but never used.", "'customMessage' is assigned a value but never used.", "'Settings' is defined but never used.", "'showCreateForm' is assigned a value but never used.", "'DollarSign' is defined but never used.", "'User' is defined but never used.", "'CreditCard' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchOrders'. Either include it or remove the dependency array.", ["2138"], "'handleViewOrder' is assigned a value but never used.", "'FileText' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchOrder'. Either include it or remove the dependency array.", ["2139"], "'Eye' is defined but never used.", ["2140", "2141"], ["2142", "2143", "2144", "2145"], ["2146", "2147", "2148", "2149"], ["2150", "2151"], "'Package' is defined but never used.", "'CheckSquare' is defined but never used.", "'Square' is defined but never used.", "'X' is defined but never used.", "'formatStock' is defined but never used.", "'getStockColor' is defined but never used.", "'ImageSelector' is defined but never used.", "'jsonError' is defined but never used.", "'e' is defined but never used.", ["2152", "2153"], "'toggleFeatured' is assigned a value but never used.", ["2154", "2155"], ["2156", "2157"], "'Truck' is defined but never used.", "'Database' is defined but never used.", "'request' is defined but never used.", ["2158", "2159"], ["2160", "2161"], ["2162", "2163"], ["2164", "2165"], ["2166", "2167"], ["2168", "2169"], ["2170", "2171"], ["2172", "2173"], "'Review' is defined but never used.", ["2174", "2175"], "'status' is assigned a value but never used.", "'skip' is assigned a value but never used.", ["2176", "2177"], ["2178", "2179"], "'handleApiError' is defined but never used.", "'ValidationError' is defined but never used.", "'RateLimitError' is defined but never used.", ["2180", "2181"], ["2182", "2183"], "'error' is defined but never used.", ["2184", "2185"], ["2186", "2187"], ["2188", "2189"], "'_err' is defined but never used.", ["2190", "2191"], ["2192", "2193"], ["2194", "2195"], ["2196", "2197"], ["2198", "2199"], ["2200", "2201"], ["2202", "2203"], ["2204", "2205"], ["2206", "2207"], "'session' is assigned a value but never used.", ["2208", "2209"], ["2210", "2211"], ["2212", "2213"], ["2214", "2215"], ["2216", "2217"], ["2218", "2219"], ["2220", "2221"], ["2222", "2223"], ["2224", "2225"], ["2226", "2227"], ["2228", "2229"], ["2230", "2231"], ["2232", "2233"], ["2234", "2235"], ["2236", "2237"], ["2238", "2239"], ["2240", "2241"], ["2242", "2243"], ["2244", "2245"], ["2246", "2247"], ["2248", "2249"], ["2250", "2251"], ["2252", "2253"], ["2254", "2255"], ["2256", "2257"], ["2258", "2259"], ["2260", "2261"], ["2262", "2263"], ["2264", "2265"], ["2266", "2267"], ["2268", "2269"], ["2270", "2271"], ["2272", "2273"], ["2274", "2275"], ["2276", "2277"], ["2278", "2279"], "'Product' is defined but never used.", "'Category' is defined but never used.", ["2280", "2281"], ["2282", "2283"], ["2284", "2285"], ["2286", "2287"], ["2288", "2289"], "'subscriberData' is assigned a value but never used.", ["2290", "2291"], ["2292", "2293"], ["2294", "2295"], ["2296", "2297"], ["2298", "2299"], ["2300", "2301"], ["2302", "2303"], ["2304", "2305"], ["2306", "2307"], ["2308", "2309"], ["2310", "2311"], ["2312", "2313"], ["2314", "2315"], ["2316", "2317"], ["2318", "2319"], ["2320", "2321"], ["2322", "2323"], ["2324", "2325"], ["2326", "2327"], ["2328", "2329"], ["2330", "2331"], ["2332", "2333"], ["2334", "2335"], ["2336", "2337"], ["2338", "2339"], ["2340", "2341"], ["2342", "2343"], ["2344", "2345"], ["2346", "2347"], ["2348", "2349"], ["2350", "2351"], ["2352", "2353"], ["2354", "2355"], ["2356", "2357"], ["2358", "2359"], ["2360", "2361"], ["2362", "2363"], ["2364", "2365"], ["2366", "2367"], ["2368", "2369"], ["2370", "2371"], ["2372", "2373"], ["2374", "2375"], ["2376", "2377"], ["2378", "2379"], ["2380", "2381"], ["2382", "2383"], "'convertToPaise' is defined but never used.", "'AppError' is defined but never used.", ["2384", "2385"], ["2386", "2387"], ["2388", "2389"], ["2390", "2391"], ["2392", "2393"], ["2394", "2395"], ["2396", "2397"], ["2398", "2399"], ["2400", "2401"], ["2402", "2403"], ["2404", "2405"], ["2406", "2407"], ["2408", "2409"], ["2410", "2411"], ["2412", "2413"], "'Order' is defined but never used.", ["2414", "2415"], ["2416", "2417"], ["2418", "2419"], ["2420", "2421"], ["2422", "2423"], ["2424", "2425"], ["2426", "2427"], ["2428", "2429"], ["2430", "2431"], ["2432", "2433"], ["2434", "2435"], ["2436", "2437"], ["2438", "2439"], ["2440", "2441"], ["2442", "2443"], ["2444", "2445"], ["2446", "2447"], ["2448", "2449"], ["2450", "2451"], ["2452", "2453"], ["2454", "2455"], ["2456", "2457"], ["2458", "2459"], ["2460", "2461"], ["2462", "2463"], ["2464", "2465"], ["2466", "2467"], ["2468", "2469"], "prefer-const", "'total' is never reassigned. Use 'const' instead.", "Identifier", "useConst", ["2470", "2471"], ["2472", "2473"], "'categoryIds' is assigned a value but never used.", ["2474", "2475"], ["2476", "2477"], ["2478", "2479"], ["2480", "2481"], ["2482", "2483"], ["2484", "2485"], ["2486", "2487"], ["2488", "2489"], "@typescript-eslint/ban-ts-comment", "Use \"@ts-expect-error\" instead of \"@ts-ignore\", as \"@ts-ignore\" will do nothing if the following line is error-free.", "Line", "tsIgnoreInsteadOfExpectError", ["2490"], ["2491", "2492"], ["2493", "2494"], ["2495", "2496"], ["2497", "2498"], ["2499", "2500"], ["2501", "2502"], ["2503", "2504"], ["2505", "2506"], ["2507", "2508"], ["2509", "2510"], ["2511", "2512"], ["2513", "2514"], ["2515", "2516"], ["2517", "2518"], ["2519", "2520"], ["2521", "2522"], ["2523", "2524"], ["2525", "2526"], ["2527", "2528"], ["2529", "2530"], ["2531", "2532"], ["2533", "2534"], ["2535", "2536"], ["2537", "2538"], ["2539", "2540"], ["2541", "2542"], ["2543", "2544"], ["2545", "2546"], ["2547", "2548"], ["2549", "2550"], ["2551", "2552"], ["2553", "2554"], ["2555", "2556"], ["2557", "2558"], ["2559", "2560"], ["2561", "2562"], ["2563", "2564"], ["2565", "2566"], ["2567", "2568"], ["2569", "2570"], ["2571", "2572"], ["2573", "2574"], ["2575", "2576", "2577", "2578"], ["2579", "2580", "2581", "2582"], ["2583", "2584", "2585", "2586"], ["2587", "2588", "2589", "2590"], "React Hook useEffect has a missing dependency: 'fetchFAQs'. Either include it or remove the dependency array.", ["2591"], "'editingIndex' is assigned a value but never used.", "'setEditingIndex' is assigned a value but never used.", "'Upload' is defined but never used.", "'currentSelection' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadFiles'. Either include it or remove the dependency array.", ["2592"], "react-hooks/rules-of-hooks", "React Hook \"useEffect\" is called conditionally. React Hooks must be called in the exact same order in every component render.", ["2593"], "React Hook useEffect has a missing dependency: 'fetchReviews'. Either include it or remove the dependency array.", ["2594"], ["2595", "2596", "2597", "2598"], ["2599", "2600", "2601", "2602"], "React Hook useEffect has a missing dependency: 'fetchTestimonials'. Either include it or remove the dependency array.", ["2603"], "React Hook useEffect has a missing dependency: 'fetchVariations'. Either include it or remove the dependency array.", ["2604"], "React Hook useEffect has missing dependencies: 'fetchAvailableCoupons' and 'fetchFeaturedCoupons'. Either include them or remove the dependency array.", ["2605"], ["2606", "2607", "2608", "2609"], "'useState' is defined but never used.", ["2610", "2611"], ["2612", "2613", "2614", "2615"], ["2616", "2617", "2618", "2619"], ["2620", "2621", "2622", "2623"], "'Tag' is defined but never used.", "'subtotal' is assigned a value but never used.", "'shipping' is assigned a value but never used.", ["2624", "2625", "2626", "2627"], ["2628", "2629", "2630", "2631"], "'Heart' is defined but never used.", "'Shield' is defined but never used.", "'Leaf' is defined but never used.", "'RotateCcw' is defined but never used.", ["2632", "2633"], ["2634", "2635"], ["2636", "2637"], "React Hook useEffect has a missing dependency: 'fetchHomepageData'. Either include it or remove the dependency array.", ["2638"], ["2639", "2640"], ["2641", "2642"], ["2643", "2644"], ["2645", "2646"], ["2647", "2648", "2649", "2650"], ["2651"], "'Image' is defined but never used.", "'Loader2' is defined but never used.", ["2652", "2653"], ["2654", "2655"], "'hasVariants' is assigned a value but never used.", ["2656", "2657"], ["2658", "2659"], ["2660", "2661"], ["2662", "2663"], ["2664", "2665"], "'loading' is assigned a value but never used.", ["2666", "2667"], "'Smartphone' is defined but never used.", "'saving' is assigned a value but never used.", "'handleLanguageChange' is assigned a value but never used.", "'productBelongsToCategory' is defined but never used.", "'displayPrice' is assigned a value but never used.", "'originalDBProducts' is assigned a value but never used.", "'originalDBProducts' is never reassigned. Use 'const' instead.", {"range": "2668", "text": "2669"}, ["2670", "2671"], "React Hook useEffect has a missing dependency: 'categories.length'. Either include it or remove the dependency array.", ["2672"], "'ArrowLeft' is defined but never used.", "React Hook useEffect has a missing dependency: 'session'. Either include it or remove the dependency array.", ["2673"], ["2674", "2675"], ["2676", "2677"], "'wishlistItemId' is assigned a value but never used.", ["2678", "2679", "2680", "2681"], ["2682", "2683", "2684", "2685"], ["2686", "2687"], ["2688", "2689"], ["2690", "2691"], "'wishlistLoading' is assigned a value but never used.", ["2692", "2693"], ["2694", "2695"], ["2696", "2697"], ["2698", "2699"], ["2700", "2701"], ["2702", "2703"], ["2704"], ["2705", "2706", "2707", "2708"], "React Hook useEffect has a missing dependency: 'handleKeyDown'. Either include it or remove the dependency array.", ["2709"], "'error' is assigned a value but never used.", ["2710"], "'formatPrice' is defined but never used.", "'basePrice' is defined but never used.", ["2711"], "React Hook useEffect has missing dependencies: 'onVariationChange' and 'selectedVariations'. Either include them or remove the dependency array. If 'onVariationChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2712"], "'variation' is defined but never used.", "'selectedInfo' is assigned a value but never used.", ["2713", "2714", "2715", "2716"], ["2717", "2718", "2719", "2720"], "'useToast' is defined but never used.", "'isHydrated' is assigned a value but never used.", ["2721", "2722"], "React Hook useEffect has a missing dependency: 'state.items'. Either include it or remove the dependency array.", ["2723"], ["2724", "2725"], "React Hook useCallback has missing dependencies: 'fetchNotifications' and 'prevUnreadCount'. Either include them or remove the dependency array.", ["2726"], "'IUser' is defined but never used.", ["2727", "2728"], ["2729", "2730"], ["2731", "2732"], "'profile' is defined but never used.", ["2733", "2734"], ["2735", "2736"], ["2737", "2738"], ["2739", "2740"], ["2741", "2742"], "@typescript-eslint/no-unsafe-function-type", "The `Function` type accepts any function-like value.\nPrefer explicitly defining any function parameters and return type.", "bannedFunctionType", ["2743", "2744"], ["2745", "2746"], ["2747", "2748"], ["2749", "2750"], ["2751", "2752"], ["2753", "2754"], ["2755", "2756"], ["2757", "2758"], ["2759", "2760"], ["2761", "2762"], ["2763", "2764"], ["2765", "2766"], "'quality' is assigned a value but never used.", ["2767", "2768"], ["2769", "2770"], ["2771", "2772"], ["2773", "2774"], ["2775", "2776"], ["2777", "2778"], ["2779", "2780"], ["2781", "2782"], ["2783", "2784"], ["2785", "2786"], ["2787", "2788"], ["2789", "2790"], ["2791", "2792"], ["2793", "2794"], ["2795", "2796"], ["2797", "2798"], ["2799", "2800"], ["2801", "2802"], ["2803", "2804"], ["2805", "2806"], ["2807", "2808"], ["2809", "2810"], ["2811", "2812"], ["2813", "2814"], ["2815", "2816"], "'product' is never reassigned. Use 'const' instead.", {"range": "2817", "text": "2818"}, ["2819", "2820"], ["2821", "2822"], ["2823", "2824"], ["2825", "2826"], ["2827", "2828"], ["2829", "2830"], ["2831", "2832"], ["2833", "2834"], ["2835", "2836"], ["2837", "2838"], ["2839", "2840"], ["2841", "2842"], ["2843", "2844"], ["2845", "2846"], ["2847", "2848"], ["2849", "2850"], ["2851", "2852"], ["2853", "2854"], ["2855", "2856"], ["2857", "2858"], ["2859", "2860"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "no-var", "Unexpected var, use let or const instead.", "VariableDeclaration", "<PERSON><PERSON><PERSON>", ["2861", "2862"], "'sendEmail' is defined but never used.", ["2863", "2864"], "'options' is defined but never used.", "'notificationId' is defined but never used.", "'userId' is defined but never used.", ["2865", "2866"], "'daysOld' is assigned a value but never used.", "'notification' is defined but never used.", ["2867", "2868"], ["2869", "2870"], ["2871", "2872"], ["2873", "2874"], "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["2875", "2876"], ["2877", "2878"], ["2879", "2880"], ["2881", "2882"], ["2883", "2884"], ["2885", "2886"], "'GetObjectCommand' is defined but never used.", "'preprocessed' is never reassigned. Use 'const' instead.", {"range": "2887", "text": "2888"}, ["2889", "2890"], "'Clock' is defined but never used.", "'ChevronLeft' is defined but never used.", ["2891", "2892"], "'totalCount' is assigned a value but never used.", "'formatNotificationType' is assigned a value but never used.", ["2893", "2894"], ["2895", "2896", "2897", "2898"], {"desc": "2899", "fix": "2900"}, {"messageId": "2901", "fix": "2902", "desc": "2903"}, {"messageId": "2904", "fix": "2905", "desc": "2906"}, {"messageId": "2901", "fix": "2907", "desc": "2903"}, {"messageId": "2904", "fix": "2908", "desc": "2906"}, {"messageId": "2909", "data": "2910", "fix": "2911", "desc": "2912"}, {"messageId": "2909", "data": "2913", "fix": "2914", "desc": "2915"}, {"messageId": "2909", "data": "2916", "fix": "2917", "desc": "2918"}, {"messageId": "2909", "data": "2919", "fix": "2920", "desc": "2921"}, {"messageId": "2901", "fix": "2922", "desc": "2903"}, {"messageId": "2904", "fix": "2923", "desc": "2906"}, {"messageId": "2901", "fix": "2924", "desc": "2903"}, {"messageId": "2904", "fix": "2925", "desc": "2906"}, {"messageId": "2901", "fix": "2926", "desc": "2903"}, {"messageId": "2904", "fix": "2927", "desc": "2906"}, {"messageId": "2901", "fix": "2928", "desc": "2903"}, {"messageId": "2904", "fix": "2929", "desc": "2906"}, {"desc": "2930", "fix": "2931"}, {"desc": "2932", "fix": "2933"}, {"messageId": "2901", "fix": "2934", "desc": "2903"}, {"messageId": "2904", "fix": "2935", "desc": "2906"}, {"messageId": "2909", "data": "2936", "fix": "2937", "desc": "2912"}, {"messageId": "2909", "data": "2938", "fix": "2939", "desc": "2915"}, {"messageId": "2909", "data": "2940", "fix": "2941", "desc": "2918"}, {"messageId": "2909", "data": "2942", "fix": "2943", "desc": "2921"}, {"messageId": "2909", "data": "2944", "fix": "2945", "desc": "2946"}, {"messageId": "2909", "data": "2947", "fix": "2948", "desc": "2949"}, {"messageId": "2909", "data": "2950", "fix": "2951", "desc": "2952"}, {"messageId": "2909", "data": "2953", "fix": "2954", "desc": "2955"}, {"messageId": "2909", "data": "2956", "fix": "2957", "desc": "2946"}, {"messageId": "2909", "data": "2958", "fix": "2959", "desc": "2949"}, {"messageId": "2909", "data": "2960", "fix": "2961", "desc": "2952"}, {"messageId": "2909", "data": "2962", "fix": "2963", "desc": "2955"}, {"messageId": "2909", "data": "2964", "fix": "2965", "desc": "2946"}, {"messageId": "2909", "data": "2966", "fix": "2967", "desc": "2949"}, {"messageId": "2909", "data": "2968", "fix": "2969", "desc": "2952"}, {"messageId": "2909", "data": "2970", "fix": "2971", "desc": "2955"}, {"messageId": "2909", "data": "2972", "fix": "2973", "desc": "2946"}, {"messageId": "2909", "data": "2974", "fix": "2975", "desc": "2949"}, {"messageId": "2909", "data": "2976", "fix": "2977", "desc": "2952"}, {"messageId": "2909", "data": "2978", "fix": "2979", "desc": "2955"}, {"messageId": "2909", "data": "2980", "fix": "2981", "desc": "2946"}, {"messageId": "2909", "data": "2982", "fix": "2983", "desc": "2949"}, {"messageId": "2909", "data": "2984", "fix": "2985", "desc": "2952"}, {"messageId": "2909", "data": "2986", "fix": "2987", "desc": "2955"}, {"messageId": "2909", "data": "2988", "fix": "2989", "desc": "2946"}, {"messageId": "2909", "data": "2990", "fix": "2991", "desc": "2949"}, {"messageId": "2909", "data": "2992", "fix": "2993", "desc": "2952"}, {"messageId": "2909", "data": "2994", "fix": "2995", "desc": "2955"}, {"messageId": "2909", "data": "2996", "fix": "2997", "desc": "2946"}, {"messageId": "2909", "data": "2998", "fix": "2999", "desc": "2949"}, {"messageId": "2909", "data": "3000", "fix": "3001", "desc": "2952"}, {"messageId": "2909", "data": "3002", "fix": "3003", "desc": "2955"}, {"messageId": "2909", "data": "3004", "fix": "3005", "desc": "2946"}, {"messageId": "2909", "data": "3006", "fix": "3007", "desc": "2949"}, {"messageId": "2909", "data": "3008", "fix": "3009", "desc": "2952"}, {"messageId": "2909", "data": "3010", "fix": "3011", "desc": "2955"}, {"messageId": "2901", "fix": "3012", "desc": "2903"}, {"messageId": "2904", "fix": "3013", "desc": "2906"}, {"desc": "3014", "fix": "3015"}, {"desc": "3016", "fix": "3017"}, {"desc": "3018", "fix": "3019"}, {"desc": "3020", "fix": "3021"}, {"messageId": "2901", "fix": "3022", "desc": "2903"}, {"messageId": "2904", "fix": "3023", "desc": "2906"}, {"messageId": "2909", "data": "3024", "fix": "3025", "desc": "2912"}, {"messageId": "2909", "data": "3026", "fix": "3027", "desc": "2915"}, {"messageId": "2909", "data": "3028", "fix": "3029", "desc": "2918"}, {"messageId": "2909", "data": "3030", "fix": "3031", "desc": "2921"}, {"messageId": "2909", "data": "3032", "fix": "3033", "desc": "2912"}, {"messageId": "2909", "data": "3034", "fix": "3035", "desc": "2915"}, {"messageId": "2909", "data": "3036", "fix": "3037", "desc": "2918"}, {"messageId": "2909", "data": "3038", "fix": "3039", "desc": "2921"}, {"messageId": "2901", "fix": "3040", "desc": "2903"}, {"messageId": "2904", "fix": "3041", "desc": "2906"}, {"messageId": "2901", "fix": "3042", "desc": "2903"}, {"messageId": "2904", "fix": "3043", "desc": "2906"}, {"messageId": "2901", "fix": "3044", "desc": "2903"}, {"messageId": "2904", "fix": "3045", "desc": "2906"}, {"messageId": "2901", "fix": "3046", "desc": "2903"}, {"messageId": "2904", "fix": "3047", "desc": "2906"}, {"messageId": "2901", "fix": "3048", "desc": "2903"}, {"messageId": "2904", "fix": "3049", "desc": "2906"}, {"messageId": "2901", "fix": "3050", "desc": "2903"}, {"messageId": "2904", "fix": "3051", "desc": "2906"}, {"messageId": "2901", "fix": "3052", "desc": "2903"}, {"messageId": "2904", "fix": "3053", "desc": "2906"}, {"messageId": "2901", "fix": "3054", "desc": "2903"}, {"messageId": "2904", "fix": "3055", "desc": "2906"}, {"messageId": "2901", "fix": "3056", "desc": "2903"}, {"messageId": "2904", "fix": "3057", "desc": "2906"}, {"messageId": "2901", "fix": "3058", "desc": "2903"}, {"messageId": "2904", "fix": "3059", "desc": "2906"}, {"messageId": "2901", "fix": "3060", "desc": "2903"}, {"messageId": "2904", "fix": "3061", "desc": "2906"}, {"messageId": "2901", "fix": "3062", "desc": "2903"}, {"messageId": "2904", "fix": "3063", "desc": "2906"}, {"messageId": "2901", "fix": "3064", "desc": "2903"}, {"messageId": "2904", "fix": "3065", "desc": "2906"}, {"messageId": "2901", "fix": "3066", "desc": "2903"}, {"messageId": "2904", "fix": "3067", "desc": "2906"}, {"messageId": "2901", "fix": "3068", "desc": "2903"}, {"messageId": "2904", "fix": "3069", "desc": "2906"}, {"messageId": "2901", "fix": "3070", "desc": "2903"}, {"messageId": "2904", "fix": "3071", "desc": "2906"}, {"messageId": "2901", "fix": "3072", "desc": "2903"}, {"messageId": "2904", "fix": "3073", "desc": "2906"}, {"messageId": "2901", "fix": "3074", "desc": "2903"}, {"messageId": "2904", "fix": "3075", "desc": "2906"}, {"messageId": "2901", "fix": "3076", "desc": "2903"}, {"messageId": "2904", "fix": "3077", "desc": "2906"}, {"messageId": "2901", "fix": "3078", "desc": "2903"}, {"messageId": "2904", "fix": "3079", "desc": "2906"}, {"messageId": "2901", "fix": "3080", "desc": "2903"}, {"messageId": "2904", "fix": "3081", "desc": "2906"}, {"messageId": "2901", "fix": "3082", "desc": "2903"}, {"messageId": "2904", "fix": "3083", "desc": "2906"}, {"messageId": "2901", "fix": "3084", "desc": "2903"}, {"messageId": "2904", "fix": "3085", "desc": "2906"}, {"messageId": "2901", "fix": "3086", "desc": "2903"}, {"messageId": "2904", "fix": "3087", "desc": "2906"}, {"messageId": "2901", "fix": "3088", "desc": "2903"}, {"messageId": "2904", "fix": "3089", "desc": "2906"}, {"messageId": "2901", "fix": "3090", "desc": "2903"}, {"messageId": "2904", "fix": "3091", "desc": "2906"}, {"messageId": "2901", "fix": "3092", "desc": "2903"}, {"messageId": "2904", "fix": "3093", "desc": "2906"}, {"messageId": "2901", "fix": "3094", "desc": "2903"}, {"messageId": "2904", "fix": "3095", "desc": "2906"}, {"messageId": "2901", "fix": "3096", "desc": "2903"}, {"messageId": "2904", "fix": "3097", "desc": "2906"}, {"messageId": "2901", "fix": "3098", "desc": "2903"}, {"messageId": "2904", "fix": "3099", "desc": "2906"}, {"messageId": "2901", "fix": "3100", "desc": "2903"}, {"messageId": "2904", "fix": "3101", "desc": "2906"}, {"messageId": "2901", "fix": "3102", "desc": "2903"}, {"messageId": "2904", "fix": "3103", "desc": "2906"}, {"messageId": "2901", "fix": "3104", "desc": "2903"}, {"messageId": "2904", "fix": "3105", "desc": "2906"}, {"messageId": "2901", "fix": "3106", "desc": "2903"}, {"messageId": "2904", "fix": "3107", "desc": "2906"}, {"messageId": "2901", "fix": "3108", "desc": "2903"}, {"messageId": "2904", "fix": "3109", "desc": "2906"}, {"messageId": "2901", "fix": "3110", "desc": "2903"}, {"messageId": "2904", "fix": "3111", "desc": "2906"}, {"messageId": "2901", "fix": "3112", "desc": "2903"}, {"messageId": "2904", "fix": "3113", "desc": "2906"}, {"messageId": "2901", "fix": "3114", "desc": "2903"}, {"messageId": "2904", "fix": "3115", "desc": "2906"}, {"messageId": "2901", "fix": "3116", "desc": "2903"}, {"messageId": "2904", "fix": "3117", "desc": "2906"}, {"messageId": "2901", "fix": "3118", "desc": "2903"}, {"messageId": "2904", "fix": "3119", "desc": "2906"}, {"messageId": "2901", "fix": "3120", "desc": "2903"}, {"messageId": "2904", "fix": "3121", "desc": "2906"}, {"messageId": "2901", "fix": "3122", "desc": "2903"}, {"messageId": "2904", "fix": "3123", "desc": "2906"}, {"messageId": "2901", "fix": "3124", "desc": "2903"}, {"messageId": "2904", "fix": "3125", "desc": "2906"}, {"messageId": "2901", "fix": "3126", "desc": "2903"}, {"messageId": "2904", "fix": "3127", "desc": "2906"}, {"messageId": "2901", "fix": "3128", "desc": "2903"}, {"messageId": "2904", "fix": "3129", "desc": "2906"}, {"messageId": "2901", "fix": "3130", "desc": "2903"}, {"messageId": "2904", "fix": "3131", "desc": "2906"}, {"messageId": "2901", "fix": "3132", "desc": "2903"}, {"messageId": "2904", "fix": "3133", "desc": "2906"}, {"messageId": "2901", "fix": "3134", "desc": "2903"}, {"messageId": "2904", "fix": "3135", "desc": "2906"}, {"messageId": "2901", "fix": "3136", "desc": "2903"}, {"messageId": "2904", "fix": "3137", "desc": "2906"}, {"messageId": "2901", "fix": "3138", "desc": "2903"}, {"messageId": "2904", "fix": "3139", "desc": "2906"}, {"messageId": "2901", "fix": "3140", "desc": "2903"}, {"messageId": "2904", "fix": "3141", "desc": "2906"}, {"messageId": "2901", "fix": "3142", "desc": "2903"}, {"messageId": "2904", "fix": "3143", "desc": "2906"}, {"messageId": "2901", "fix": "3144", "desc": "2903"}, {"messageId": "2904", "fix": "3145", "desc": "2906"}, {"messageId": "2901", "fix": "3146", "desc": "2903"}, {"messageId": "2904", "fix": "3147", "desc": "2906"}, {"messageId": "2901", "fix": "3148", "desc": "2903"}, {"messageId": "2904", "fix": "3149", "desc": "2906"}, {"messageId": "2901", "fix": "3150", "desc": "2903"}, {"messageId": "2904", "fix": "3151", "desc": "2906"}, {"messageId": "2901", "fix": "3152", "desc": "2903"}, {"messageId": "2904", "fix": "3153", "desc": "2906"}, {"messageId": "2901", "fix": "3154", "desc": "2903"}, {"messageId": "2904", "fix": "3155", "desc": "2906"}, {"messageId": "2901", "fix": "3156", "desc": "2903"}, {"messageId": "2904", "fix": "3157", "desc": "2906"}, {"messageId": "2901", "fix": "3158", "desc": "2903"}, {"messageId": "2904", "fix": "3159", "desc": "2906"}, {"messageId": "2901", "fix": "3160", "desc": "2903"}, {"messageId": "2904", "fix": "3161", "desc": "2906"}, {"messageId": "2901", "fix": "3162", "desc": "2903"}, {"messageId": "2904", "fix": "3163", "desc": "2906"}, {"messageId": "2901", "fix": "3164", "desc": "2903"}, {"messageId": "2904", "fix": "3165", "desc": "2906"}, {"messageId": "2901", "fix": "3166", "desc": "2903"}, {"messageId": "2904", "fix": "3167", "desc": "2906"}, {"messageId": "2901", "fix": "3168", "desc": "2903"}, {"messageId": "2904", "fix": "3169", "desc": "2906"}, {"messageId": "2901", "fix": "3170", "desc": "2903"}, {"messageId": "2904", "fix": "3171", "desc": "2906"}, {"messageId": "2901", "fix": "3172", "desc": "2903"}, {"messageId": "2904", "fix": "3173", "desc": "2906"}, {"messageId": "2901", "fix": "3174", "desc": "2903"}, {"messageId": "2904", "fix": "3175", "desc": "2906"}, {"messageId": "2901", "fix": "3176", "desc": "2903"}, {"messageId": "2904", "fix": "3177", "desc": "2906"}, {"messageId": "2901", "fix": "3178", "desc": "2903"}, {"messageId": "2904", "fix": "3179", "desc": "2906"}, {"messageId": "2901", "fix": "3180", "desc": "2903"}, {"messageId": "2904", "fix": "3181", "desc": "2906"}, {"messageId": "2901", "fix": "3182", "desc": "2903"}, {"messageId": "2904", "fix": "3183", "desc": "2906"}, {"messageId": "2901", "fix": "3184", "desc": "2903"}, {"messageId": "2904", "fix": "3185", "desc": "2906"}, {"messageId": "2901", "fix": "3186", "desc": "2903"}, {"messageId": "2904", "fix": "3187", "desc": "2906"}, {"messageId": "2901", "fix": "3188", "desc": "2903"}, {"messageId": "2904", "fix": "3189", "desc": "2906"}, {"messageId": "2901", "fix": "3190", "desc": "2903"}, {"messageId": "2904", "fix": "3191", "desc": "2906"}, {"messageId": "2901", "fix": "3192", "desc": "2903"}, {"messageId": "2904", "fix": "3193", "desc": "2906"}, {"messageId": "2901", "fix": "3194", "desc": "2903"}, {"messageId": "2904", "fix": "3195", "desc": "2906"}, {"messageId": "2901", "fix": "3196", "desc": "2903"}, {"messageId": "2904", "fix": "3197", "desc": "2906"}, {"messageId": "2901", "fix": "3198", "desc": "2903"}, {"messageId": "2904", "fix": "3199", "desc": "2906"}, {"messageId": "2901", "fix": "3200", "desc": "2903"}, {"messageId": "2904", "fix": "3201", "desc": "2906"}, {"messageId": "2901", "fix": "3202", "desc": "2903"}, {"messageId": "2904", "fix": "3203", "desc": "2906"}, {"messageId": "2901", "fix": "3204", "desc": "2903"}, {"messageId": "2904", "fix": "3205", "desc": "2906"}, {"messageId": "2901", "fix": "3206", "desc": "2903"}, {"messageId": "2904", "fix": "3207", "desc": "2906"}, {"messageId": "2901", "fix": "3208", "desc": "2903"}, {"messageId": "2904", "fix": "3209", "desc": "2906"}, {"messageId": "2901", "fix": "3210", "desc": "2903"}, {"messageId": "2904", "fix": "3211", "desc": "2906"}, {"messageId": "2901", "fix": "3212", "desc": "2903"}, {"messageId": "2904", "fix": "3213", "desc": "2906"}, {"messageId": "2901", "fix": "3214", "desc": "2903"}, {"messageId": "2904", "fix": "3215", "desc": "2906"}, {"messageId": "2901", "fix": "3216", "desc": "2903"}, {"messageId": "2904", "fix": "3217", "desc": "2906"}, {"messageId": "2901", "fix": "3218", "desc": "2903"}, {"messageId": "2904", "fix": "3219", "desc": "2906"}, {"messageId": "2901", "fix": "3220", "desc": "2903"}, {"messageId": "2904", "fix": "3221", "desc": "2906"}, {"messageId": "2901", "fix": "3222", "desc": "2903"}, {"messageId": "2904", "fix": "3223", "desc": "2906"}, {"messageId": "2901", "fix": "3224", "desc": "2903"}, {"messageId": "2904", "fix": "3225", "desc": "2906"}, {"messageId": "2901", "fix": "3226", "desc": "2903"}, {"messageId": "2904", "fix": "3227", "desc": "2906"}, {"messageId": "2901", "fix": "3228", "desc": "2903"}, {"messageId": "2904", "fix": "3229", "desc": "2906"}, {"messageId": "2901", "fix": "3230", "desc": "2903"}, {"messageId": "2904", "fix": "3231", "desc": "2906"}, {"messageId": "2901", "fix": "3232", "desc": "2903"}, {"messageId": "2904", "fix": "3233", "desc": "2906"}, {"messageId": "2901", "fix": "3234", "desc": "2903"}, {"messageId": "2904", "fix": "3235", "desc": "2906"}, {"messageId": "2901", "fix": "3236", "desc": "2903"}, {"messageId": "2904", "fix": "3237", "desc": "2906"}, {"messageId": "2901", "fix": "3238", "desc": "2903"}, {"messageId": "2904", "fix": "3239", "desc": "2906"}, {"messageId": "2901", "fix": "3240", "desc": "2903"}, {"messageId": "2904", "fix": "3241", "desc": "2906"}, {"messageId": "2901", "fix": "3242", "desc": "2903"}, {"messageId": "2904", "fix": "3243", "desc": "2906"}, {"messageId": "2901", "fix": "3244", "desc": "2903"}, {"messageId": "2904", "fix": "3245", "desc": "2906"}, {"messageId": "2901", "fix": "3246", "desc": "2903"}, {"messageId": "2904", "fix": "3247", "desc": "2906"}, {"messageId": "2901", "fix": "3248", "desc": "2903"}, {"messageId": "2904", "fix": "3249", "desc": "2906"}, {"messageId": "2901", "fix": "3250", "desc": "2903"}, {"messageId": "2904", "fix": "3251", "desc": "2906"}, {"messageId": "2901", "fix": "3252", "desc": "2903"}, {"messageId": "2904", "fix": "3253", "desc": "2906"}, {"messageId": "2901", "fix": "3254", "desc": "2903"}, {"messageId": "2904", "fix": "3255", "desc": "2906"}, {"messageId": "2901", "fix": "3256", "desc": "2903"}, {"messageId": "2904", "fix": "3257", "desc": "2906"}, {"messageId": "2901", "fix": "3258", "desc": "2903"}, {"messageId": "2904", "fix": "3259", "desc": "2906"}, {"messageId": "2901", "fix": "3260", "desc": "2903"}, {"messageId": "2904", "fix": "3261", "desc": "2906"}, {"messageId": "2901", "fix": "3262", "desc": "2903"}, {"messageId": "2904", "fix": "3263", "desc": "2906"}, {"messageId": "2901", "fix": "3264", "desc": "2903"}, {"messageId": "2904", "fix": "3265", "desc": "2906"}, {"messageId": "2901", "fix": "3266", "desc": "2903"}, {"messageId": "2904", "fix": "3267", "desc": "2906"}, {"messageId": "2901", "fix": "3268", "desc": "2903"}, {"messageId": "2904", "fix": "3269", "desc": "2906"}, {"messageId": "2901", "fix": "3270", "desc": "2903"}, {"messageId": "2904", "fix": "3271", "desc": "2906"}, {"messageId": "2901", "fix": "3272", "desc": "2903"}, {"messageId": "2904", "fix": "3273", "desc": "2906"}, {"messageId": "2901", "fix": "3274", "desc": "2903"}, {"messageId": "2904", "fix": "3275", "desc": "2906"}, {"messageId": "2901", "fix": "3276", "desc": "2903"}, {"messageId": "2904", "fix": "3277", "desc": "2906"}, {"messageId": "2901", "fix": "3278", "desc": "2903"}, {"messageId": "2904", "fix": "3279", "desc": "2906"}, {"messageId": "2901", "fix": "3280", "desc": "2903"}, {"messageId": "2904", "fix": "3281", "desc": "2906"}, {"messageId": "2901", "fix": "3282", "desc": "2903"}, {"messageId": "2904", "fix": "3283", "desc": "2906"}, {"messageId": "2901", "fix": "3284", "desc": "2903"}, {"messageId": "2904", "fix": "3285", "desc": "2906"}, {"messageId": "2901", "fix": "3286", "desc": "2903"}, {"messageId": "2904", "fix": "3287", "desc": "2906"}, {"messageId": "2901", "fix": "3288", "desc": "2903"}, {"messageId": "2904", "fix": "3289", "desc": "2906"}, {"messageId": "2901", "fix": "3290", "desc": "2903"}, {"messageId": "2904", "fix": "3291", "desc": "2906"}, {"messageId": "2901", "fix": "3292", "desc": "2903"}, {"messageId": "2904", "fix": "3293", "desc": "2906"}, {"messageId": "2901", "fix": "3294", "desc": "2903"}, {"messageId": "2904", "fix": "3295", "desc": "2906"}, {"messageId": "2901", "fix": "3296", "desc": "2903"}, {"messageId": "2904", "fix": "3297", "desc": "2906"}, {"messageId": "2901", "fix": "3298", "desc": "2903"}, {"messageId": "2904", "fix": "3299", "desc": "2906"}, {"messageId": "2901", "fix": "3300", "desc": "2903"}, {"messageId": "2904", "fix": "3301", "desc": "2906"}, {"messageId": "2901", "fix": "3302", "desc": "2903"}, {"messageId": "2904", "fix": "3303", "desc": "2906"}, {"messageId": "2901", "fix": "3304", "desc": "2903"}, {"messageId": "2904", "fix": "3305", "desc": "2906"}, {"messageId": "2901", "fix": "3306", "desc": "2903"}, {"messageId": "2904", "fix": "3307", "desc": "2906"}, {"messageId": "2901", "fix": "3308", "desc": "2903"}, {"messageId": "2904", "fix": "3309", "desc": "2906"}, {"messageId": "2901", "fix": "3310", "desc": "2903"}, {"messageId": "2904", "fix": "3311", "desc": "2906"}, {"messageId": "2901", "fix": "3312", "desc": "2903"}, {"messageId": "2904", "fix": "3313", "desc": "2906"}, {"messageId": "2901", "fix": "3314", "desc": "2903"}, {"messageId": "2904", "fix": "3315", "desc": "2906"}, {"messageId": "2901", "fix": "3316", "desc": "2903"}, {"messageId": "2904", "fix": "3317", "desc": "2906"}, {"messageId": "2901", "fix": "3318", "desc": "2903"}, {"messageId": "2904", "fix": "3319", "desc": "2906"}, {"messageId": "2901", "fix": "3320", "desc": "2903"}, {"messageId": "2904", "fix": "3321", "desc": "2906"}, {"messageId": "2901", "fix": "3322", "desc": "2903"}, {"messageId": "2904", "fix": "3323", "desc": "2906"}, {"messageId": "2901", "fix": "3324", "desc": "2903"}, {"messageId": "2904", "fix": "3325", "desc": "2906"}, {"messageId": "2901", "fix": "3326", "desc": "2903"}, {"messageId": "2904", "fix": "3327", "desc": "2906"}, {"messageId": "2901", "fix": "3328", "desc": "2903"}, {"messageId": "2904", "fix": "3329", "desc": "2906"}, {"messageId": "2901", "fix": "3330", "desc": "2903"}, {"messageId": "2904", "fix": "3331", "desc": "2906"}, {"messageId": "2901", "fix": "3332", "desc": "2903"}, {"messageId": "2904", "fix": "3333", "desc": "2906"}, {"messageId": "2901", "fix": "3334", "desc": "2903"}, {"messageId": "2904", "fix": "3335", "desc": "2906"}, {"messageId": "2901", "fix": "3336", "desc": "2903"}, {"messageId": "2904", "fix": "3337", "desc": "2906"}, {"messageId": "2901", "fix": "3338", "desc": "2903"}, {"messageId": "2904", "fix": "3339", "desc": "2906"}, {"messageId": "2901", "fix": "3340", "desc": "2903"}, {"messageId": "2904", "fix": "3341", "desc": "2906"}, {"messageId": "2901", "fix": "3342", "desc": "2903"}, {"messageId": "2904", "fix": "3343", "desc": "2906"}, {"messageId": "2901", "fix": "3344", "desc": "2903"}, {"messageId": "2904", "fix": "3345", "desc": "2906"}, {"messageId": "2901", "fix": "3346", "desc": "2903"}, {"messageId": "2904", "fix": "3347", "desc": "2906"}, {"messageId": "2901", "fix": "3348", "desc": "2903"}, {"messageId": "2904", "fix": "3349", "desc": "2906"}, {"messageId": "2901", "fix": "3350", "desc": "2903"}, {"messageId": "2904", "fix": "3351", "desc": "2906"}, {"messageId": "2901", "fix": "3352", "desc": "2903"}, {"messageId": "2904", "fix": "3353", "desc": "2906"}, {"messageId": "2901", "fix": "3354", "desc": "2903"}, {"messageId": "2904", "fix": "3355", "desc": "2906"}, {"messageId": "2901", "fix": "3356", "desc": "2903"}, {"messageId": "2904", "fix": "3357", "desc": "2906"}, {"messageId": "2901", "fix": "3358", "desc": "2903"}, {"messageId": "2904", "fix": "3359", "desc": "2906"}, {"messageId": "2901", "fix": "3360", "desc": "2903"}, {"messageId": "2904", "fix": "3361", "desc": "2906"}, {"messageId": "2901", "fix": "3362", "desc": "2903"}, {"messageId": "2904", "fix": "3363", "desc": "2906"}, {"messageId": "2901", "fix": "3364", "desc": "2903"}, {"messageId": "2904", "fix": "3365", "desc": "2906"}, {"messageId": "2901", "fix": "3366", "desc": "2903"}, {"messageId": "2904", "fix": "3367", "desc": "2906"}, {"messageId": "2901", "fix": "3368", "desc": "2903"}, {"messageId": "2904", "fix": "3369", "desc": "2906"}, {"messageId": "2901", "fix": "3370", "desc": "2903"}, {"messageId": "2904", "fix": "3371", "desc": "2906"}, {"messageId": "2901", "fix": "3372", "desc": "2903"}, {"messageId": "2904", "fix": "3373", "desc": "2906"}, {"messageId": "2901", "fix": "3374", "desc": "2903"}, {"messageId": "2904", "fix": "3375", "desc": "2906"}, {"messageId": "2901", "fix": "3376", "desc": "2903"}, {"messageId": "2904", "fix": "3377", "desc": "2906"}, {"messageId": "2901", "fix": "3378", "desc": "2903"}, {"messageId": "2904", "fix": "3379", "desc": "2906"}, {"messageId": "3380", "fix": "3381", "desc": "3382"}, {"messageId": "2901", "fix": "3383", "desc": "2903"}, {"messageId": "2904", "fix": "3384", "desc": "2906"}, {"messageId": "2901", "fix": "3385", "desc": "2903"}, {"messageId": "2904", "fix": "3386", "desc": "2906"}, {"messageId": "2901", "fix": "3387", "desc": "2903"}, {"messageId": "2904", "fix": "3388", "desc": "2906"}, {"messageId": "2901", "fix": "3389", "desc": "2903"}, {"messageId": "2904", "fix": "3390", "desc": "2906"}, {"messageId": "2901", "fix": "3391", "desc": "2903"}, {"messageId": "2904", "fix": "3392", "desc": "2906"}, {"messageId": "2901", "fix": "3393", "desc": "2903"}, {"messageId": "2904", "fix": "3394", "desc": "2906"}, {"messageId": "2901", "fix": "3395", "desc": "2903"}, {"messageId": "2904", "fix": "3396", "desc": "2906"}, {"messageId": "2901", "fix": "3397", "desc": "2903"}, {"messageId": "2904", "fix": "3398", "desc": "2906"}, {"messageId": "2901", "fix": "3399", "desc": "2903"}, {"messageId": "2904", "fix": "3400", "desc": "2906"}, {"messageId": "2901", "fix": "3401", "desc": "2903"}, {"messageId": "2904", "fix": "3402", "desc": "2906"}, {"messageId": "2901", "fix": "3403", "desc": "2903"}, {"messageId": "2904", "fix": "3404", "desc": "2906"}, {"messageId": "2901", "fix": "3405", "desc": "2903"}, {"messageId": "2904", "fix": "3406", "desc": "2906"}, {"messageId": "2901", "fix": "3407", "desc": "2903"}, {"messageId": "2904", "fix": "3408", "desc": "2906"}, {"messageId": "2901", "fix": "3409", "desc": "2903"}, {"messageId": "2904", "fix": "3410", "desc": "2906"}, {"messageId": "2901", "fix": "3411", "desc": "2903"}, {"messageId": "2904", "fix": "3412", "desc": "2906"}, {"messageId": "2901", "fix": "3413", "desc": "2903"}, {"messageId": "2904", "fix": "3414", "desc": "2906"}, {"messageId": "2901", "fix": "3415", "desc": "2903"}, {"messageId": "2904", "fix": "3416", "desc": "2906"}, {"messageId": "2901", "fix": "3417", "desc": "2903"}, {"messageId": "2904", "fix": "3418", "desc": "2906"}, {"messageId": "2901", "fix": "3419", "desc": "2903"}, {"messageId": "2904", "fix": "3420", "desc": "2906"}, {"messageId": "2901", "fix": "3421", "desc": "2903"}, {"messageId": "2904", "fix": "3422", "desc": "2906"}, {"messageId": "2901", "fix": "3423", "desc": "2903"}, {"messageId": "2904", "fix": "3424", "desc": "2906"}, {"messageId": "2901", "fix": "3425", "desc": "2903"}, {"messageId": "2904", "fix": "3426", "desc": "2906"}, {"messageId": "2901", "fix": "3427", "desc": "2903"}, {"messageId": "2904", "fix": "3428", "desc": "2906"}, {"messageId": "2901", "fix": "3429", "desc": "2903"}, {"messageId": "2904", "fix": "3430", "desc": "2906"}, {"messageId": "2901", "fix": "3431", "desc": "2903"}, {"messageId": "2904", "fix": "3432", "desc": "2906"}, {"messageId": "2901", "fix": "3433", "desc": "2903"}, {"messageId": "2904", "fix": "3434", "desc": "2906"}, {"messageId": "2901", "fix": "3435", "desc": "2903"}, {"messageId": "2904", "fix": "3436", "desc": "2906"}, {"messageId": "2901", "fix": "3437", "desc": "2903"}, {"messageId": "2904", "fix": "3438", "desc": "2906"}, {"messageId": "2901", "fix": "3439", "desc": "2903"}, {"messageId": "2904", "fix": "3440", "desc": "2906"}, {"messageId": "2901", "fix": "3441", "desc": "2903"}, {"messageId": "2904", "fix": "3442", "desc": "2906"}, {"messageId": "2901", "fix": "3443", "desc": "2903"}, {"messageId": "2904", "fix": "3444", "desc": "2906"}, {"messageId": "2901", "fix": "3445", "desc": "2903"}, {"messageId": "2904", "fix": "3446", "desc": "2906"}, {"messageId": "2901", "fix": "3447", "desc": "2903"}, {"messageId": "2904", "fix": "3448", "desc": "2906"}, {"messageId": "2901", "fix": "3449", "desc": "2903"}, {"messageId": "2904", "fix": "3450", "desc": "2906"}, {"messageId": "2901", "fix": "3451", "desc": "2903"}, {"messageId": "2904", "fix": "3452", "desc": "2906"}, {"messageId": "2901", "fix": "3453", "desc": "2903"}, {"messageId": "2904", "fix": "3454", "desc": "2906"}, {"messageId": "2901", "fix": "3455", "desc": "2903"}, {"messageId": "2904", "fix": "3456", "desc": "2906"}, {"messageId": "2901", "fix": "3457", "desc": "2903"}, {"messageId": "2904", "fix": "3458", "desc": "2906"}, {"messageId": "2901", "fix": "3459", "desc": "2903"}, {"messageId": "2904", "fix": "3460", "desc": "2906"}, {"messageId": "2901", "fix": "3461", "desc": "2903"}, {"messageId": "2904", "fix": "3462", "desc": "2906"}, {"messageId": "2901", "fix": "3463", "desc": "2903"}, {"messageId": "2904", "fix": "3464", "desc": "2906"}, {"messageId": "2901", "fix": "3465", "desc": "2903"}, {"messageId": "2904", "fix": "3466", "desc": "2906"}, {"messageId": "2909", "data": "3467", "fix": "3468", "desc": "2946"}, {"messageId": "2909", "data": "3469", "fix": "3470", "desc": "2949"}, {"messageId": "2909", "data": "3471", "fix": "3472", "desc": "2952"}, {"messageId": "2909", "data": "3473", "fix": "3474", "desc": "2955"}, {"messageId": "2909", "data": "3475", "fix": "3476", "desc": "2946"}, {"messageId": "2909", "data": "3477", "fix": "3478", "desc": "2949"}, {"messageId": "2909", "data": "3479", "fix": "3480", "desc": "2952"}, {"messageId": "2909", "data": "3481", "fix": "3482", "desc": "2955"}, {"messageId": "2909", "data": "3483", "fix": "3484", "desc": "2912"}, {"messageId": "2909", "data": "3485", "fix": "3486", "desc": "2915"}, {"messageId": "2909", "data": "3487", "fix": "3488", "desc": "2918"}, {"messageId": "2909", "data": "3489", "fix": "3490", "desc": "2921"}, {"messageId": "2909", "data": "3491", "fix": "3492", "desc": "2912"}, {"messageId": "2909", "data": "3493", "fix": "3494", "desc": "2915"}, {"messageId": "2909", "data": "3495", "fix": "3496", "desc": "2918"}, {"messageId": "2909", "data": "3497", "fix": "3498", "desc": "2921"}, {"desc": "3499", "fix": "3500"}, {"desc": "3501", "fix": "3502"}, {"desc": "3503", "fix": "3504"}, {"desc": "3505", "fix": "3506"}, {"messageId": "2909", "data": "3507", "fix": "3508", "desc": "2946"}, {"messageId": "2909", "data": "3509", "fix": "3510", "desc": "2949"}, {"messageId": "2909", "data": "3511", "fix": "3512", "desc": "2952"}, {"messageId": "2909", "data": "3513", "fix": "3514", "desc": "2955"}, {"messageId": "2909", "data": "3515", "fix": "3516", "desc": "2946"}, {"messageId": "2909", "data": "3517", "fix": "3518", "desc": "2949"}, {"messageId": "2909", "data": "3519", "fix": "3520", "desc": "2952"}, {"messageId": "2909", "data": "3521", "fix": "3522", "desc": "2955"}, {"desc": "3523", "fix": "3524"}, {"desc": "3525", "fix": "3526"}, {"desc": "3527", "fix": "3528"}, {"messageId": "2909", "data": "3529", "fix": "3530", "desc": "2912"}, {"messageId": "2909", "data": "3531", "fix": "3532", "desc": "2915"}, {"messageId": "2909", "data": "3533", "fix": "3534", "desc": "2918"}, {"messageId": "2909", "data": "3535", "fix": "3536", "desc": "2921"}, {"messageId": "2901", "fix": "3537", "desc": "2903"}, {"messageId": "2904", "fix": "3538", "desc": "2906"}, {"messageId": "2909", "data": "3539", "fix": "3540", "desc": "2912"}, {"messageId": "2909", "data": "3541", "fix": "3542", "desc": "2915"}, {"messageId": "2909", "data": "3543", "fix": "3544", "desc": "2918"}, {"messageId": "2909", "data": "3545", "fix": "3546", "desc": "2921"}, {"messageId": "2909", "data": "3547", "fix": "3548", "desc": "2912"}, {"messageId": "2909", "data": "3549", "fix": "3550", "desc": "2915"}, {"messageId": "2909", "data": "3551", "fix": "3552", "desc": "2918"}, {"messageId": "2909", "data": "3553", "fix": "3554", "desc": "2921"}, {"messageId": "2909", "data": "3555", "fix": "3556", "desc": "2912"}, {"messageId": "2909", "data": "3557", "fix": "3558", "desc": "2915"}, {"messageId": "2909", "data": "3559", "fix": "3560", "desc": "2918"}, {"messageId": "2909", "data": "3561", "fix": "3562", "desc": "2921"}, {"messageId": "2909", "data": "3563", "fix": "3564", "desc": "2912"}, {"messageId": "2909", "data": "3565", "fix": "3566", "desc": "2915"}, {"messageId": "2909", "data": "3567", "fix": "3568", "desc": "2918"}, {"messageId": "2909", "data": "3569", "fix": "3570", "desc": "2921"}, {"messageId": "2909", "data": "3571", "fix": "3572", "desc": "2912"}, {"messageId": "2909", "data": "3573", "fix": "3574", "desc": "2915"}, {"messageId": "2909", "data": "3575", "fix": "3576", "desc": "2918"}, {"messageId": "2909", "data": "3577", "fix": "3578", "desc": "2921"}, {"messageId": "2901", "fix": "3579", "desc": "2903"}, {"messageId": "2904", "fix": "3580", "desc": "2906"}, {"messageId": "2901", "fix": "3581", "desc": "2903"}, {"messageId": "2904", "fix": "3582", "desc": "2906"}, {"messageId": "2901", "fix": "3583", "desc": "2903"}, {"messageId": "2904", "fix": "3584", "desc": "2906"}, {"desc": "3585", "fix": "3586"}, {"messageId": "2901", "fix": "3587", "desc": "2903"}, {"messageId": "2904", "fix": "3588", "desc": "2906"}, {"messageId": "2901", "fix": "3589", "desc": "2903"}, {"messageId": "2904", "fix": "3590", "desc": "2906"}, {"messageId": "2901", "fix": "3591", "desc": "2903"}, {"messageId": "2904", "fix": "3592", "desc": "2906"}, {"messageId": "2901", "fix": "3593", "desc": "2903"}, {"messageId": "2904", "fix": "3594", "desc": "2906"}, {"messageId": "2909", "data": "3595", "fix": "3596", "desc": "2912"}, {"messageId": "2909", "data": "3597", "fix": "3598", "desc": "2915"}, {"messageId": "2909", "data": "3599", "fix": "3600", "desc": "2918"}, {"messageId": "2909", "data": "3601", "fix": "3602", "desc": "2921"}, {"desc": "3603", "fix": "3604"}, {"messageId": "2901", "fix": "3605", "desc": "2903"}, {"messageId": "2904", "fix": "3606", "desc": "2906"}, {"messageId": "2901", "fix": "3607", "desc": "2903"}, {"messageId": "2904", "fix": "3608", "desc": "2906"}, {"messageId": "2901", "fix": "3609", "desc": "2903"}, {"messageId": "2904", "fix": "3610", "desc": "2906"}, {"messageId": "2901", "fix": "3611", "desc": "2903"}, {"messageId": "2904", "fix": "3612", "desc": "2906"}, {"messageId": "2901", "fix": "3613", "desc": "2903"}, {"messageId": "2904", "fix": "3614", "desc": "2906"}, {"messageId": "2901", "fix": "3615", "desc": "2903"}, {"messageId": "2904", "fix": "3616", "desc": "2906"}, {"messageId": "2901", "fix": "3617", "desc": "2903"}, {"messageId": "2904", "fix": "3618", "desc": "2906"}, {"messageId": "2901", "fix": "3619", "desc": "2903"}, {"messageId": "2904", "fix": "3620", "desc": "2906"}, [3354, 3395], "const originalDBProducts: DBProduct[] = [];", {"messageId": "2901", "fix": "3621", "desc": "2903"}, {"messageId": "2904", "fix": "3622", "desc": "2906"}, {"desc": "3623", "fix": "3624"}, {"desc": "3625", "fix": "3626"}, {"messageId": "2901", "fix": "3627", "desc": "2903"}, {"messageId": "2904", "fix": "3628", "desc": "2906"}, {"messageId": "2901", "fix": "3629", "desc": "2903"}, {"messageId": "2904", "fix": "3630", "desc": "2906"}, {"messageId": "2909", "data": "3631", "fix": "3632", "desc": "2912"}, {"messageId": "2909", "data": "3633", "fix": "3634", "desc": "2915"}, {"messageId": "2909", "data": "3635", "fix": "3636", "desc": "2918"}, {"messageId": "2909", "data": "3637", "fix": "3638", "desc": "2921"}, {"messageId": "2909", "data": "3639", "fix": "3640", "desc": "2912"}, {"messageId": "2909", "data": "3641", "fix": "3642", "desc": "2915"}, {"messageId": "2909", "data": "3643", "fix": "3644", "desc": "2918"}, {"messageId": "2909", "data": "3645", "fix": "3646", "desc": "2921"}, {"messageId": "2901", "fix": "3647", "desc": "2903"}, {"messageId": "2904", "fix": "3648", "desc": "2906"}, {"messageId": "2901", "fix": "3649", "desc": "2903"}, {"messageId": "2904", "fix": "3650", "desc": "2906"}, {"messageId": "2901", "fix": "3651", "desc": "2903"}, {"messageId": "2904", "fix": "3652", "desc": "2906"}, {"messageId": "2901", "fix": "3653", "desc": "2903"}, {"messageId": "2904", "fix": "3654", "desc": "2906"}, {"messageId": "2901", "fix": "3655", "desc": "2903"}, {"messageId": "2904", "fix": "3656", "desc": "2906"}, {"messageId": "2901", "fix": "3657", "desc": "2903"}, {"messageId": "2904", "fix": "3658", "desc": "2906"}, {"messageId": "2901", "fix": "3659", "desc": "2903"}, {"messageId": "2904", "fix": "3660", "desc": "2906"}, {"messageId": "2901", "fix": "3661", "desc": "2903"}, {"messageId": "2904", "fix": "3662", "desc": "2906"}, {"messageId": "2901", "fix": "3663", "desc": "2903"}, {"messageId": "2904", "fix": "3664", "desc": "2906"}, {"desc": "3665", "fix": "3666"}, {"messageId": "2909", "data": "3667", "fix": "3668", "desc": "2912"}, {"messageId": "2909", "data": "3669", "fix": "3670", "desc": "2915"}, {"messageId": "2909", "data": "3671", "fix": "3672", "desc": "2918"}, {"messageId": "2909", "data": "3673", "fix": "3674", "desc": "2921"}, {"desc": "3675", "fix": "3676"}, {"desc": "3677", "fix": "3678"}, {"desc": "3679", "fix": "3680"}, {"desc": "3681", "fix": "3682"}, {"messageId": "2909", "data": "3683", "fix": "3684", "desc": "2946"}, {"messageId": "2909", "data": "3685", "fix": "3686", "desc": "2949"}, {"messageId": "2909", "data": "3687", "fix": "3688", "desc": "2952"}, {"messageId": "2909", "data": "3689", "fix": "3690", "desc": "2955"}, {"messageId": "2909", "data": "3691", "fix": "3692", "desc": "2946"}, {"messageId": "2909", "data": "3693", "fix": "3694", "desc": "2949"}, {"messageId": "2909", "data": "3695", "fix": "3696", "desc": "2952"}, {"messageId": "2909", "data": "3697", "fix": "3698", "desc": "2955"}, {"messageId": "2901", "fix": "3699", "desc": "2903"}, {"messageId": "2904", "fix": "3700", "desc": "2906"}, {"desc": "3701", "fix": "3702"}, {"messageId": "2901", "fix": "3703", "desc": "2903"}, {"messageId": "2904", "fix": "3704", "desc": "2906"}, {"desc": "3705", "fix": "3706"}, {"messageId": "2901", "fix": "3707", "desc": "2903"}, {"messageId": "2904", "fix": "3708", "desc": "2906"}, {"messageId": "2901", "fix": "3709", "desc": "2903"}, {"messageId": "2904", "fix": "3710", "desc": "2906"}, {"messageId": "2901", "fix": "3711", "desc": "2903"}, {"messageId": "2904", "fix": "3712", "desc": "2906"}, {"messageId": "2901", "fix": "3713", "desc": "2903"}, {"messageId": "2904", "fix": "3714", "desc": "2906"}, {"messageId": "2901", "fix": "3715", "desc": "2903"}, {"messageId": "2904", "fix": "3716", "desc": "2906"}, {"messageId": "2901", "fix": "3717", "desc": "2903"}, {"messageId": "2904", "fix": "3718", "desc": "2906"}, {"messageId": "2901", "fix": "3719", "desc": "2903"}, {"messageId": "2904", "fix": "3720", "desc": "2906"}, {"messageId": "2901", "fix": "3721", "desc": "2903"}, {"messageId": "2904", "fix": "3722", "desc": "2906"}, {"messageId": "2901", "fix": "3723", "desc": "2903"}, {"messageId": "2904", "fix": "3724", "desc": "2906"}, {"messageId": "2901", "fix": "3725", "desc": "2903"}, {"messageId": "2904", "fix": "3726", "desc": "2906"}, {"messageId": "2901", "fix": "3727", "desc": "2903"}, {"messageId": "2904", "fix": "3728", "desc": "2906"}, {"messageId": "2901", "fix": "3729", "desc": "2903"}, {"messageId": "2904", "fix": "3730", "desc": "2906"}, {"messageId": "2901", "fix": "3731", "desc": "2903"}, {"messageId": "2904", "fix": "3732", "desc": "2906"}, {"messageId": "2901", "fix": "3733", "desc": "2903"}, {"messageId": "2904", "fix": "3734", "desc": "2906"}, {"messageId": "2901", "fix": "3735", "desc": "2903"}, {"messageId": "2904", "fix": "3736", "desc": "2906"}, {"messageId": "2901", "fix": "3737", "desc": "2903"}, {"messageId": "2904", "fix": "3738", "desc": "2906"}, {"messageId": "2901", "fix": "3739", "desc": "2903"}, {"messageId": "2904", "fix": "3740", "desc": "2906"}, {"messageId": "2901", "fix": "3741", "desc": "2903"}, {"messageId": "2904", "fix": "3742", "desc": "2906"}, {"messageId": "2901", "fix": "3743", "desc": "2903"}, {"messageId": "2904", "fix": "3744", "desc": "2906"}, {"messageId": "2901", "fix": "3745", "desc": "2903"}, {"messageId": "2904", "fix": "3746", "desc": "2906"}, {"messageId": "2901", "fix": "3747", "desc": "2903"}, {"messageId": "2904", "fix": "3748", "desc": "2906"}, {"messageId": "2901", "fix": "3749", "desc": "2903"}, {"messageId": "2904", "fix": "3750", "desc": "2906"}, {"messageId": "2901", "fix": "3751", "desc": "2903"}, {"messageId": "2904", "fix": "3752", "desc": "2906"}, {"messageId": "2901", "fix": "3753", "desc": "2903"}, {"messageId": "2904", "fix": "3754", "desc": "2906"}, {"messageId": "2901", "fix": "3755", "desc": "2903"}, {"messageId": "2904", "fix": "3756", "desc": "2906"}, {"messageId": "2901", "fix": "3757", "desc": "2903"}, {"messageId": "2904", "fix": "3758", "desc": "2906"}, {"messageId": "2901", "fix": "3759", "desc": "2903"}, {"messageId": "2904", "fix": "3760", "desc": "2906"}, {"messageId": "2901", "fix": "3761", "desc": "2903"}, {"messageId": "2904", "fix": "3762", "desc": "2906"}, {"messageId": "2901", "fix": "3763", "desc": "2903"}, {"messageId": "2904", "fix": "3764", "desc": "2906"}, {"messageId": "2901", "fix": "3765", "desc": "2903"}, {"messageId": "2904", "fix": "3766", "desc": "2906"}, {"messageId": "2901", "fix": "3767", "desc": "2903"}, {"messageId": "2904", "fix": "3768", "desc": "2906"}, {"messageId": "2901", "fix": "3769", "desc": "2903"}, {"messageId": "2904", "fix": "3770", "desc": "2906"}, {"messageId": "2901", "fix": "3771", "desc": "2903"}, {"messageId": "2904", "fix": "3772", "desc": "2906"}, {"messageId": "2901", "fix": "3773", "desc": "2903"}, {"messageId": "2904", "fix": "3774", "desc": "2906"}, {"messageId": "2901", "fix": "3775", "desc": "2903"}, {"messageId": "2904", "fix": "3776", "desc": "2906"}, {"messageId": "2901", "fix": "3777", "desc": "2903"}, {"messageId": "2904", "fix": "3778", "desc": "2906"}, {"messageId": "2901", "fix": "3779", "desc": "2903"}, {"messageId": "2904", "fix": "3780", "desc": "2906"}, {"messageId": "2901", "fix": "3781", "desc": "2903"}, {"messageId": "2904", "fix": "3782", "desc": "2906"}, {"messageId": "2901", "fix": "3783", "desc": "2903"}, {"messageId": "2904", "fix": "3784", "desc": "2906"}, {"messageId": "2901", "fix": "3785", "desc": "2903"}, {"messageId": "2904", "fix": "3786", "desc": "2906"}, {"messageId": "2901", "fix": "3787", "desc": "2903"}, {"messageId": "2904", "fix": "3788", "desc": "2906"}, {"messageId": "2901", "fix": "3789", "desc": "2903"}, {"messageId": "2904", "fix": "3790", "desc": "2906"}, {"messageId": "2901", "fix": "3791", "desc": "2903"}, {"messageId": "2904", "fix": "3792", "desc": "2906"}, {"messageId": "2901", "fix": "3793", "desc": "2903"}, {"messageId": "2904", "fix": "3794", "desc": "2906"}, {"messageId": "2901", "fix": "3795", "desc": "2903"}, {"messageId": "2904", "fix": "3796", "desc": "2906"}, [714, 764], "const product = await Product.findOne(query).lean();", {"messageId": "2901", "fix": "3797", "desc": "2903"}, {"messageId": "2904", "fix": "3798", "desc": "2906"}, {"messageId": "2901", "fix": "3799", "desc": "2903"}, {"messageId": "2904", "fix": "3800", "desc": "2906"}, {"messageId": "2901", "fix": "3801", "desc": "2903"}, {"messageId": "2904", "fix": "3802", "desc": "2906"}, {"messageId": "2901", "fix": "3803", "desc": "2903"}, {"messageId": "2904", "fix": "3804", "desc": "2906"}, {"messageId": "2901", "fix": "3805", "desc": "2903"}, {"messageId": "2904", "fix": "3806", "desc": "2906"}, {"messageId": "2901", "fix": "3807", "desc": "2903"}, {"messageId": "2904", "fix": "3808", "desc": "2906"}, {"messageId": "2901", "fix": "3809", "desc": "2903"}, {"messageId": "2904", "fix": "3810", "desc": "2906"}, {"messageId": "2901", "fix": "3811", "desc": "2903"}, {"messageId": "2904", "fix": "3812", "desc": "2906"}, {"messageId": "2901", "fix": "3813", "desc": "2903"}, {"messageId": "2904", "fix": "3814", "desc": "2906"}, {"messageId": "2901", "fix": "3815", "desc": "2903"}, {"messageId": "2904", "fix": "3816", "desc": "2906"}, {"messageId": "2901", "fix": "3817", "desc": "2903"}, {"messageId": "2904", "fix": "3818", "desc": "2906"}, {"messageId": "2901", "fix": "3819", "desc": "2903"}, {"messageId": "2904", "fix": "3820", "desc": "2906"}, {"messageId": "2901", "fix": "3821", "desc": "2903"}, {"messageId": "2904", "fix": "3822", "desc": "2906"}, {"messageId": "2901", "fix": "3823", "desc": "2903"}, {"messageId": "2904", "fix": "3824", "desc": "2906"}, {"messageId": "2901", "fix": "3825", "desc": "2903"}, {"messageId": "2904", "fix": "3826", "desc": "2906"}, {"messageId": "2901", "fix": "3827", "desc": "2903"}, {"messageId": "2904", "fix": "3828", "desc": "2906"}, {"messageId": "2901", "fix": "3829", "desc": "2903"}, {"messageId": "2904", "fix": "3830", "desc": "2906"}, {"messageId": "2901", "fix": "3831", "desc": "2903"}, {"messageId": "2904", "fix": "3832", "desc": "2906"}, {"messageId": "2901", "fix": "3833", "desc": "2903"}, {"messageId": "2904", "fix": "3834", "desc": "2906"}, {"messageId": "2901", "fix": "3835", "desc": "2903"}, {"messageId": "2904", "fix": "3836", "desc": "2906"}, {"messageId": "2901", "fix": "3837", "desc": "2903"}, {"messageId": "2904", "fix": "3838", "desc": "2906"}, {"messageId": "2901", "fix": "3839", "desc": "2903"}, {"messageId": "2904", "fix": "3840", "desc": "2906"}, {"messageId": "2901", "fix": "3841", "desc": "2903"}, {"messageId": "2904", "fix": "3842", "desc": "2906"}, {"messageId": "2901", "fix": "3843", "desc": "2903"}, {"messageId": "2904", "fix": "3844", "desc": "2906"}, {"messageId": "2901", "fix": "3845", "desc": "2903"}, {"messageId": "2904", "fix": "3846", "desc": "2906"}, {"messageId": "2901", "fix": "3847", "desc": "2903"}, {"messageId": "2904", "fix": "3848", "desc": "2906"}, {"messageId": "2901", "fix": "3849", "desc": "2903"}, {"messageId": "2904", "fix": "3850", "desc": "2906"}, {"messageId": "2901", "fix": "3851", "desc": "2903"}, {"messageId": "2904", "fix": "3852", "desc": "2906"}, {"messageId": "2901", "fix": "3853", "desc": "2903"}, {"messageId": "2904", "fix": "3854", "desc": "2906"}, {"messageId": "2901", "fix": "3855", "desc": "2903"}, {"messageId": "2904", "fix": "3856", "desc": "2906"}, {"messageId": "2901", "fix": "3857", "desc": "2903"}, {"messageId": "2904", "fix": "3858", "desc": "2906"}, {"messageId": "2901", "fix": "3859", "desc": "2903"}, {"messageId": "2904", "fix": "3860", "desc": "2906"}, {"messageId": "2901", "fix": "3861", "desc": "2903"}, {"messageId": "2904", "fix": "3862", "desc": "2906"}, {"messageId": "2901", "fix": "3863", "desc": "2903"}, {"messageId": "2904", "fix": "3864", "desc": "2906"}, [819, 1065], "const preprocessed = dirty\n    .replace(/javascript:/gi, \"\")\n    .replace(/on\\w+\\s*=/gi, \"\") // Remove event handlers\n    .replace(/<script[^>]*>[\\s\\S]*?<\\/script>/gi, \"\") // Remove script tags\n    .replace(/<iframe[^>]*>[\\s\\S]*?<\\/iframe>/gi, \"\");", {"messageId": "2901", "fix": "3865", "desc": "2903"}, {"messageId": "2904", "fix": "3866", "desc": "2906"}, {"messageId": "2901", "fix": "3867", "desc": "2903"}, {"messageId": "2904", "fix": "3868", "desc": "2906"}, {"messageId": "2901", "fix": "3869", "desc": "2903"}, {"messageId": "2904", "fix": "3870", "desc": "2906"}, {"messageId": "2909", "data": "3871", "fix": "3872", "desc": "2912"}, {"messageId": "2909", "data": "3873", "fix": "3874", "desc": "2915"}, {"messageId": "2909", "data": "3875", "fix": "3876", "desc": "2918"}, {"messageId": "2909", "data": "3877", "fix": "3878", "desc": "2921"}, "Update the dependencies array to be: []", {"range": "3879", "text": "3880"}, "suggestUnknown", {"range": "3881", "text": "3882"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "3883", "text": "3884"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "3885", "text": "3882"}, {"range": "3886", "text": "3884"}, "replaceWithAlt", {"alt": "3887"}, {"range": "3888", "text": "3889"}, "Replace with `&apos;`.", {"alt": "3890"}, {"range": "3891", "text": "3892"}, "Replace with `&lsquo;`.", {"alt": "3893"}, {"range": "3894", "text": "3895"}, "Replace with `&#39;`.", {"alt": "3896"}, {"range": "3897", "text": "3898"}, "Replace with `&rsquo;`.", {"range": "3899", "text": "3882"}, {"range": "3900", "text": "3884"}, {"range": "3901", "text": "3882"}, {"range": "3902", "text": "3884"}, {"range": "3903", "text": "3882"}, {"range": "3904", "text": "3884"}, {"range": "3905", "text": "3882"}, {"range": "3906", "text": "3884"}, "Update the dependencies array to be: [session, status, router, currentPage, statusFilter, searchTerm, fetchEnquiries]", {"range": "3907", "text": "3908"}, "Update the dependencies array to be: [session, status, router, fetchInitialData]", {"range": "3909", "text": "3910"}, {"range": "3911", "text": "3882"}, {"range": "3912", "text": "3884"}, {"alt": "3887"}, {"range": "3913", "text": "3914"}, {"alt": "3890"}, {"range": "3915", "text": "3916"}, {"alt": "3893"}, {"range": "3917", "text": "3918"}, {"alt": "3896"}, {"range": "3919", "text": "3920"}, {"alt": "3921"}, {"range": "3922", "text": "3923"}, "Replace with `&quot;`.", {"alt": "3924"}, {"range": "3925", "text": "3926"}, "Replace with `&ldquo;`.", {"alt": "3927"}, {"range": "3928", "text": "3929"}, "Replace with `&#34;`.", {"alt": "3930"}, {"range": "3931", "text": "3932"}, "Replace with `&rdquo;`.", {"alt": "3921"}, {"range": "3933", "text": "3934"}, {"alt": "3924"}, {"range": "3935", "text": "3936"}, {"alt": "3927"}, {"range": "3937", "text": "3938"}, {"alt": "3930"}, {"range": "3939", "text": "3940"}, {"alt": "3921"}, {"range": "3941", "text": "3942"}, {"alt": "3924"}, {"range": "3943", "text": "3944"}, {"alt": "3927"}, {"range": "3945", "text": "3946"}, {"alt": "3930"}, {"range": "3947", "text": "3948"}, {"alt": "3921"}, {"range": "3949", "text": "3950"}, {"alt": "3924"}, {"range": "3951", "text": "3952"}, {"alt": "3927"}, {"range": "3953", "text": "3954"}, {"alt": "3930"}, {"range": "3955", "text": "3956"}, {"alt": "3921"}, {"range": "3957", "text": "3958"}, {"alt": "3924"}, {"range": "3959", "text": "3960"}, {"alt": "3927"}, {"range": "3961", "text": "3962"}, {"alt": "3930"}, {"range": "3963", "text": "3964"}, {"alt": "3921"}, {"range": "3965", "text": "3966"}, {"alt": "3924"}, {"range": "3967", "text": "3968"}, {"alt": "3927"}, {"range": "3969", "text": "3970"}, {"alt": "3930"}, {"range": "3971", "text": "3972"}, {"alt": "3921"}, {"range": "3973", "text": "3974"}, {"alt": "3924"}, {"range": "3975", "text": "3976"}, {"alt": "3927"}, {"range": "3977", "text": "3978"}, {"alt": "3930"}, {"range": "3979", "text": "3980"}, {"alt": "3921"}, {"range": "3981", "text": "3982"}, {"alt": "3924"}, {"range": "3983", "text": "3984"}, {"alt": "3927"}, {"range": "3985", "text": "3986"}, {"alt": "3930"}, {"range": "3987", "text": "3988"}, {"range": "3989", "text": "3882"}, {"range": "3990", "text": "3884"}, "Update the dependencies array to be: [session, status, router, pagination.page, filters, fetchSubscribers]", {"range": "3991", "text": "3992"}, "Update the dependencies array to be: [page, typeFilter, statusFilter, fetchNotifications]", {"range": "3993", "text": "3994"}, "Update the dependencies array to be: [session, statusFilter, paymentStatusFilter, fetchOrders]", {"range": "3995", "text": "3996"}, "Update the dependencies array to be: [session, params.id, fetchOrder]", {"range": "3997", "text": "3998"}, {"range": "3999", "text": "3882"}, {"range": "4000", "text": "3884"}, {"alt": "3887"}, {"range": "4001", "text": "4002"}, {"alt": "3890"}, {"range": "4003", "text": "4004"}, {"alt": "3893"}, {"range": "4005", "text": "4006"}, {"alt": "3896"}, {"range": "4007", "text": "4008"}, {"alt": "3887"}, {"range": "4009", "text": "4010"}, {"alt": "3890"}, {"range": "4011", "text": "4012"}, {"alt": "3893"}, {"range": "4013", "text": "4014"}, {"alt": "3896"}, {"range": "4015", "text": "4016"}, {"range": "4017", "text": "3882"}, {"range": "4018", "text": "3884"}, {"range": "4019", "text": "3882"}, {"range": "4020", "text": "3884"}, {"range": "4021", "text": "3882"}, {"range": "4022", "text": "3884"}, {"range": "4023", "text": "3882"}, {"range": "4024", "text": "3884"}, {"range": "4025", "text": "3882"}, {"range": "4026", "text": "3884"}, {"range": "4027", "text": "3882"}, {"range": "4028", "text": "3884"}, {"range": "4029", "text": "3882"}, {"range": "4030", "text": "3884"}, {"range": "4031", "text": "3882"}, {"range": "4032", "text": "3884"}, {"range": "4033", "text": "3882"}, {"range": "4034", "text": "3884"}, {"range": "4035", "text": "3882"}, {"range": "4036", "text": "3884"}, {"range": "4037", "text": "3882"}, {"range": "4038", "text": "3884"}, {"range": "4039", "text": "3882"}, {"range": "4040", "text": "3884"}, {"range": "4041", "text": "3882"}, {"range": "4042", "text": "3884"}, {"range": "4043", "text": "3882"}, {"range": "4044", "text": "3884"}, {"range": "4045", "text": "3882"}, {"range": "4046", "text": "3884"}, {"range": "4047", "text": "3882"}, {"range": "4048", "text": "3884"}, {"range": "4049", "text": "3882"}, {"range": "4050", "text": "3884"}, {"range": "4051", "text": "3882"}, {"range": "4052", "text": "3884"}, {"range": "4053", "text": "3882"}, {"range": "4054", "text": "3884"}, {"range": "4055", "text": "3882"}, {"range": "4056", "text": "3884"}, {"range": "4057", "text": "3882"}, {"range": "4058", "text": "3884"}, {"range": "4059", "text": "3882"}, {"range": "4060", "text": "3884"}, {"range": "4061", "text": "3882"}, {"range": "4062", "text": "3884"}, {"range": "4063", "text": "3882"}, {"range": "4064", "text": "3884"}, {"range": "4065", "text": "3882"}, {"range": "4066", "text": "3884"}, {"range": "4067", "text": "3882"}, {"range": "4068", "text": "3884"}, {"range": "4069", "text": "3882"}, {"range": "4070", "text": "3884"}, {"range": "4071", "text": "3882"}, {"range": "4072", "text": "3884"}, {"range": "4073", "text": "3882"}, {"range": "4074", "text": "3884"}, {"range": "4075", "text": "3882"}, {"range": "4076", "text": "3884"}, {"range": "4077", "text": "3882"}, {"range": "4078", "text": "3884"}, {"range": "4079", "text": "3882"}, {"range": "4080", "text": "3884"}, {"range": "4081", "text": "3882"}, {"range": "4082", "text": "3884"}, {"range": "4083", "text": "3882"}, {"range": "4084", "text": "3884"}, {"range": "4085", "text": "3882"}, {"range": "4086", "text": "3884"}, {"range": "4087", "text": "3882"}, {"range": "4088", "text": "3884"}, {"range": "4089", "text": "3882"}, {"range": "4090", "text": "3884"}, {"range": "4091", "text": "3882"}, {"range": "4092", "text": "3884"}, {"range": "4093", "text": "3882"}, {"range": "4094", "text": "3884"}, {"range": "4095", "text": "3882"}, {"range": "4096", "text": "3884"}, {"range": "4097", "text": "3882"}, {"range": "4098", "text": "3884"}, {"range": "4099", "text": "3882"}, {"range": "4100", "text": "3884"}, {"range": "4101", "text": "3882"}, {"range": "4102", "text": "3884"}, {"range": "4103", "text": "3882"}, {"range": "4104", "text": "3884"}, {"range": "4105", "text": "3882"}, {"range": "4106", "text": "3884"}, {"range": "4107", "text": "3882"}, {"range": "4108", "text": "3884"}, {"range": "4109", "text": "3882"}, {"range": "4110", "text": "3884"}, {"range": "4111", "text": "3882"}, {"range": "4112", "text": "3884"}, {"range": "4113", "text": "3882"}, {"range": "4114", "text": "3884"}, {"range": "4115", "text": "3882"}, {"range": "4116", "text": "3884"}, {"range": "4117", "text": "3882"}, {"range": "4118", "text": "3884"}, {"range": "4119", "text": "3882"}, {"range": "4120", "text": "3884"}, {"range": "4121", "text": "3882"}, {"range": "4122", "text": "3884"}, {"range": "4123", "text": "3882"}, {"range": "4124", "text": "3884"}, {"range": "4125", "text": "3882"}, {"range": "4126", "text": "3884"}, {"range": "4127", "text": "3882"}, {"range": "4128", "text": "3884"}, {"range": "4129", "text": "3882"}, {"range": "4130", "text": "3884"}, {"range": "4131", "text": "3882"}, {"range": "4132", "text": "3884"}, {"range": "4133", "text": "3882"}, {"range": "4134", "text": "3884"}, {"range": "4135", "text": "3882"}, {"range": "4136", "text": "3884"}, {"range": "4137", "text": "3882"}, {"range": "4138", "text": "3884"}, {"range": "4139", "text": "3882"}, {"range": "4140", "text": "3884"}, {"range": "4141", "text": "3882"}, {"range": "4142", "text": "3884"}, {"range": "4143", "text": "3882"}, {"range": "4144", "text": "3884"}, {"range": "4145", "text": "3882"}, {"range": "4146", "text": "3884"}, {"range": "4147", "text": "3882"}, {"range": "4148", "text": "3884"}, {"range": "4149", "text": "3882"}, {"range": "4150", "text": "3884"}, {"range": "4151", "text": "3882"}, {"range": "4152", "text": "3884"}, {"range": "4153", "text": "3882"}, {"range": "4154", "text": "3884"}, {"range": "4155", "text": "3882"}, {"range": "4156", "text": "3884"}, {"range": "4157", "text": "3882"}, {"range": "4158", "text": "3884"}, {"range": "4159", "text": "3882"}, {"range": "4160", "text": "3884"}, {"range": "4161", "text": "3882"}, {"range": "4162", "text": "3884"}, {"range": "4163", "text": "3882"}, {"range": "4164", "text": "3884"}, {"range": "4165", "text": "3882"}, {"range": "4166", "text": "3884"}, {"range": "4167", "text": "3882"}, {"range": "4168", "text": "3884"}, {"range": "4169", "text": "3882"}, {"range": "4170", "text": "3884"}, {"range": "4171", "text": "3882"}, {"range": "4172", "text": "3884"}, {"range": "4173", "text": "3882"}, {"range": "4174", "text": "3884"}, {"range": "4175", "text": "3882"}, {"range": "4176", "text": "3884"}, {"range": "4177", "text": "3882"}, {"range": "4178", "text": "3884"}, {"range": "4179", "text": "3882"}, {"range": "4180", "text": "3884"}, {"range": "4181", "text": "3882"}, {"range": "4182", "text": "3884"}, {"range": "4183", "text": "3882"}, {"range": "4184", "text": "3884"}, {"range": "4185", "text": "3882"}, {"range": "4186", "text": "3884"}, {"range": "4187", "text": "3882"}, {"range": "4188", "text": "3884"}, {"range": "4189", "text": "3882"}, {"range": "4190", "text": "3884"}, {"range": "4191", "text": "3882"}, {"range": "4192", "text": "3884"}, {"range": "4193", "text": "3882"}, {"range": "4194", "text": "3884"}, {"range": "4195", "text": "3882"}, {"range": "4196", "text": "3884"}, {"range": "4197", "text": "3882"}, {"range": "4198", "text": "3884"}, {"range": "4199", "text": "3882"}, {"range": "4200", "text": "3884"}, {"range": "4201", "text": "3882"}, {"range": "4202", "text": "3884"}, {"range": "4203", "text": "3882"}, {"range": "4204", "text": "3884"}, {"range": "4205", "text": "3882"}, {"range": "4206", "text": "3884"}, {"range": "4207", "text": "3882"}, {"range": "4208", "text": "3884"}, {"range": "4209", "text": "3882"}, {"range": "4210", "text": "3884"}, {"range": "4211", "text": "3882"}, {"range": "4212", "text": "3884"}, {"range": "4213", "text": "3882"}, {"range": "4214", "text": "3884"}, {"range": "4215", "text": "3882"}, {"range": "4216", "text": "3884"}, {"range": "4217", "text": "3882"}, {"range": "4218", "text": "3884"}, {"range": "4219", "text": "3882"}, {"range": "4220", "text": "3884"}, {"range": "4221", "text": "3882"}, {"range": "4222", "text": "3884"}, {"range": "4223", "text": "3882"}, {"range": "4224", "text": "3884"}, {"range": "4225", "text": "3882"}, {"range": "4226", "text": "3884"}, {"range": "4227", "text": "3882"}, {"range": "4228", "text": "3884"}, {"range": "4229", "text": "3882"}, {"range": "4230", "text": "3884"}, {"range": "4231", "text": "3882"}, {"range": "4232", "text": "3884"}, {"range": "4233", "text": "3882"}, {"range": "4234", "text": "3884"}, {"range": "4235", "text": "3882"}, {"range": "4236", "text": "3884"}, {"range": "4237", "text": "3882"}, {"range": "4238", "text": "3884"}, {"range": "4239", "text": "3882"}, {"range": "4240", "text": "3884"}, {"range": "4241", "text": "3882"}, {"range": "4242", "text": "3884"}, {"range": "4243", "text": "3882"}, {"range": "4244", "text": "3884"}, {"range": "4245", "text": "3882"}, {"range": "4246", "text": "3884"}, {"range": "4247", "text": "3882"}, {"range": "4248", "text": "3884"}, {"range": "4249", "text": "3882"}, {"range": "4250", "text": "3884"}, {"range": "4251", "text": "3882"}, {"range": "4252", "text": "3884"}, {"range": "4253", "text": "3882"}, {"range": "4254", "text": "3884"}, {"range": "4255", "text": "3882"}, {"range": "4256", "text": "3884"}, {"range": "4257", "text": "3882"}, {"range": "4258", "text": "3884"}, {"range": "4259", "text": "3882"}, {"range": "4260", "text": "3884"}, {"range": "4261", "text": "3882"}, {"range": "4262", "text": "3884"}, {"range": "4263", "text": "3882"}, {"range": "4264", "text": "3884"}, {"range": "4265", "text": "3882"}, {"range": "4266", "text": "3884"}, {"range": "4267", "text": "3882"}, {"range": "4268", "text": "3884"}, {"range": "4269", "text": "3882"}, {"range": "4270", "text": "3884"}, {"range": "4271", "text": "3882"}, {"range": "4272", "text": "3884"}, {"range": "4273", "text": "3882"}, {"range": "4274", "text": "3884"}, {"range": "4275", "text": "3882"}, {"range": "4276", "text": "3884"}, {"range": "4277", "text": "3882"}, {"range": "4278", "text": "3884"}, {"range": "4279", "text": "3882"}, {"range": "4280", "text": "3884"}, {"range": "4281", "text": "3882"}, {"range": "4282", "text": "3884"}, {"range": "4283", "text": "3882"}, {"range": "4284", "text": "3884"}, {"range": "4285", "text": "3882"}, {"range": "4286", "text": "3884"}, {"range": "4287", "text": "3882"}, {"range": "4288", "text": "3884"}, {"range": "4289", "text": "3882"}, {"range": "4290", "text": "3884"}, {"range": "4291", "text": "3882"}, {"range": "4292", "text": "3884"}, {"range": "4293", "text": "3882"}, {"range": "4294", "text": "3884"}, {"range": "4295", "text": "3882"}, {"range": "4296", "text": "3884"}, {"range": "4297", "text": "3882"}, {"range": "4298", "text": "3884"}, {"range": "4299", "text": "3882"}, {"range": "4300", "text": "3884"}, {"range": "4301", "text": "3882"}, {"range": "4302", "text": "3884"}, {"range": "4303", "text": "3882"}, {"range": "4304", "text": "3884"}, {"range": "4305", "text": "3882"}, {"range": "4306", "text": "3884"}, {"range": "4307", "text": "3882"}, {"range": "4308", "text": "3884"}, {"range": "4309", "text": "3882"}, {"range": "4310", "text": "3884"}, {"range": "4311", "text": "3882"}, {"range": "4312", "text": "3884"}, {"range": "4313", "text": "3882"}, {"range": "4314", "text": "3884"}, {"range": "4315", "text": "3882"}, {"range": "4316", "text": "3884"}, {"range": "4317", "text": "3882"}, {"range": "4318", "text": "3884"}, {"range": "4319", "text": "3882"}, {"range": "4320", "text": "3884"}, {"range": "4321", "text": "3882"}, {"range": "4322", "text": "3884"}, {"range": "4323", "text": "3882"}, {"range": "4324", "text": "3884"}, {"range": "4325", "text": "3882"}, {"range": "4326", "text": "3884"}, {"range": "4327", "text": "3882"}, {"range": "4328", "text": "3884"}, {"range": "4329", "text": "3882"}, {"range": "4330", "text": "3884"}, {"range": "4331", "text": "3882"}, {"range": "4332", "text": "3884"}, {"range": "4333", "text": "3882"}, {"range": "4334", "text": "3884"}, {"range": "4335", "text": "3882"}, {"range": "4336", "text": "3884"}, {"range": "4337", "text": "3882"}, {"range": "4338", "text": "3884"}, {"range": "4339", "text": "3882"}, {"range": "4340", "text": "3884"}, {"range": "4341", "text": "3882"}, {"range": "4342", "text": "3884"}, {"range": "4343", "text": "3882"}, {"range": "4344", "text": "3884"}, {"range": "4345", "text": "3882"}, {"range": "4346", "text": "3884"}, {"range": "4347", "text": "3882"}, {"range": "4348", "text": "3884"}, {"range": "4349", "text": "3882"}, {"range": "4350", "text": "3884"}, {"range": "4351", "text": "3882"}, {"range": "4352", "text": "3884"}, {"range": "4353", "text": "3882"}, {"range": "4354", "text": "3884"}, {"range": "4355", "text": "3882"}, {"range": "4356", "text": "3884"}, "replaceTsIgnoreWithTsExpectError", {"range": "4357", "text": "4358"}, "Replace \"@ts-ignore\" with \"@ts-expect-error\".", {"range": "4359", "text": "3882"}, {"range": "4360", "text": "3884"}, {"range": "4361", "text": "3882"}, {"range": "4362", "text": "3884"}, {"range": "4363", "text": "3882"}, {"range": "4364", "text": "3884"}, {"range": "4365", "text": "3882"}, {"range": "4366", "text": "3884"}, {"range": "4367", "text": "3882"}, {"range": "4368", "text": "3884"}, {"range": "4369", "text": "3882"}, {"range": "4370", "text": "3884"}, {"range": "4371", "text": "3882"}, {"range": "4372", "text": "3884"}, {"range": "4373", "text": "3882"}, {"range": "4374", "text": "3884"}, {"range": "4375", "text": "3882"}, {"range": "4376", "text": "3884"}, {"range": "4377", "text": "3882"}, {"range": "4378", "text": "3884"}, {"range": "4379", "text": "3882"}, {"range": "4380", "text": "3884"}, {"range": "4381", "text": "3882"}, {"range": "4382", "text": "3884"}, {"range": "4383", "text": "3882"}, {"range": "4384", "text": "3884"}, {"range": "4385", "text": "3882"}, {"range": "4386", "text": "3884"}, {"range": "4387", "text": "3882"}, {"range": "4388", "text": "3884"}, {"range": "4389", "text": "3882"}, {"range": "4390", "text": "3884"}, {"range": "4391", "text": "3882"}, {"range": "4392", "text": "3884"}, {"range": "4393", "text": "3882"}, {"range": "4394", "text": "3884"}, {"range": "4395", "text": "3882"}, {"range": "4396", "text": "3884"}, {"range": "4397", "text": "3882"}, {"range": "4398", "text": "3884"}, {"range": "4399", "text": "3882"}, {"range": "4400", "text": "3884"}, {"range": "4401", "text": "3882"}, {"range": "4402", "text": "3884"}, {"range": "4403", "text": "3882"}, {"range": "4404", "text": "3884"}, {"range": "4405", "text": "3882"}, {"range": "4406", "text": "3884"}, {"range": "4407", "text": "3882"}, {"range": "4408", "text": "3884"}, {"range": "4409", "text": "3882"}, {"range": "4410", "text": "3884"}, {"range": "4411", "text": "3882"}, {"range": "4412", "text": "3884"}, {"range": "4413", "text": "3882"}, {"range": "4414", "text": "3884"}, {"range": "4415", "text": "3882"}, {"range": "4416", "text": "3884"}, {"range": "4417", "text": "3882"}, {"range": "4418", "text": "3884"}, {"range": "4419", "text": "3882"}, {"range": "4420", "text": "3884"}, {"range": "4421", "text": "3882"}, {"range": "4422", "text": "3884"}, {"range": "4423", "text": "3882"}, {"range": "4424", "text": "3884"}, {"range": "4425", "text": "3882"}, {"range": "4426", "text": "3884"}, {"range": "4427", "text": "3882"}, {"range": "4428", "text": "3884"}, {"range": "4429", "text": "3882"}, {"range": "4430", "text": "3884"}, {"range": "4431", "text": "3882"}, {"range": "4432", "text": "3884"}, {"range": "4433", "text": "3882"}, {"range": "4434", "text": "3884"}, {"range": "4435", "text": "3882"}, {"range": "4436", "text": "3884"}, {"range": "4437", "text": "3882"}, {"range": "4438", "text": "3884"}, {"range": "4439", "text": "3882"}, {"range": "4440", "text": "3884"}, {"range": "4441", "text": "3882"}, {"range": "4442", "text": "3884"}, {"alt": "3921"}, {"range": "4443", "text": "4444"}, {"alt": "3924"}, {"range": "4445", "text": "4446"}, {"alt": "3927"}, {"range": "4447", "text": "4448"}, {"alt": "3930"}, {"range": "4449", "text": "4450"}, {"alt": "3921"}, {"range": "4451", "text": "3921"}, {"alt": "3924"}, {"range": "4452", "text": "3924"}, {"alt": "3927"}, {"range": "4453", "text": "3927"}, {"alt": "3930"}, {"range": "4454", "text": "3930"}, {"alt": "3887"}, {"range": "4455", "text": "4456"}, {"alt": "3890"}, {"range": "4457", "text": "4458"}, {"alt": "3893"}, {"range": "4459", "text": "4460"}, {"alt": "3896"}, {"range": "4461", "text": "4462"}, {"alt": "3887"}, {"range": "4463", "text": "4464"}, {"alt": "3890"}, {"range": "4465", "text": "4466"}, {"alt": "3893"}, {"range": "4467", "text": "4468"}, {"alt": "3896"}, {"range": "4469", "text": "4470"}, "Update the dependencies array to be: [fetchFAQs, isOpen, product]", {"range": "4471", "text": "4472"}, "Update the dependencies array to be: [isOpen, loadFiles]", {"range": "4473", "text": "4474"}, "Update the dependencies array to be: [fetchOrders, filters]", {"range": "4475", "text": "4476"}, "Update the dependencies array to be: [fetchReviews, status]", {"range": "4477", "text": "4478"}, {"alt": "3921"}, {"range": "4479", "text": "4480"}, {"alt": "3924"}, {"range": "4481", "text": "4482"}, {"alt": "3927"}, {"range": "4483", "text": "4484"}, {"alt": "3930"}, {"range": "4485", "text": "4486"}, {"alt": "3921"}, {"range": "4487", "text": "4488"}, {"alt": "3924"}, {"range": "4489", "text": "4490"}, {"alt": "3927"}, {"range": "4491", "text": "4492"}, {"alt": "3930"}, {"range": "4493", "text": "4494"}, "Update the dependencies array to be: [fetchTestimonials, isOpen]", {"range": "4495", "text": "4496"}, "Update the dependencies array to be: [fetchVariations, isOpen, product]", {"range": "4497", "text": "4498"}, "Update the dependencies array to be: [cartItems, userId, flashSaleActive, fetchAvailableCoupons, fetchFeaturedCoupons]", {"range": "4499", "text": "4500"}, {"alt": "3887"}, {"range": "4501", "text": "4502"}, {"alt": "3890"}, {"range": "4503", "text": "4504"}, {"alt": "3893"}, {"range": "4505", "text": "4506"}, {"alt": "3896"}, {"range": "4507", "text": "4508"}, {"range": "4509", "text": "3882"}, {"range": "4510", "text": "3884"}, {"alt": "3887"}, {"range": "4511", "text": "4512"}, {"alt": "3890"}, {"range": "4513", "text": "4514"}, {"alt": "3893"}, {"range": "4515", "text": "4516"}, {"alt": "3896"}, {"range": "4517", "text": "4518"}, {"alt": "3887"}, {"range": "4519", "text": "4520"}, {"alt": "3890"}, {"range": "4521", "text": "4522"}, {"alt": "3893"}, {"range": "4523", "text": "4524"}, {"alt": "3896"}, {"range": "4525", "text": "4526"}, {"alt": "3887"}, {"range": "4527", "text": "4528"}, {"alt": "3890"}, {"range": "4529", "text": "4530"}, {"alt": "3893"}, {"range": "4531", "text": "4532"}, {"alt": "3896"}, {"range": "4533", "text": "4534"}, {"alt": "3887"}, {"range": "4535", "text": "4536"}, {"alt": "3890"}, {"range": "4537", "text": "4538"}, {"alt": "3893"}, {"range": "4539", "text": "4540"}, {"alt": "3896"}, {"range": "4541", "text": "4542"}, {"alt": "3887"}, {"range": "4543", "text": "4544"}, {"alt": "3890"}, {"range": "4545", "text": "4546"}, {"alt": "3893"}, {"range": "4547", "text": "4548"}, {"alt": "3896"}, {"range": "4549", "text": "4550"}, {"range": "4551", "text": "3882"}, {"range": "4552", "text": "3884"}, {"range": "4553", "text": "3882"}, {"range": "4554", "text": "3884"}, {"range": "4555", "text": "3882"}, {"range": "4556", "text": "3884"}, "Update the dependencies array to be: [fetchHomepageData]", {"range": "4557", "text": "4558"}, {"range": "4559", "text": "3882"}, {"range": "4560", "text": "3884"}, {"range": "4561", "text": "3882"}, {"range": "4562", "text": "3884"}, {"range": "4563", "text": "3882"}, {"range": "4564", "text": "3884"}, {"range": "4565", "text": "3882"}, {"range": "4566", "text": "3884"}, {"alt": "3887"}, {"range": "4567", "text": "4568"}, {"alt": "3890"}, {"range": "4569", "text": "4570"}, {"alt": "3893"}, {"range": "4571", "text": "4572"}, {"alt": "3896"}, {"range": "4573", "text": "4574"}, "Update the dependencies array to be: [fetchOrders, page, selectedFilter, session]", {"range": "4575", "text": "4576"}, {"range": "4577", "text": "3882"}, {"range": "4578", "text": "3884"}, {"range": "4579", "text": "3882"}, {"range": "4580", "text": "3884"}, {"range": "4581", "text": "3882"}, {"range": "4582", "text": "3884"}, {"range": "4583", "text": "3882"}, {"range": "4584", "text": "3884"}, {"range": "4585", "text": "3882"}, {"range": "4586", "text": "3884"}, {"range": "4587", "text": "3882"}, {"range": "4588", "text": "3884"}, {"range": "4589", "text": "3882"}, {"range": "4590", "text": "3884"}, {"range": "4591", "text": "3882"}, {"range": "4592", "text": "3884"}, {"range": "4593", "text": "3882"}, {"range": "4594", "text": "3884"}, "Update the dependencies array to be: [categories.length, sortBy]", {"range": "4595", "text": "4596"}, "Update the dependencies array to be: [status, router, session]", {"range": "4597", "text": "4598"}, {"range": "4599", "text": "3882"}, {"range": "4600", "text": "3884"}, {"range": "4601", "text": "3882"}, {"range": "4602", "text": "3884"}, {"alt": "3887"}, {"range": "4603", "text": "4604"}, {"alt": "3890"}, {"range": "4605", "text": "4606"}, {"alt": "3893"}, {"range": "4607", "text": "4608"}, {"alt": "3896"}, {"range": "4609", "text": "4610"}, {"alt": "3887"}, {"range": "4611", "text": "4612"}, {"alt": "3890"}, {"range": "4613", "text": "4614"}, {"alt": "3893"}, {"range": "4615", "text": "4616"}, {"alt": "3896"}, {"range": "4617", "text": "4618"}, {"range": "4619", "text": "3882"}, {"range": "4620", "text": "3884"}, {"range": "4621", "text": "3882"}, {"range": "4622", "text": "3884"}, {"range": "4623", "text": "3882"}, {"range": "4624", "text": "3884"}, {"range": "4625", "text": "3882"}, {"range": "4626", "text": "3884"}, {"range": "4627", "text": "3882"}, {"range": "4628", "text": "3884"}, {"range": "4629", "text": "3882"}, {"range": "4630", "text": "3884"}, {"range": "4631", "text": "3882"}, {"range": "4632", "text": "3884"}, {"range": "4633", "text": "3882"}, {"range": "4634", "text": "3884"}, {"range": "4635", "text": "3882"}, {"range": "4636", "text": "3884"}, "Update the dependencies array to be: [fetchFAQs, productId]", {"range": "4637", "text": "4638"}, {"alt": "3887"}, {"range": "4639", "text": "4640"}, {"alt": "3890"}, {"range": "4641", "text": "4642"}, {"alt": "3893"}, {"range": "4643", "text": "4644"}, {"alt": "3896"}, {"range": "4645", "text": "4646"}, "Update the dependencies array to be: [handleKeyDown, showLightbox]", {"range": "4647", "text": "4648"}, "Update the dependencies array to be: [fetchReviews, productId]", {"range": "4649", "text": "4650"}, "Update the dependencies array to be: [fetchVariations, productId]", {"range": "4651", "text": "4652"}, "Update the dependencies array to be: [onVariationChange, selectedVariations, variations]", {"range": "4653", "text": "4654"}, {"alt": "3921"}, {"range": "4655", "text": "4656"}, {"alt": "3924"}, {"range": "4657", "text": "4658"}, {"alt": "3927"}, {"range": "4659", "text": "4660"}, {"alt": "3930"}, {"range": "4661", "text": "4662"}, {"alt": "3921"}, {"range": "4663", "text": "4664"}, {"alt": "3924"}, {"range": "4665", "text": "4666"}, {"alt": "3927"}, {"range": "4667", "text": "4668"}, {"alt": "3930"}, {"range": "4669", "text": "4670"}, {"range": "4671", "text": "3882"}, {"range": "4672", "text": "3884"}, "Update the dependencies array to be: [state.isHydrated, state.items]", {"range": "4673", "text": "4674"}, {"range": "4675", "text": "3882"}, {"range": "4676", "text": "3884"}, "Update the dependencies array to be: [fetchNotifications, prevUnreadCount, session?.user?.id]", {"range": "4677", "text": "4678"}, {"range": "4679", "text": "3882"}, {"range": "4680", "text": "3884"}, {"range": "4681", "text": "3882"}, {"range": "4682", "text": "3884"}, {"range": "4683", "text": "3882"}, {"range": "4684", "text": "3884"}, {"range": "4685", "text": "3882"}, {"range": "4686", "text": "3884"}, {"range": "4687", "text": "3882"}, {"range": "4688", "text": "3884"}, {"range": "4689", "text": "3882"}, {"range": "4690", "text": "3884"}, {"range": "4691", "text": "3882"}, {"range": "4692", "text": "3884"}, {"range": "4693", "text": "3882"}, {"range": "4694", "text": "3884"}, {"range": "4695", "text": "3882"}, {"range": "4696", "text": "3884"}, {"range": "4697", "text": "3882"}, {"range": "4698", "text": "3884"}, {"range": "4699", "text": "3882"}, {"range": "4700", "text": "3884"}, {"range": "4701", "text": "3882"}, {"range": "4702", "text": "3884"}, {"range": "4703", "text": "3882"}, {"range": "4704", "text": "3884"}, {"range": "4705", "text": "3882"}, {"range": "4706", "text": "3884"}, {"range": "4707", "text": "3882"}, {"range": "4708", "text": "3884"}, {"range": "4709", "text": "3882"}, {"range": "4710", "text": "3884"}, {"range": "4711", "text": "3882"}, {"range": "4712", "text": "3884"}, {"range": "4713", "text": "3882"}, {"range": "4714", "text": "3884"}, {"range": "4715", "text": "3882"}, {"range": "4716", "text": "3884"}, {"range": "4717", "text": "3882"}, {"range": "4718", "text": "3884"}, {"range": "4719", "text": "3882"}, {"range": "4720", "text": "3884"}, {"range": "4721", "text": "3882"}, {"range": "4722", "text": "3884"}, {"range": "4723", "text": "3882"}, {"range": "4724", "text": "3884"}, {"range": "4725", "text": "3882"}, {"range": "4726", "text": "3884"}, {"range": "4727", "text": "3882"}, {"range": "4728", "text": "3884"}, {"range": "4729", "text": "3882"}, {"range": "4730", "text": "3884"}, {"range": "4731", "text": "3882"}, {"range": "4732", "text": "3884"}, {"range": "4733", "text": "3882"}, {"range": "4734", "text": "3884"}, {"range": "4735", "text": "3882"}, {"range": "4736", "text": "3884"}, {"range": "4737", "text": "3882"}, {"range": "4738", "text": "3884"}, {"range": "4739", "text": "3882"}, {"range": "4740", "text": "3884"}, {"range": "4741", "text": "3882"}, {"range": "4742", "text": "3884"}, {"range": "4743", "text": "3882"}, {"range": "4744", "text": "3884"}, {"range": "4745", "text": "3882"}, {"range": "4746", "text": "3884"}, {"range": "4747", "text": "3882"}, {"range": "4748", "text": "3884"}, {"range": "4749", "text": "3882"}, {"range": "4750", "text": "3884"}, {"range": "4751", "text": "3882"}, {"range": "4752", "text": "3884"}, {"range": "4753", "text": "3882"}, {"range": "4754", "text": "3884"}, {"range": "4755", "text": "3882"}, {"range": "4756", "text": "3884"}, {"range": "4757", "text": "3882"}, {"range": "4758", "text": "3884"}, {"range": "4759", "text": "3882"}, {"range": "4760", "text": "3884"}, {"range": "4761", "text": "3882"}, {"range": "4762", "text": "3884"}, {"range": "4763", "text": "3882"}, {"range": "4764", "text": "3884"}, {"range": "4765", "text": "3882"}, {"range": "4766", "text": "3884"}, {"range": "4767", "text": "3882"}, {"range": "4768", "text": "3884"}, {"range": "4769", "text": "3882"}, {"range": "4770", "text": "3884"}, {"range": "4771", "text": "3882"}, {"range": "4772", "text": "3884"}, {"range": "4773", "text": "3882"}, {"range": "4774", "text": "3884"}, {"range": "4775", "text": "3882"}, {"range": "4776", "text": "3884"}, {"range": "4777", "text": "3882"}, {"range": "4778", "text": "3884"}, {"range": "4779", "text": "3882"}, {"range": "4780", "text": "3884"}, {"range": "4781", "text": "3882"}, {"range": "4782", "text": "3884"}, {"range": "4783", "text": "3882"}, {"range": "4784", "text": "3884"}, {"range": "4785", "text": "3882"}, {"range": "4786", "text": "3884"}, {"range": "4787", "text": "3882"}, {"range": "4788", "text": "3884"}, {"range": "4789", "text": "3882"}, {"range": "4790", "text": "3884"}, {"range": "4791", "text": "3882"}, {"range": "4792", "text": "3884"}, {"range": "4793", "text": "3882"}, {"range": "4794", "text": "3884"}, {"range": "4795", "text": "3882"}, {"range": "4796", "text": "3884"}, {"range": "4797", "text": "3882"}, {"range": "4798", "text": "3884"}, {"range": "4799", "text": "3882"}, {"range": "4800", "text": "3884"}, {"range": "4801", "text": "3882"}, {"range": "4802", "text": "3884"}, {"range": "4803", "text": "3882"}, {"range": "4804", "text": "3884"}, {"range": "4805", "text": "3882"}, {"range": "4806", "text": "3884"}, {"range": "4807", "text": "3882"}, {"range": "4808", "text": "3884"}, {"range": "4809", "text": "3882"}, {"range": "4810", "text": "3884"}, {"range": "4811", "text": "3882"}, {"range": "4812", "text": "3884"}, {"range": "4813", "text": "3882"}, {"range": "4814", "text": "3884"}, {"range": "4815", "text": "3882"}, {"range": "4816", "text": "3884"}, {"range": "4817", "text": "3882"}, {"range": "4818", "text": "3884"}, {"range": "4819", "text": "3882"}, {"range": "4820", "text": "3884"}, {"range": "4821", "text": "3882"}, {"range": "4822", "text": "3884"}, {"range": "4823", "text": "3882"}, {"range": "4824", "text": "3884"}, {"range": "4825", "text": "3882"}, {"range": "4826", "text": "3884"}, {"range": "4827", "text": "3882"}, {"range": "4828", "text": "3884"}, {"range": "4829", "text": "3882"}, {"range": "4830", "text": "3884"}, {"range": "4831", "text": "3882"}, {"range": "4832", "text": "3884"}, {"range": "4833", "text": "3882"}, {"range": "4834", "text": "3884"}, {"range": "4835", "text": "3882"}, {"range": "4836", "text": "3884"}, {"range": "4837", "text": "3882"}, {"range": "4838", "text": "3884"}, {"range": "4839", "text": "3882"}, {"range": "4840", "text": "3884"}, {"range": "4841", "text": "3882"}, {"range": "4842", "text": "3884"}, {"alt": "3887"}, {"range": "4843", "text": "4844"}, {"alt": "3890"}, {"range": "4845", "text": "4846"}, {"alt": "3893"}, {"range": "4847", "text": "4848"}, {"alt": "3896"}, {"range": "4849", "text": "4850"}, [7623, 7640], "[]", [3591, 3594], "unknown", [3591, 3594], "never", [10540, 10543], [10540, 10543], "&apos;", [10895, 10973], "\n                You don&apos;t have permission to access this page.\n              ", "&lsquo;", [10895, 10973], "\n                You don&lsquo;t have permission to access this page.\n              ", "&#39;", [10895, 10973], "\n                You don&#39;t have permission to access this page.\n              ", "&rsquo;", [10895, 10973], "\n                You don&rsquo;t have permission to access this page.\n              ", [24085, 24088], [24085, 24088], [24133, 24136], [24133, 24136], [26622, 26625], [26622, 26625], [31315, 31318], [31315, 31318], [1763, 1827], "[session, status, router, currentPage, statusFilter, searchTerm, fetchEnquiries]", [5161, 5186], "[session, status, router, fetchInitialData]", [9470, 9473], [9470, 9473], [10715, 10939], " Changes made here will be\n            reflected on your store&apos;s homepage immediately after saving. For\n            best results, use high-quality images and compelling copy for your\n            featured products.\n          ", [10715, 10939], " Changes made here will be\n            reflected on your store&lsquo;s homepage immediately after saving. For\n            best results, use high-quality images and compelling copy for your\n            featured products.\n          ", [10715, 10939], " Changes made here will be\n            reflected on your store&#39;s homepage immediately after saving. For\n            best results, use high-quality images and compelling copy for your\n            featured products.\n          ", [10715, 10939], " Changes made here will be\n            reflected on your store&rsquo;s homepage immediately after saving. For\n            best results, use high-quality images and compelling copy for your\n            featured products.\n          ", "&quot;", [23891, 24061], "\n              If no product is selected, the system will automatically feature\n              the first product marked as &quot;Featured\" in the product settings.\n            ", "&ldquo;", [23891, 24061], "\n              If no product is selected, the system will automatically feature\n              the first product marked as &ldquo;Featured\" in the product settings.\n            ", "&#34;", [23891, 24061], "\n              If no product is selected, the system will automatically feature\n              the first product marked as &#34;Featured\" in the product settings.\n            ", "&rdquo;", [23891, 24061], "\n              If no product is selected, the system will automatically feature\n              the first product marked as &rdquo;Featured\" in the product settings.\n            ", [23891, 24061], "\n              If no product is selected, the system will automatically feature\n              the first product marked as \"Featured&quot; in the product settings.\n            ", [23891, 24061], "\n              If no product is selected, the system will automatically feature\n              the first product marked as \"Featured&ldquo; in the product settings.\n            ", [23891, 24061], "\n              If no product is selected, the system will automatically feature\n              the first product marked as \"Featured&#34; in the product settings.\n            ", [23891, 24061], "\n              If no product is selected, the system will automatically feature\n              the first product marked as \"Featured&rdquo; in the product settings.\n            ", [28227, 28321], "\n              Action-oriented text like &quot;Shop Now\", \"Learn More\", or \"Get Offer\"\n            ", [28227, 28321], "\n              Action-oriented text like &ldquo;Shop Now\", \"Learn More\", or \"Get Offer\"\n            ", [28227, 28321], "\n              Action-oriented text like &#34;Shop Now\", \"Learn More\", or \"Get Offer\"\n            ", [28227, 28321], "\n              Action-oriented text like &rdquo;Shop Now\", \"Learn More\", or \"Get Offer\"\n            ", [28227, 28321], "\n              Action-oriented text like \"Shop Now&quot;, \"Learn More\", or \"Get Offer\"\n            ", [28227, 28321], "\n              Action-oriented text like \"Shop Now&ldquo;, \"Learn More\", or \"Get Offer\"\n            ", [28227, 28321], "\n              Action-oriented text like \"Shop Now&#34;, \"Learn More\", or \"Get Offer\"\n            ", [28227, 28321], "\n              Action-oriented text like \"Shop Now&rdquo;, \"Learn More\", or \"Get Offer\"\n            ", [28227, 28321], "\n              Action-oriented text like \"Shop Now\", &quot;Learn More\", or \"Get Offer\"\n            ", [28227, 28321], "\n              Action-oriented text like \"Shop Now\", &ldquo;Learn More\", or \"Get Offer\"\n            ", [28227, 28321], "\n              Action-oriented text like \"Shop Now\", &#34;Learn More\", or \"Get Offer\"\n            ", [28227, 28321], "\n              Action-oriented text like \"Shop Now\", &rdquo;Learn More\", or \"Get Offer\"\n            ", [28227, 28321], "\n              Action-oriented text like \"Shop Now\", \"Learn More&quot;, or \"Get Offer\"\n            ", [28227, 28321], "\n              Action-oriented text like \"Shop Now\", \"Learn More&ldquo;, or \"Get Offer\"\n            ", [28227, 28321], "\n              Action-oriented text like \"Shop Now\", \"Learn More&#34;, or \"Get Offer\"\n            ", [28227, 28321], "\n              Action-oriented text like \"Shop Now\", \"Learn More&rdquo;, or \"Get Offer\"\n            ", [28227, 28321], "\n              Action-oriented text like \"Shop Now\", \"Learn More\", or &quot;Get Offer\"\n            ", [28227, 28321], "\n              Action-oriented text like \"Shop Now\", \"Learn More\", or &ldquo;Get Offer\"\n            ", [28227, 28321], "\n              Action-oriented text like \"Shop Now\", \"Learn More\", or &#34;Get Offer\"\n            ", [28227, 28321], "\n              Action-oriented text like \"Shop Now\", \"Learn More\", or &rdquo;Get Offer\"\n            ", [28227, 28321], "\n              Action-oriented text like \"Shop Now\", \"Learn More\", or \"Get Offer&quot;\n            ", [28227, 28321], "\n              Action-oriented text like \"Shop Now\", \"Learn More\", or \"Get Offer&ldquo;\n            ", [28227, 28321], "\n              Action-oriented text like \"Shop Now\", \"Learn More\", or \"Get Offer&#34;\n            ", [28227, 28321], "\n              Action-oriented text like \"Shop Now\", \"Learn More\", or \"Get Offer&rdquo;\n            ", [15505, 15508], [15505, 15508], [1598, 1649], "[session, status, router, pagination.page, filters, fetchSubscribers]", [1044, 1076], "[page, typeFilter, statusFilter, fetchNotifications]", [3652, 3696], "[session, statusFilter, paymentStatusFilter, fetchOrders]", [3376, 3396], "[session, params.id, fetchOrder]", [379, 382], [379, 382], [3156, 3234], "\n            Welcome back! Here&apos;s what's happening with your store.\n          ", [3156, 3234], "\n            Welcome back! Here&lsquo;s what's happening with your store.\n          ", [3156, 3234], "\n            Welcome back! Here&#39;s what's happening with your store.\n          ", [3156, 3234], "\n            Welcome back! Here&rsquo;s what's happening with your store.\n          ", [3156, 3234], "\n            Welcome back! Here's what&apos;s happening with your store.\n          ", [3156, 3234], "\n            Welcome back! Here's what&lsquo;s happening with your store.\n          ", [3156, 3234], "\n            Welcome back! Here's what&#39;s happening with your store.\n          ", [3156, 3234], "\n            Welcome back! Here's what&rsquo;s happening with your store.\n          ", [6562, 6565], [6562, 6565], [19596, 19599], [19596, 19599], [36218, 36221], [36218, 36221], [44398, 44401], [44398, 44401], [1172, 1175], [1172, 1175], [2146, 2149], [2146, 2149], [1349, 1352], [1349, 1352], [1992, 1995], [1992, 1995], [2250, 2253], [2250, 2253], [3140, 3143], [3140, 3143], [3225, 3228], [3225, 3228], [3380, 3383], [3380, 3383], [1293, 1296], [1293, 1296], [3045, 3048], [3045, 3048], [1070, 1073], [1070, 1073], [1952, 1955], [1952, 1955], [1504, 1507], [1504, 1507], [284, 287], [284, 287], [492, 495], [492, 495], [2261, 2264], [2261, 2264], [1484, 1487], [1484, 1487], [1970, 1973], [1970, 1973], [2909, 2912], [2909, 2912], [3239, 3242], [3239, 3242], [3472, 3475], [3472, 3475], [3663, 3666], [3663, 3666], [3806, 3809], [3806, 3809], [4447, 4450], [4447, 4450], [6475, 6478], [6475, 6478], [1657, 1660], [1657, 1660], [2585, 2588], [2585, 2588], [4574, 4577], [4574, 4577], [5737, 5740], [5737, 5740], [5998, 6001], [5998, 6001], [6159, 6162], [6159, 6162], [6606, 6609], [6606, 6609], [7422, 7425], [7422, 7425], [7471, 7474], [7471, 7474], [7519, 7522], [7519, 7522], [7565, 7568], [7565, 7568], [7611, 7614], [7611, 7614], [7659, 7662], [7659, 7662], [7707, 7710], [7707, 7710], [7755, 7758], [7755, 7758], [7807, 7810], [7807, 7810], [7865, 7868], [7865, 7868], [7924, 7927], [7924, 7927], [8077, 8080], [8077, 8080], [12326, 12329], [12326, 12329], [1129, 1132], [1129, 1132], [1361, 1364], [1361, 1364], [1973, 1976], [1973, 1976], [2057, 2060], [2057, 2060], [2381, 2384], [2381, 2384], [2456, 2459], [2456, 2459], [2523, 2526], [2523, 2526], [3604, 3607], [3604, 3607], [4167, 4170], [4167, 4170], [5504, 5507], [5504, 5507], [7056, 7059], [7056, 7059], [7596, 7599], [7596, 7599], [7863, 7866], [7863, 7866], [2131, 2134], [2131, 2134], [2292, 2295], [2292, 2295], [1870, 1873], [1870, 1873], [1375, 1378], [1375, 1378], [923, 926], [923, 926], [813, 816], [813, 816], [1223, 1226], [1223, 1226], [1293, 1296], [1293, 1296], [2534, 2537], [2534, 2537], [3056, 3059], [3056, 3059], [2267, 2270], [2267, 2270], [2731, 2734], [2731, 2734], [1938, 1941], [1938, 1941], [2369, 2372], [2369, 2372], [2411, 2414], [2411, 2414], [2579, 2582], [2579, 2582], [2647, 2650], [2647, 2650], [2798, 2801], [2798, 2801], [4112, 4115], [4112, 4115], [4163, 4166], [4163, 4166], [4214, 4217], [4214, 4217], [4266, 4269], [4266, 4269], [4854, 4857], [4854, 4857], [4896, 4899], [4896, 4899], [4939, 4942], [4939, 4942], [4983, 4986], [4983, 4986], [9797, 9800], [9797, 9800], [9950, 9953], [9950, 9953], [10396, 10399], [10396, 10399], [10691, 10694], [10691, 10694], [10930, 10933], [10930, 10933], [11299, 11302], [11299, 11302], [1533, 1536], [1533, 1536], [1802, 1805], [1802, 1805], [2000, 2003], [2000, 2003], [2122, 2125], [2122, 2125], [2176, 2179], [2176, 2179], [2220, 2223], [2220, 2223], [2725, 2728], [2725, 2728], [3916, 3919], [3916, 3919], [5534, 5537], [5534, 5537], [6098, 6101], [6098, 6101], [7219, 7222], [7219, 7222], [7460, 7463], [7460, 7463], [7695, 7698], [7695, 7698], [7771, 7774], [7771, 7774], [8024, 8027], [8024, 8027], [8599, 8602], [8599, 8602], [8882, 8885], [8882, 8885], [9015, 9018], [9015, 9018], [9074, 9077], [9074, 9077], [10391, 10394], [10391, 10394], [11033, 11036], [11033, 11036], [11525, 11528], [11525, 11528], [2663, 2666], [2663, 2666], [2029, 2032], [2029, 2032], [2046, 2049], [2046, 2049], [4415, 4418], [4415, 4418], [4489, 4492], [4489, 4492], [4665, 4668], [4665, 4668], [4701, 4704], [4701, 4704], [4736, 4739], [4736, 4739], [4771, 4774], [4771, 4774], [4805, 4808], [4805, 4808], [4851, 4854], [4851, 4854], [4883, 4886], [4883, 4886], [4915, 4918], [4915, 4918], [4952, 4955], [4952, 4955], [5128, 5131], [5128, 5131], [5199, 5202], [5199, 5202], [369, 372], [369, 372], [2354, 2357], [2354, 2357], [3513, 3516], [3513, 3516], [261, 264], [261, 264], [873, 876], [873, 876], [895, 898], [895, 898], [368, 371], [368, 371], [1388, 1391], [1388, 1391], [1508, 1511], [1508, 1511], [2564, 2567], [2564, 2567], [3701, 3704], [3701, 3704], [5971, 5974], [5971, 5974], [6087, 6090], [6087, 6090], [6321, 6324], [6321, 6324], [6372, 6375], [6372, 6375], [7050, 7053], [7050, 7053], [7456, 7459], [7456, 7459], [324, 327], [324, 327], [1304, 1307], [1304, 1307], [1428, 1431], [1428, 1431], [2120, 2123], [2120, 2123], [2647, 2650], [2647, 2650], [2893, 2896], [2893, 2896], [3000, 3003], [3000, 3003], [3130, 3133], [3130, 3133], [895, 898], [895, 898], [1082, 1085], [1082, 1085], [2460, 2463], [2460, 2463], [4078, 4081], [4078, 4081], [4142, 4145], [4142, 4145], [6813, 6816], [6813, 6816], [7606, 7609], [7606, 7609], [7895, 7898], [7895, 7898], [2565, 2568], [2565, 2568], [259, 262], [259, 262], [638, 641], [638, 641], [669, 672], [669, 672], [1831, 1834], [1831, 1834], [3264, 3347], "// @ts-expect-error - depending on mongoose version, matchedCount/modifiedCount may exist", [3370, 3373], [3370, 3373], [1018, 1021], [1018, 1021], [1248, 1251], [1248, 1251], [1344, 1347], [1344, 1347], [1363, 1366], [1363, 1366], [1385, 1388], [1385, 1388], [3414, 3417], [3414, 3417], [4008, 4011], [4008, 4011], [5611, 5614], [5611, 5614], [2199, 2202], [2199, 2202], [3800, 3803], [3800, 3803], [4882, 4885], [4882, 4885], [5081, 5084], [5081, 5084], [5551, 5554], [5551, 5554], [5778, 5781], [5778, 5781], [6226, 6229], [6226, 6229], [6346, 6349], [6346, 6349], [6626, 6629], [6626, 6629], [6726, 6729], [6726, 6729], [6774, 6777], [6774, 6777], [6796, 6799], [6796, 6799], [7071, 7074], [7071, 7074], [3725, 3728], [3725, 3728], [778, 781], [778, 781], [2464, 2467], [2464, 2467], [587, 590], [587, 590], [1185, 1188], [1185, 1188], [606, 609], [606, 609], [1121, 1124], [1121, 1124], [2519, 2522], [2519, 2522], [3227, 3230], [3227, 3230], [5564, 5567], [5564, 5567], [5929, 5932], [5929, 5932], [791, 794], [791, 794], [1141, 1144], [1141, 1144], [1291, 1294], [1291, 1294], [1075, 1078], [1075, 1078], [5646, 5649], [5646, 5649], [5739, 5742], [5739, 5742], [5882, 5885], [5882, 5885], [5931, 5934], [5931, 5934], [6028, 6031], [6028, 6031], [3895, 3900], "for &quot;", [3895, 3900], "for &ldquo;", [3895, 3900], "for &#34;", [3895, 3900], "for &rdquo;", [3912, 3913], [3912, 3913], [3912, 3913], [3912, 3913], [11960, 12031], "\n                  Can&apos;t find what you're looking for?\n                ", [11960, 12031], "\n                  Can&lsquo;t find what you're looking for?\n                ", [11960, 12031], "\n                  Can&#39;t find what you're looking for?\n                ", [11960, 12031], "\n                  Can&rsquo;t find what you're looking for?\n                ", [11960, 12031], "\n                  Can't find what you&apos;re looking for?\n                ", [11960, 12031], "\n                  Can't find what you&lsquo;re looking for?\n                ", [11960, 12031], "\n                  Can't find what you&#39;re looking for?\n                ", [11960, 12031], "\n                  Can't find what you&rsquo;re looking for?\n                ", [923, 940], "[fetchFAQs, isOpen, product]", [1390, 1398], "[isOpen, loadFiles]", [5210, 5219], "[fetchOrders, filters]", [519, 527], "[fetchReviews, status]", [7108, 7153], "\n                No products found matching &quot;", [7108, 7153], "\n                No products found matching &ldquo;", [7108, 7153], "\n                No products found matching &#34;", [7108, 7153], "\n                No products found matching &rdquo;", [7165, 7181], "&quot;\n              ", [7165, 7181], "&ldquo;\n              ", [7165, 7181], "&#34;\n              ", [7165, 7181], "&rdquo;\n              ", [1189, 1197], "[fetchTestimonials, isOpen]", [1098, 1115], "[fetchVariations, isOpen, product]", [1729, 1765], "[cartItems, userId, flashSaleActive, fetchAvailableCoupons, fetchFeaturedCoupons]", [2012, 2084], "\n                You&apos;ve reached the end of our collection\n              ", [2012, 2084], "\n                You&lsquo;ve reached the end of our collection\n              ", [2012, 2084], "\n                You&#39;ve reached the end of our collection\n              ", [2012, 2084], "\n                You&rsquo;ve reached the end of our collection\n              ", [2971, 2974], [2971, 2974], [5345, 5412], "\n              We&apos;ll notify you when something happens\n            ", [5345, 5412], "\n              We&lsquo;ll notify you when something happens\n            ", [5345, 5412], "\n              We&#39;ll notify you when something happens\n            ", [5345, 5412], "\n              We&rsquo;ll notify you when something happens\n            ", [5385, 5607], "\n              We&apos;re committed to protecting the planet that provides our\n              ingredients. Our packaging is recyclable, and we partner with\n              suppliers who share our environmental values.\n            ", [5385, 5607], "\n              We&lsquo;re committed to protecting the planet that provides our\n              ingredients. Our packaging is recyclable, and we partner with\n              suppliers who share our environmental values.\n            ", [5385, 5607], "\n              We&#39;re committed to protecting the planet that provides our\n              ingredients. Our packaging is recyclable, and we partner with\n              suppliers who share our environmental values.\n            ", [5385, 5607], "\n              We&rsquo;re committed to protecting the planet that provides our\n              ingredients. Our packaging is recyclable, and we partner with\n              suppliers who share our environmental values.\n            ", [13587, 13855], "\n                We&apos;re committed to protecting the planet that provides our\n                ingredients. Our comprehensive sustainability program ensures\n                that every aspect of our business contributes to a healthier\n                world.\n              ", [13587, 13855], "\n                We&lsquo;re committed to protecting the planet that provides our\n                ingredients. Our comprehensive sustainability program ensures\n                that every aspect of our business contributes to a healthier\n                world.\n              ", [13587, 13855], "\n                We&#39;re committed to protecting the planet that provides our\n                ingredients. Our comprehensive sustainability program ensures\n                that every aspect of our business contributes to a healthier\n                world.\n              ", [13587, 13855], "\n                We&rsquo;re committed to protecting the planet that provides our\n                ingredients. Our comprehensive sustainability program ensures\n                that every aspect of our business contributes to a healthier\n                world.\n              ", [1809, 1950], "\n            Have questions or need assistance? We&apos;re here to help. Reach out to\n            us through any of the channels below.\n          ", [1809, 1950], "\n            Have questions or need assistance? We&lsquo;re here to help. Reach out to\n            us through any of the channels below.\n          ", [1809, 1950], "\n            Have questions or need assistance? We&#39;re here to help. Reach out to\n            us through any of the channels below.\n          ", [1809, 1950], "\n            Have questions or need assistance? We&rsquo;re here to help. Reach out to\n            us through any of the channels below.\n          ", [9206, 9338], "\n                      Thank you for your message! We&apos;ll get back to you within\n                      24 hours.\n                    ", [9206, 9338], "\n                      Thank you for your message! We&lsquo;ll get back to you within\n                      24 hours.\n                    ", [9206, 9338], "\n                      Thank you for your message! We&#39;ll get back to you within\n                      24 hours.\n                    ", [9206, 9338], "\n                      Thank you for your message! We&rsquo;ll get back to you within\n                      24 hours.\n                    ", [486, 489], [486, 489], [545, 548], [545, 548], [600, 603], [600, 603], [846, 848], "[fetchHomepageData]", [2083, 2086], [2083, 2086], [2127, 2130], [2127, 2130], [14336, 14339], [14336, 14339], [15864, 15867], [15864, 15867], [8751, 8788], "\n              Don&apos;t have an account?", [8751, 8788], "\n              Don&lsquo;t have an account?", [8751, 8788], "\n              Don&#39;t have an account?", [8751, 8788], "\n              Don&rsquo;t have an account?", [2532, 2563], "[fetchOrders, page, selectedFilter, session]", [2575, 2578], [2575, 2578], [4776, 4779], [4776, 4779], [6776, 6779], [6776, 6779], [7071, 7074], [7071, 7074], [16799, 16802], [16799, 16802], [28656, 28659], [28656, 28659], [511, 514], [511, 514], [1961, 1964], [1961, 1964], [6296, 6299], [6296, 6299], [6740, 6748], "[categories.length, sortBy]", [1472, 1488], "[status, router, session]", [2510, 2513], [2510, 2513], [3638, 3641], [3638, 3641], [11431, 11520], "\n              Looks like you haven&apos;t added anything yet. Let's change that!\n            ", [11431, 11520], "\n              Looks like you haven&lsquo;t added anything yet. Let's change that!\n            ", [11431, 11520], "\n              Looks like you haven&#39;t added anything yet. Let's change that!\n            ", [11431, 11520], "\n              Looks like you haven&rsquo;t added anything yet. Let's change that!\n            ", [11431, 11520], "\n              Looks like you haven't added anything yet. Let&apos;s change that!\n            ", [11431, 11520], "\n              Looks like you haven't added anything yet. Let&lsquo;s change that!\n            ", [11431, 11520], "\n              Looks like you haven't added anything yet. Let&#39;s change that!\n            ", [11431, 11520], "\n              Looks like you haven't added anything yet. Let&rsquo;s change that!\n            ", [908, 911], [908, 911], [3137, 3140], [3137, 3140], [773, 776], [773, 776], [1770, 1773], [1770, 1773], [2265, 2268], [2265, 2268], [2393, 2396], [2393, 2396], [4290, 4293], [4290, 4293], [4544, 4547], [4544, 4547], [373, 376], [373, 376], [580, 591], "[fetchFAQs, productId]", [3188, 3229], "Have a question that&apos;s not answered here?", [3188, 3229], "Have a question that&lsquo;s not answered here?", [3188, 3229], "Have a question that&#39;s not answered here?", [3188, 3229], "Have a question that&rsquo;s not answered here?", [2460, 2474], "[handleKeyDown, showLightbox]", [742, 753], "[fetchReviews, productId]", [862, 873], "[fetchVariations, productId]", [2138, 2150], "[onVariationChange, selectedVariations, variations]", [5192, 5218], "\n                        &quot;", [5192, 5218], "\n                        &ldquo;", [5192, 5218], "\n                        &#34;", [5192, 5218], "\n                        &rdquo;", [5239, 5263], "&quot;\n                      ", [5239, 5263], "&ldquo;\n                      ", [5239, 5263], "&#34;\n                      ", [5239, 5263], "&rdquo;\n                      ", [2540, 2543], [2540, 2543], [12544, 12562], "[state.isHydrated, state.items]", [283, 286], [283, 286], [3309, 3328], "[fetchNotifications, prevUnreadCount, session?.user?.id]", [2646, 2649], [2646, 2649], [3094, 3097], [3094, 3097], [4853, 4856], [4853, 4856], [1399, 1402], [1399, 1402], [1957, 1960], [1957, 1960], [2263, 2266], [2263, 2266], [3663, 3666], [3663, 3666], [3706, 3709], [3706, 3709], [5360, 5363], [5360, 5363], [174, 177], [174, 177], [318, 321], [318, 321], [441, 444], [441, 444], [859, 862], [859, 862], [2008, 2011], [2008, 2011], [2218, 2221], [2218, 2221], [2618, 2621], [2618, 2621], [3984, 3987], [3984, 3987], [4679, 4682], [4679, 4682], [8030, 8033], [8030, 8033], [9205, 9208], [9205, 9208], [190, 193], [190, 193], [1453, 1456], [1453, 1456], [2360, 2363], [2360, 2363], [2483, 2486], [2483, 2486], [2598, 2601], [2598, 2601], [2714, 2717], [2714, 2717], [2917, 2920], [2917, 2920], [3177, 3180], [3177, 3180], [3463, 3466], [3463, 3466], [3719, 3722], [3719, 3722], [3972, 3975], [3972, 3975], [4258, 4261], [4258, 4261], [4525, 4528], [4525, 4528], [4818, 4821], [4818, 4821], [5252, 5255], [5252, 5255], [5532, 5535], [5532, 5535], [5776, 5779], [5776, 5779], [6039, 6042], [6039, 6042], [6595, 6598], [6595, 6598], [6737, 6740], [6737, 6740], [7178, 7181], [7178, 7181], [7363, 7366], [7363, 7366], [7511, 7514], [7511, 7514], [7698, 7701], [7698, 7701], [9696, 9699], [9696, 9699], [1023, 1026], [1023, 1026], [1081, 1084], [1081, 1084], [1223, 1226], [1223, 1226], [1373, 1376], [1373, 1376], [1485, 1488], [1485, 1488], [2142, 2145], [2142, 2145], [4019, 4022], [4019, 4022], [4481, 4484], [4481, 4484], [5274, 5277], [5274, 5277], [5318, 5321], [5318, 5321], [5418, 5421], [5418, 5421], [5464, 5467], [5464, 5467], [7413, 7416], [7413, 7416], [7460, 7463], [7460, 7463], [7750, 7753], [7750, 7753], [8057, 8060], [8057, 8060], [8079, 8082], [8079, 8082], [8101, 8104], [8101, 8104], [8509, 8512], [8509, 8512], [8753, 8756], [8753, 8756], [8860, 8863], [8860, 8863], [67, 70], [67, 70], [952, 955], [952, 955], [2299, 2302], [2299, 2302], [2876, 2879], [2876, 2879], [3035, 3038], [3035, 3038], [4225, 4228], [4225, 4228], [505, 508], [505, 508], [2919, 2922], [2919, 2922], [140, 143], [140, 143], [366, 369], [366, 369], [1004, 1007], [1004, 1007], [1268, 1271], [1268, 1271], [1549, 1552], [1549, 1552], [1782, 1785], [1782, 1785], [777, 780], [777, 780], [6399, 6402], [6399, 6402], [10469, 10554], "\n                  We&apos;ll notify you when something important happens\n                ", [10469, 10554], "\n                  We&lsquo;ll notify you when something important happens\n                ", [10469, 10554], "\n                  We&#39;ll notify you when something important happens\n                ", [10469, 10554], "\n                  We&rsquo;ll notify you when something important happens\n                "]